<?php
/**
 * WebsocketClient.php
 *
 * User: Dican
 * Date: 2022/7/15
 * Email: <<EMAIL>>
 */

namespace websocketService;


use WebSocket\Client;

class WebsocketClient
{
    /**
     * @var Client
     */
    protected $client;

    public function __construct($local = false)
    {
        if ($local) {
            $url = 'ws://127.0.0.1:8085';
            $this->client = new Client($url);
        } else {
            $url = 'wss://127.0.0.1:8085';
            $this->client = new Client($url, [
                'context' => stream_context_create([
                    'ssl' => [
                        "verify_peer" => true,					// 是否需要校验对端证书
                        "verify_peer_name" => false,			// 是否校验peer_name，默认为true，如果为true，需要在客户端和服务端同时设置peer_name参数
                        "allow_self_signed" => true,			// 使用自签名证书时开启
                        "cafile" => '/data/conf/nginx/1_shiyue.com_bundle.crt', 	// ca文件
                    ]
                ])
            ]);
        }
    }

    public function text(string $text)
    {
        $this->client->text($text);
    }

    public function close()
    {
        $this->client->close();
    }
}
