<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWarningRuleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('warning_rule', function (Blueprint $table) {
            $table->bigIncrements('rule_id');
            $table->unsignedInteger('warning_id')->comment('预警id');
            $table->unsignedInteger('schedule_time')->comment('频率/计划时间');
            $table->unsignedInteger('index')->comment('指标');
            $table->string('operator', '4')->comment('运算符');
            $table->decimal('value', 10, 4)->default(0)->comment('值');
            $table->timestamps();
            $table->index('warning_id');
        });
        \DB::connection('apm')->statement("ALTER TABLE `warning_rule` comment '预警触发条件'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('warning_rule');
    }
}
