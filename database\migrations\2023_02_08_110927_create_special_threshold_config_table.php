<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSpecialThresholdConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('special_threshold_config', function (Blueprint $table) {
            $table->bigIncrements('threshold_config_id')->comment('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->string('name', 64)->default('')->comment('名称');
            $table->json('config')->comment('配置内容');
            $table->unsignedTinyInteger('is_current_config')->default(0)
                ->comment('是否为当前配置(1是,0不是)');
            $table->timestamps();

            $table->unique(['developer_app_id', 'name'], 'uni_developer_app_id_name', 'BTREE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('special_threshold_config');
    }
}
