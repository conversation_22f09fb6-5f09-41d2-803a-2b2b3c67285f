<?php

/**
 * 线上性能全局配置校验类
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;

/**
 * @method static ConfigValidation build()
 */
class ConfigValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): ConfigValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 配置信息内容校验
     *
     * @return $this
     */
    public function config(): ConfigValidation
    {
        $this->rules['config'] = 'required|string|json';
        return $this;
    }
}
