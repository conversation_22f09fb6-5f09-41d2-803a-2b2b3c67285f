{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2.5|^8.0", "alchemy/zippy": "^1.0", "fideloper/proxy": "^4.4", "laravel/framework": "^6.20.26", "laravel/tinker": "^2.5", "qcloud/cos-sdk-v5": "^2.6", "spatie/laravel-permission": "5.3.2", "swoole/ide-helper": "^4.8", "textalk/websocket": "^1.5"}, "require-dev": {"barryvdh/laravel-ide-helper": "2.6.4", "doctrine/dbal": "2.13.9", "facade/ignition": "^1.16.15", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^8.5.8|^9.3.3", "qcloudsms/qcloudsms_php": "^0.1.4"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "websocketService\\": "websocketService"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}