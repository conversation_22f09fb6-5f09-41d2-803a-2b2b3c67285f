<?php
/**
 * BaseModel.php
 *
 * User: Dican
 * Date: 2022/8/15
 * Email: <<EMAIL>>
 */

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\BaseModel
 *
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\BaseModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\BaseModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\BaseModel query()
 * @mixin \Eloquent
 */
class BaseModel extends Model
{
    public $connection = "tool";
    protected $guarded = [];

    /**
     * 操作系统中文
     *
     * @var array
     */
    const OS_TYPE_TEXT = [
        1 => '安卓',
        2 => '苹果',
        3 => 'PC',
        4 => '小程序',
    ];
}
