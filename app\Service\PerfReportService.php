<?php

/**
 * 通用性能检测服务类
 * @desc 通用性能检测服务类
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use App\Model\Perf\Bookmark;
use App\Model\Perf\BookmarkItem;
use App\Model\Perf\Report;
use Illuminate\Support\Facades\Auth;

class PerfReportService
{
    private $report;

    public function __construct(Report $report)
    {
        $this->report = $report;
    }

    /**
     * 保存报表文件并返回地址
     * @param $report
     * @param string $reportFormat
     * @return string|string[]
     */
    public function storeReport($report, string $reportFormat)
    {
        // 将接收到的json存储到storage/app/public/upload/perf-report目录下
        $reportPath = "upload/perf-report/" . date("Y-m");
        // json文件命名应该唯一
        // 命名格式为 date("d")."_" . md5($input['report']->getClientOriginalName().now())
        $reportName = date("d") . "_" . md5($report->getClientOriginalName() . now());
        $reportUrl = $reportPath . "/" . $reportName . "." . $reportFormat;
        $report->storeAs(
            $reportPath,
            $reportName . "." . $reportFormat,
            'public'
        );
        //访问的地址为
        //域名/storage/upload/perf-report/date("Y-m")/date("d")."_" .md5($report->getClientOriginalName() . now()).json
        return ["storage/" . $reportUrl, $reportUrl];
    }

    /**
     * 筛选后得出的列表(分页)和总条数
     *
     * @param int $perPage
     * @param int $page
     * @param array $input
     * @return array
     */
    public function getReportList(int $perPage, int $page, array $input): array
    {
        //根据developer_app_id进行筛选并将数据根据创建时间进行排序
        $query = $this->report->orderBy('created_at', 'desc');
        //获取筛选条件
        $title = $input['title'] ?? null;
        $appName = $input['app_name'] ?? null;
        $deviceModel = $input['device_model'] ?? null;
        $packageName = $input['package_name'] ?? null;
        $reportIds = $input['report_ids'] ?? null;
        $system = $input['system'] ?? null;
        //处理分页
        $list = $this->paging((clone $query), $page, $perPage, [$title, $appName, $deviceModel, $packageName, $reportIds, $system]);
        //统计总条数
        $total = (clone $query)->when($title, function ($query, $title) {
            return $query->where('title', 'like', "%{$title}%");
        })
            ->when($appName, function ($query, $appName) {
                return $query->where('app_name', 'like', "%{$appName}%");
            })
            ->when($deviceModel, function ($query, $deviceModel) {
                return $query->where('device_model', 'like', "%{$deviceModel}%");
            })
            ->when($system, function ($query, $system) {
                return $query->where('system', $system);
            })
            ->when($packageName, function ($query, $packageName) {
                $query->where(function ($query) use ($packageName) {
                    foreach ($packageName as $value) {
                        $query->orWhere('package_name', 'like', "{$value}%");
                    }
                });
                return $query;
            })
            ->when($reportIds, function ($query, $reportIds) {
                return $query->whereIn('report_id', $reportIds);
            })->count();
        return [$list, $total];
    }

    /**
     * 处理分页
     * @param $query
     * @param int $page
     * @param int $perPage
     * @param $params
     * @return array
     */
    private function paging($query, int $page, int $perPage, $params): array
    {
        list($title, $appName, $deviceModel, $packageName, $reportIds, $system) = $params;
        //offset 是数据偏移量 (页数-1) × 条数
        $list = $query->offset((($page - 1) * $perPage))->limit($perPage)
            ->when($title, function ($query, $title) {
                return $query->where('title', 'like', "%{$title}%");
            })
            ->when($appName, function ($query, $appName) {
                return $query->where('app_name', 'like', "%{$appName}%");
            })
            ->when($deviceModel, function ($query, $deviceModel) {
                return $query->where('device_model', 'like', "%{$deviceModel}%");
            })
            ->when($system, function ($query, $system) {
                return $query->where('system', $system);
            })
            ->when($packageName, function ($query, $packageName) {
                $query->where(function ($query) use ($packageName) {
                    foreach ($packageName as $value) {
                        $query->orWhere('package_name', 'like', "{$value}%");
                    }
                });
                return $query;
            })
            ->when($reportIds, function ($query, $reportIds) {
                return $query->whereIn('report_id', $reportIds);
            })
            ->get()
            ->toArray();

        //处理是否收藏
        $isBookmarkList = [];
        if (!empty($list)) {
            $bookmarkItemList = BookmarkItem::query()
                ->whereIn('report_id', array_column($list, 'report_id'))
                ->get(['bookmark_id', 'report_id'])
                ->toArray();
            $bookmarkList = Bookmark::query()
                ->where('user_id', Auth::user()->user_id)
                ->whereIn('id', array_column($bookmarkItemList, 'bookmark_id'))
                ->pluck('id')
                ->toArray();
            foreach ($bookmarkItemList as $value) {
                in_array($value['bookmark_id'], $bookmarkList) && $isBookmarkList[$value['report_id']] = 1;
            }
        }

        //处理图片
        foreach ($list as &$item) {
            if (empty($item['app_icon'])) {
                $item['app_icon'] = "/images/{$item['system']}.png";
            }
            $item['is_bookmark'] = isset($isBookmarkList[$item['report_id']]);
            // 判断是否有device_data字段是否不为空，若不为空，则将device_data字段转换为数组
            if (!empty($item['device_data'])) {
                $item['device_data'] = json_decode($item['device_data'], true);
            }
        }

        return $list;
    }
}
