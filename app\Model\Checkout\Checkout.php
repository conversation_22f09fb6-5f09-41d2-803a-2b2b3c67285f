<?php
/**
 * Checkout.php
 *
 * User: Dican
 * Date: 2022/8/15
 * Email: <<EMAIL>>
 */

namespace App\Model\Checkout;



use App\Model\BaseModel;
use App\Model\ModelTrait;

/**
 * App\Model\Checkout\Checkout
 *
 * @property int $checkout_id
 * @property bool $platform_type 平台类型;1为iOS,2为安卓国内,3为安卓谷歌,4为安卓通用
 * @property string $name 文件名
 * @property string $title 标题
 * @property bool $type 类型;1为包,2为json
 * @property string $url 下载地址
 * @property int $size 大小
 * @property bool $status 状态;1为已就绪,2为进行中,3为检测完成,4为检测失败
 * @property int $estimated_time 预计完成时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereCheckoutId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereEstimatedTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout wherePlatformType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereUrl($value)
 * @mixin \Eloquent
 * @property string|null $checkout_report 检测报告
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereCheckoutReport($value)
 * @property string|null $file_type 文件类型
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereFileType($value)
 * @property string|null $release_store 渠道
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Checkout\Checkout whereReleaseStore($value)
 */
class Checkout extends BaseModel
{
    use ModelTrait;

    protected $table = 'checkout';
    protected $primaryKey = 'checkout_id';
    protected $fillable = [
        'platform_type', 'name', 'title', 'type', 'url', 'size', 'status', 'estimated_time', 'status', 'checkout_report',
        'file_type', 'release_store'
    ];

    protected $casts = [
        'release_store' => 'array',
    ];

    public $validateRule = [
        'platform_type' => 'required|integer',
        'name' => 'required|string',
        'title' => 'required|string',
        'type' => 'required|integer',
        'url' => 'required|string',
        'size' => 'required|integer',
        'file_type' => 'nullable|string',
    ];
    public $uniqueKey = ['title'];

    //平台类型
    const PLATFORM_IOS = 1;
    const PLATFORM_INLAND = 2;
    const PLATFORM_GOOGLE = 3;
    const PLATFORM_COMMON = 4;
    const PLATFORM_TYPE = [
        self::PLATFORM_IOS => 'ios',
        self::PLATFORM_INLAND => 'fuse',
        self::PLATFORM_GOOGLE => 'google',
        self::PLATFORM_COMMON => 'common',
    ];

    //类型
    const TYPE_PACKAGE = 1;
    const TYPE_JSON = 2;
    const TYPE = [
        self::TYPE_PACKAGE,
        self::TYPE_JSON,
    ];

    //状态
    const STATUS_BEGIN = 1;
    const STATUS_RUNNING = 2;
    const STATUS_FINISHED = 3;
    const STATUS_FAIL = 4;
}
