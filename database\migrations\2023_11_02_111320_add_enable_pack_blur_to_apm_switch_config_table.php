<?php

/**
 * PerfMate 增加启用包名模糊匹配的迁移文件
 * @desc PerfMate 增加启用包名模糊匹配的迁移文件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/02
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEnablePackBlurToApmSwitchConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->table('apm_switch_config', function (Blueprint $table) {
            $table->unsignedTinyInteger('enable_pack_blur')->default(0)->comment('启用包名模糊匹配，1:开启、0:关闭');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->table('apm_switch_config', function (Blueprint $table) {
            $table->dropColumn('enable_pack_blur');
        });
    }
}
