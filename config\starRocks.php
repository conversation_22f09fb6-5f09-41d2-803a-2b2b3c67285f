<?php

/**
 * starRocks数据仓库相关配置
 */

return [
    //数据仓地址
    'starRocks_host' => env('STAR_ROCKS_HOST', ''),
    //数据仓的用户名
    'starRocks_username' => env('STAR_ROCKS_USERNAME', ''),
    //数据仓的密码
    'starRocks_password' => env('STAR_ROCKS_PASSWORD', ''),
    //数据仓的数据库
    'starRocks_database' => env('STAR_ROCKS_DATABASE', 'sdk_log'),
    //数据仓的端口
    'starRocks_port' => env('STAR_ROCKS_PORT', 9030),
];