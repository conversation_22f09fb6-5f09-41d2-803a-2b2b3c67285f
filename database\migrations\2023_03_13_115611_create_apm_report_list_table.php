<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApmReportListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('apm_report_list', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedBigInteger('app_total_memory')->default(0)->comment('应用可用总内存');
            $table->string('package_name')->default('')->comment('应用包名');
            $table->string('app_name')->default('')->comment('应用名称');
            $table->string('app_version_name')->default('')->comment('商店版本号');
            $table->string('app_version_code')->default('')->comment('应用版本号');
            $table->string('game_version_code')->default('')->comment('游戏版本号');
            $table->string('os_version_code')->default('')->comment('系统版本号');
            $table->string('opengl_es_version')->default('')->comment('OpenGL ES版本');
            $table->string('dev_str')->default('')->comment('设备ID');
            $table->string('sdk_ver')->default('')->comment('sdk版本');
            $table->timestamps();

            $table->index('dev_str');
            $table->index('developer_app_id');
            $table->index('created_at');
        });
        \DB::connection('apm')->statement("ALTER TABLE `apm_report_list` comment 'apm-性能报告列表数据'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('apm_report_list');
    }
}
