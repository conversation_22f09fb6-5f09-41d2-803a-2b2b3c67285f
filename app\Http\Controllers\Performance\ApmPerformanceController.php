<?php

/**
 * Created by phpstorm
 * User: liuxr
 * Date: 2023/3/3
 * Time: 10:43
 * TODO:线上玩家性能数据
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\PerformanceValidation;
use App\Model\Apm\StarRocks\MysqlApmDeviceList;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\PerformanceStatData;
use App\Service\Apm\Chart\BaseChart;
use App\Service\Apm\Performance\ApmDailyReportList;
use App\Service\Apm\Performance\ApmDeviceDailyPerformanceList;
use App\Service\Apm\Performance\ApmExceptionReportList;
use App\Service\Apm\Performance\ApmExceptionSearch;
use App\Service\Apm\Performance\ApmHomeSearch;
use App\Service\Apm\Performance\ApmInfo;
use App\Service\Apm\Performance\ApmInfoTagStat;
use App\Service\Apm\Performance\ApmRecentDeviceInfo;
use App\Service\Apm\Performance\ApmRecentPlayerMatchNum;
use App\Service\Apm\Performance\ApmRefreshInfo;
use App\Service\Apm\Performance\ApmScoreBelowEightyReportList;
use App\Service\Apm\Performance\ApmSearchList;
use App\Service\Apm\Performance\ApmSingleDeviceList;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApmPerformanceController extends Controller
{
    /**
     *
     * 单设备最新数据记录列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3758
     * @param Request $request
     * @return JsonResponse
     */
    public function singleDeviceList(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->devStrNullable()->gameVersionCode()->uid()->osType()->tag()
            ->sortFieldNullable()->sortTypeNullable()->devBrand()->devModel()
            ->page()->limit()->startTime()->endTime()->isSimulator()->deviceTier()->validate();

        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmSingleDeviceList($params))->getList());
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据单设备列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取设备近30天数据总和
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3761
     * @param Request $request
     * @return JsonResponse
     */
    public function recentDeviceInfo(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->devStr()->startTime()->endTime()->validate();

        try {
            $data = (new ApmRecentDeviceInfo($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取设备总数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取近30天设备每日对局数
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3763
     * @param Request $request
     * @return JsonResponse
     */
    public function recentPlayerMatchNum(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->devStr()->startTime()->endTime()->validate();
        try {
            $list = (new ApmRecentPlayerMatchNum($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, compact('list'));
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取近30天玩家玩家对局数接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取某日设备的性能数值列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3767
     * @param Request $request
     * @return JsonResponse
     */
    public function deviceDailyPerformanceList(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->devStr()->dateNullable()->dataOrdinate()->validate();

        try {
            $list = (new ApmDeviceDailyPerformanceList($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, compact('list'));
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取某日设备的某一项性能数值接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取具体设备某一天的数据报告列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3768
     * @param Request $request
     * @return JsonResponse
     */
    public function deviceDailyReportList(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->devStr()->date()
            ->sortFieldNullable()->sortTypeNullable()
            ->page()->limit()->validate();

        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmDailyReportList($params))->getList());
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取具体设备某一天的数据报告列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取最近10条评分低于80分的报告列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3769
     * @param Request $request
     * @return JsonResponse
     */
    public function scoreBelowEightyReportList(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->sortFieldNullable()->sortTypeNullable()->startTime()
            ->endTime()->osType()->isSimulator()->deviceTier()
            ->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmScoreBelowEightyReportList($params))->getList());
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取最近10条评分低于80分的报告列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 性能参数详情接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3463
     * @param Request $request
     * @return JsonResponse
     */
    public function info(Request $request): JsonResponse
    {
        //请求参数校验
        $params = PerformanceValidation::build()->developerAppId()->reportId()->validate();

        try {
            $data = (new ApmInfo($params))->get();
            //返回
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据详情接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 标签下拉选项数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3389
     * @param Request $request
     * @return JsonResponse
     */
    public function tag(Request $request): JsonResponse
    {
        //请求参数校验
        $params = PerformanceValidation::build()->reportId()->validate();

        try {
            //获取数据
            $res = PerformanceStatData::query()
                ->select(['tags_info'])
                ->where('session_id', $params['report_id'])
                ->firstFromSR();
            //将字符串转为数组
            $tag = json_decode($res['tags_info'] ?? '[]', true);
            // 把标签中存在 \\/ 转义的字符转为 /
            foreach ($tag as $key => $value) {
                $tag[$key] = str_replace('\\/', '/', $value);
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $tag);
        } catch (Exception $e) {
            \Log::error('获取标签下拉选项数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 标签统计数据
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15527
     * @return JsonResponse
     */
    public function getTagStat(): JsonResponse
    {
        //请求参数校验
        $params = PerformanceValidation::build()->reportId()->validate();

        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmInfoTagStat($params))->getData());
        } catch (Exception $e) {
            \Log::error('获取标签统计数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取游戏版本号列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3476
     * @param Request $request
     * @return JsonResponse
     */
    public function gameVersion(Request $request): JsonResponse
    {
        //请求参数校验
        $params = PerformanceValidation::build()->developerAppId()->validate();

        try {
            //获取数据
            $res = MysqlApmReportList::query()
                ->selectRaw('app_version_name as game_version_code')
                ->where('developer_app_id', $params['developer_app_id'])
                ->where('app_version_name', '<>', '')
                ->groupBy('app_version_name')
                ->orderBy('app_version_name', 'desc')
                ->getFromSR();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, array_column($res, 'game_version_code'));
        } catch (Exception $e) {
            \Log::error('获取游戏版本号接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取设备品牌列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3476
     * @param Request $request
     * @return JsonResponse
     */
    public function devBrand(Request $request): JsonResponse
    {
        //请求参数校验
        $params = PerformanceValidation::build()->developerAppId()->validate();

        try {
            //获取数据
            $res = MysqlApmDeviceList::query()
                ->select(['dev_brand'])
                ->where('developer_app_id', $params['developer_app_id'])
                ->where('dev_brand', '<>', '')
                ->groupBy('dev_brand')
                ->orderBy('dev_brand', 'desc')
                ->getFromSR();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, array_column($res, 'dev_brand'));
        } catch (Exception $e) {
            \Log::error('获取设备品牌接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取报表数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function chart(Request $request): JsonResponse
    {
        $params = PerformanceValidation::build()->reportId()->type()->tag()->validate();

        try {
            ini_set("memory_limit", '2560M');
            $tag = isset($params['tag']) ? explode(',', $params['tag']) : [];
            $data = BaseChart::createChart($params['type'], $params['report_id'], $tag)->getData();
            //返回
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据详情报表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取异常数据列表
     *
     * @doc https://test-tool-manager.shiyue.com/performance/apm/single/exception/list
     * @param Request $request
     * @return JsonResponse
     */
    public function exceptionList(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()->deviceTier()
            ->sortFieldNullable()->sortTypeNullable()->isSimulator()->page()->limit()
            ->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmExceptionReportList($params))->getList());
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取异常报告列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取异常数据筛选
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14209
     * @param Request $request
     * @return JsonResponse
     */
    public function exceptionSearch(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()->deviceTier()
            ->sortFieldNullable()->sortTypeNullable()->isSimulator()
            ->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmExceptionSearch($params))->getSearch());
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取异常报告筛选接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取首页数据筛选
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14210
     * @param Request $request
     * @return JsonResponse
     */
    public function homeSearch(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()->deviceTier()->isSimulator()
            ->validate();
        try {
            $result = (new ApmHomeSearch($params))->getSearch();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $result);
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取首页筛选接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取首页数据最新刷新信息
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14247
     * @param Request $request
     * @return JsonResponse
     */
    public function homeRefresh(Request $request): JsonResponse
    {
        // 请求参数校验
        $params = PerformanceValidation::build()
            ->developerAppId()->validate();
        try {
            $result = (new ApmRefreshInfo($params))->getInfo();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $result);
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据获取首页数据最新刷新信息接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     *
     * 搜索列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3758
     * @param Request $request
     * @return JsonResponse
     */
    public function searchList(Request $request): JsonResponse
    {
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new ApmSearchList(request()->all()))->getList());
        } catch (Exception $e) {
            \Log::error('线上玩家性能数据搜索列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
