<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePerfBookMarkTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('perf_bookmark', function (Blueprint $table) {
            $table->increments('id')->comment('ID');
            $table->string('title', 255)->default('')->comment('收藏夹名称');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->timestamps();

            $table->index('user_id');
        });
        \DB::connection('tool')->statement("ALTER TABLE `perf_bookmark` comment 'apm-线下性能收藏夹表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('perf_bookmark');
    }
}
