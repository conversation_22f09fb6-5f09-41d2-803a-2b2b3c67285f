<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddReleaseStoreToCheckoutTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('checkout', function (Blueprint $table) {
            $table->string('release_store', 32)->after('file_type')->nullable()->comment('渠道');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('checkout', function (Blueprint $table) {
            $table->dropColumn('release_store');
        });
    }
}
