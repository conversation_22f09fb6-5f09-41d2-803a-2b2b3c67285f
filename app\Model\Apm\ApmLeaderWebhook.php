<?php

/**
 * 负责人巡检配置表模型
 * @desc 负责人巡检配置表模型
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/04/14
 */

namespace App\Model\Apm;

use Illuminate\Database\Eloquent\Model;

class ApmLeaderWebhook extends Model
{
    public $connection = "apm";

    protected $table = 'apm_leader_webhook';

    protected $primaryKey = 'id';

    protected $fillable = [
        'developer_app_id',
        'frequency',
        'range',
        'webhook_urls',
        'status',
        'operator'
    ];

    protected $casts = [
        'webhook_urls' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
