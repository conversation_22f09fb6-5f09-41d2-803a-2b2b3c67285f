<?php

namespace websocketService\dbService;

use Redis;


class RedisService
{
    /**
     * @var Redis
     */
    private static $redis = null;

    private function __construct()
    {
    }

    /**取得实例
     * @param $cfg
     * @return Redis
     */
    public static function getInstance($cfg)
    {
        if (self::$redis == null) {
            self::$redis = new Redis();
        }
        //连接
        self::connect($cfg);
        return self::$redis;
    }

    /**连接
     * @param $cfg
     */
    public static function connect($cfg)
    {
        self::$redis->connect(
            $cfg['redis']['host']
        );
        if ($cfg['redis']['password'] != 'null') {
            self::$redis->auth($cfg['redis']['password']);
        }
        if ($cfg['redis']['database'] != 0) {
            self::$redis->select($cfg['redis']['database']);
        }
    }

    /**
     * 关闭连接
     */
    public static function close()
    {
        if (self::$redis != null) {
            self::$redis->close();
        }
    }

    /**
     * 摧毁本实例
     */
    public static function destroy()
    {
        self::destroy();
    }

    private function __clone()
    {
        // TODO: Implement __clone() method.
    }

}
