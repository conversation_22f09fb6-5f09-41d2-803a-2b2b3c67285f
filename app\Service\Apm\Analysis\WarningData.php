<?php

/**
 * 获取预警数据
 * @desc 获取预警数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Analysis;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Model\Apm\StarRocks\PerformanceTagScoreData;
use App\Service\Apm\Performance\ApmTrait;
use Carbon\Carbon;

class WarningData
{
    use ApmTrait;

    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        $selectRaw = <<<COLUMNS
count({$this->performance_score_data_table}.session_id) as score_num,
sum(case when {$this->performance_score_data_table}.all_score < {$this->standardScore} then 1 else 0 end) as low_score_num,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness_score,
round((sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_stat_data_table}.session_id)) / (1024*1024), 2) as avg_memory_score,
round((sum({$this->performance_stat_data_table}.down_traffic_10 + {$this->performance_stat_data_table}.up_traffic_10) / count({$this->performance_score_data_table}.session_id)) / 1024, 2) as avg_network_traffic,
round(sum(case when {$this->performance_stat_data_table}.sum_network_delay > 0 then {$this->performance_stat_data_table}.sum_network_delay / {$this->performance_stat_data_table}.num else 0 end) / sum(case when {$this->performance_stat_data_table}.sum_network_delay > 0 then 1 else 0 end), 2) as avg_network_delay,
round(sum({$this->performance_stat_data_table}.sum_battery_power) / sum({$this->performance_stat_data_table}.num), 2) as battery_power
COLUMNS;

        $data = PerformanceScoreData::query() //查询性能分数表
            ->selectRaw($selectRaw) //计算平均分
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [Carbon::yesterday()->startOfDay()->toDateTimeString(), Carbon::yesterday()->endOfDay()->toDateTimeString()]) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type'])
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->firstFromSR();
        // 增加战斗场景的卡顿率
        $data['battle_stutter'] = $this->getBattleStutter();
        // 返回数据
        return $data;
    }

    /**
     * 获取标签数据
     *
     * @return string
     */
    private function getBattleStutter()
    {
        $selectRaw = <<<EXPRESSION
{$this->performance_tag_score_data_table}.tag,
round(round(sum({$this->performance_tag_stat_data_table}.sum_jank_time) / sum({$this->performance_tag_stat_data_table}.sum_frame_times_time), 4) * 100, 2) as stutter
EXPRESSION;

        $data = PerformanceTagScoreData::query() //查询标签评分表
            ->selectRaw($selectRaw)
            ->join($this->performance_stat_data_table, "{$this->performance_tag_score_data_table}.session_id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->mysql_apm_report_list_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_tag_score_data_table}.session_id") //关联报告表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->join($this->performance_tag_stat_data_table, function ($json) { //关联标签统计表
                $json->on("{$this->performance_tag_stat_data_table}.session_id", '=', "{$this->performance_tag_score_data_table}.session_id") //通过session_id关联
                    ->on("{$this->performance_tag_stat_data_table}.tag", '=', "{$this->performance_tag_score_data_table}.tag"); //通过标签关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [Carbon::yesterday()->startOfDay()->toDateTimeString(), Carbon::yesterday()->endOfDay()->toDateTimeString()]) //过滤掉不在时间范围内的数据
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->where("{$this->performance_tag_score_data_table}.tag", '战斗')
            ->groupBy("{$this->performance_tag_score_data_table}.tag") //按标签分组
            ->firstFromSR();
        // 返回数据
        return $data['stutter'] ?? '0.00';
    }
}
