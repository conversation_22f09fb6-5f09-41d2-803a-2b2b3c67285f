<?php

namespace App;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use Notifiable;
    use HasRoles;

    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    //在职状态
    public static $statusList = [
        self::STATUS_ENABLE => '可用',
        self::STATUS_DISABLE => '不可用',
    ];

    public static $statusClass = [
        self::STATUS_ENABLE => 'success',
        self::STATUS_DISABLE => 'danger',
    ];

    protected $primaryKey = 'user_id';

    protected $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];
}
