<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAbiIpNetworkTypeChannelToApmReportListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->table('apm_report_list', function (Blueprint $table) {
            $table->string('abi', 255)->default('')->comment('运行架构');
            $table->string('ip', 30)->default('')->comment('IP地址');
            $table->unsignedTinyInteger('network_type')->default(0)->comment('网络类型');
            $table->string('channel', 255)->default('')->comment('渠道');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
       Schema::connection('apm')->table('apm_report_list', function (Blueprint $table) {
            $table->dropColumn('abi');
            $table->dropColumn('ip');
            $table->dropColumn('network_type');
            $table->dropColumn('channel');
        });
    }
}
