<?php

/**
 * 删除线上性能导出数据定时脚本
 * @desc 删除线上性能导出数据定时脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/11/26
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DeleteExportFileCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:export:file';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除线上性能导出数据定时脚本';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            // 打印日志
            Log::info('开始删除线上性能导出数据，当前时间：' . now()->toDateTimeString());

            $directory = public_path('export');
            $files = scandir($directory); // 获取目录下的所有文件

            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    $filePath = $directory . '/' . $file;
                    $fileCreationTime = filectime($filePath); // 获取文件创建时间
                    // 判断文件是否超过1天
                    if (time() - $fileCreationTime > 86400) { // 86400 秒 = 1 天
                        unlink($filePath); // 删除文件
                        Log::info("删除文件：{$filePath}");
                    }
                }
            }
            // 打印日志
            Log::info("结束删除线上性能导出数据，当前时间：" . now()->toDateTimeString());
        } catch (\Exception $e) {
            Log::error("删除线上性能导出数据失败，{$e->getTraceAsString()}");
        }
    }
}
