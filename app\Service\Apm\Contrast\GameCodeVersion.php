<?php

/**
 * 版本对比分析
 * @desc 版本对比分析
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Contrast;

use App\Model\Apm\StarRocks\MysqlApmReportList;

class GameCodeVersion extends BaseContrast
{
    /**
     * 获取对比列表
     *
     * @return array
     */
    public function getList()
    {
        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_report_list_table}.app_version_name as game_version_code,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
count({$this->mysql_apm_report_list_table}.id) as num,
round(sum({$this->performance_stat_data_table}.sum_cpu_usage) / sum({$this->performance_stat_data_table}.num), 2) as cpu,
round(sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num), 2) as memory,
round(sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_score_data_table}.session_id), 2) as max_memory,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_score_data_table}.session_id), 2) as big_jank,
round(sum({$this->performance_stat_data_table}.fps_jitter_count_10) / count({$this->performance_score_data_table}.session_id), 2) as fps_jitter,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round(sum({$this->performance_stat_data_table}.sum_battery_temp) / sum({$this->performance_stat_data_table}.num), 2) as temp,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter
EXPRESSION;
        return [
            $this->handleListData(
                $this->getCommonBuilder($this->params['contrast_data'][0])
                    ->selectRaw($selectRaw)
                    ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
                    ->getFromSR()
            ),
            $this->handleListData(
                $this->getCommonBuilder($this->params['contrast_data'][1])
                    ->selectRaw($selectRaw)
                    ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
                    ->getFromSR()
            ),
        ];
    }

    /**
     * 获取设备对比列表
     *
     * @return array
     */
    public function getDevList()
    {
        // 获取两个条件的所有机型
        $devModelOne = $this->getCommonBuilder($this->params['contrast_data'][0])
            ->selectRaw("{$this->mysql_apm_device_list_table}.dev_model")
            ->where("{$this->mysql_apm_device_list_table}.dev_model", '!=', '')
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model")
            ->getFromSR();
        $devModelTwo = $this->getCommonBuilder($this->params['contrast_data'][1])
            ->selectRaw("{$this->mysql_apm_device_list_table}.dev_model")
            ->where("{$this->mysql_apm_device_list_table}.dev_model", '!=', '')
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model")
            ->getFromSR();
        // 整理两个数据，取并集
        $devModels = array_unique(array_merge(array_column($devModelOne, 'dev_model'), array_column($devModelTwo, 'dev_model')));

        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_report_list_table}.app_version_name as game_version_code,
{$this->mysql_apm_device_list_table}.dev_model,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
count({$this->mysql_apm_report_list_table}.id) as num,
round(sum({$this->performance_stat_data_table}.sum_cpu_usage) / sum({$this->performance_stat_data_table}.num), 2) as cpu,
round(sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num), 2) as memory,
round(sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_score_data_table}.session_id), 2) as max_memory,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_score_data_table}.session_id), 2) as big_jank,
round(sum({$this->performance_stat_data_table}.fps_jitter_count_10) / count({$this->performance_score_data_table}.session_id), 2) as fps_jitter,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round(sum({$this->performance_stat_data_table}.sum_battery_temp) / sum({$this->performance_stat_data_table}.num), 2) as temp,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter
EXPRESSION;
        // 分页
        $page = request('page', 1);
        $limit = request('pre_page', 10);
        // 排序
        sort($devModels);
        // 分页
        $whereDevModels = array_slice($devModels, ($page - 1) * $limit, $limit);
        // 判断是否为空
        if (empty($whereDevModels)) {
            return [
                'total' => count($devModels),
                'list' => [
                    [],
                    [],
                ],
            ];
        }
        $list = $listDev = [];
        $listDev[0] = array_column($this->handleListData(
            $this->getCommonBuilder($this->params['contrast_data'][0])
                ->selectRaw($selectRaw)
                ->whereIn("{$this->mysql_apm_device_list_table}.dev_model", $whereDevModels)
                ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name", "{$this->mysql_apm_device_list_table}.dev_model")
                ->getFromSR()
        ), null, 'dev_model');
        $listDev[1] = array_column($this->handleListData(
            $this->getCommonBuilder($this->params['contrast_data'][1])
                ->selectRaw($selectRaw)
                ->whereIn("{$this->mysql_apm_device_list_table}.dev_model", $whereDevModels)
                ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name", "{$this->mysql_apm_device_list_table}.dev_model")
                ->getFromSR()
        ), null, 'dev_model');
        // 补全数据
        foreach ($whereDevModels as $model) {
            if (!isset($listDev[0][$model])) {
                $list[0][] = [
                    'game_version_code' => $this->params['contrast_data'][0]['game_version_code'],
                    'dev_model' => $model,
                    'score' => 0,
                    'num' => 0,
                    'cpu' => 0,
                    'memory' => 0,
                    'max_memory' => 0,
                    'big_jank' => 0,
                    'fps_jitter' => 0,
                    'fps' => 0,
                    'stutter' => 0,
                ];
            } else {
                $list[0][] = $listDev[0][$model];
            }
            if (!isset($listDev[1][$model])) {
                $list[1][] = [
                    'game_version_code' => $this->params['contrast_data'][1]['game_version_code'],
                    'dev_model' => $model,
                    'score' => 0,
                    'num' => 0,
                    'cpu' => 0,
                    'memory' => 0,
                    'max_memory' => 0,
                    'big_jank' => 0,
                    'fps_jitter' => 0,
                    'fps' => 0,
                    'stutter' => 0,
                ];
            } else {
                $list[1][] = $listDev[1][$model];
            }
        }
        // 获取数据
        return [
            'total' => count($devModels),
            'list' => [
                $list[0],
                $list[1],
            ],
        ];
    }

    /**
     * 获取筛选数据
     *
     * @return array
     */
    public function getSelect()
    {
        $builder = MysqlApmReportList::query() //查询报告表
            ->join($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']); //过滤掉不在时间范围内的数据
        return [
            'game_version_code' => array_column((clone $builder)->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name as game_version_code")
                ->where("{$this->mysql_apm_report_list_table}.app_version_name", '<>', '')
                ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
                ->orderByDesc("{$this->mysql_apm_report_list_table}.app_version_name")
                ->getFromSR(), 'game_version_code'),
            'inner_version' => array_column((clone $builder)->selectRaw("{$this->mysql_apm_report_list_table}.inner_version")
                ->where("{$this->mysql_apm_report_list_table}.inner_version", '<>', '')
                ->groupBy("{$this->mysql_apm_report_list_table}.inner_version")
                ->orderByDesc("{$this->mysql_apm_report_list_table}.inner_version")
                ->getFromSR(), 'inner_version'),
            'quality' => array_column((clone $builder)->selectRaw("{$this->mysql_apm_report_list_table}.quality")
                ->where("{$this->mysql_apm_report_list_table}.quality", '<>', '')
                ->groupBy("{$this->mysql_apm_report_list_table}.quality")
                ->orderByDesc("{$this->mysql_apm_report_list_table}.quality")
                ->getFromSR(), 'quality'),
            'dev_model' => array_column((clone $builder)->selectRaw("{$this->mysql_apm_device_list_table}.dev_model")
                ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备表
                    $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                        ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
                })
                ->where("{$this->mysql_apm_device_list_table}.dev_model", '<>', '')
                ->groupBy("{$this->mysql_apm_device_list_table}.dev_model")
                ->orderByDesc("{$this->mysql_apm_device_list_table}.dev_model")
                ->getFromSR(), 'dev_model'),
        ];
    }
}
