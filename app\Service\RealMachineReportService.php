<?php
/**
 * RealMachineReportService.php
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/11
 */

namespace App\Service;

use Alchemy\Zippy\Zippy;
use App\Components\Helper\DataHelper;
use App\Components\Scope\Scope;
use App\Model\RealMachine\RealMachineReport;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;

class RealMachineReportService
{
    private $report;
    protected $scope;
    private $reportPath;

    public function __construct(RealMachineReport $report, $scope = null)
    {
        empty($scope) && $scope = new Scope();
        $this->scope = $scope;
        $this->report = $report;
        // 存储路径为 upload/realMachineReport
        $this->reportPath = "upload/realMachineReport";
    }

    /**
     * 保存压缩文件
     * @param $report
     * @return string
     */
    public function storeReport($report): string
    {
        // 获取压缩包的原始名称(包括后缀)
        $originalName = $report->getClientOriginalName();
        // 存储压缩包
        return $report->storeAs(
            $this->reportPath, $originalName, 'public'
        );
    }

    /**
     * 1. 解压到对应路径
     * 2. 删除压缩包
     * @param string $reportUrl
     * @return void
     */
    public function extractReport(string $reportUrl): void
    {
        // 加载文件
        $file = new File(storage_path('app/public/' . $reportUrl));
        $zippy = Zippy::load();
        // 打开压缩包
        $archive = $zippy->open($file->getPathname());
        // 解压到对应路径
        $archive->extract($file->getPath());
        // 没有这一步会导致压缩包删除失败 cant delete file, it gives error "Text file busy"
        $archive = null;
    }

    /**
     * 读取brief-report.json的 元数据中的数据
     * @param $reportUrl
     * @return array
     * @throws FileNotFoundException
     */
    public function getData($reportUrl): array
    {
        // 去掉.zip后缀就是解压的文件夹路径
        $path = str_replace('.zip', '/', $reportUrl);
        // 读取brief-report.json数据
        $briefReportPath = $path . "brief-report.json";
        $contents = Storage::disk('public')->get($briefReportPath);
        $json = json_decode($contents, true);
        if (empty($json)) {
            \Log::info('json_decode failed-' . $contents);
            return [];
        }
        $data = [];
        // 元数据
        $meta = $json['meta'];
        isset($meta['deviceModel']) && $data['device_model'] = $meta['deviceModel'];
        isset($meta['avgCpu']) && $data['avg_cpu'] = $meta['avgCpu'];
        isset($meta['maxMem']) && $data['max_mem'] = $meta['maxMem'];
        isset($meta['avgMem']) && $data['avg_mem'] = $meta['avgMem'];
        isset($meta['score']) && $data['score'] = $meta['score'];
        isset($meta['avgMem']) && $data['avg_mem'] = $meta['avgMem'];
        isset($meta['jank_10min']) && $data['jank_10min'] = $meta['jank_10min'];
        return $data;
    }

    /**
     * 移动文件到对应的id目录下
     * 删除源文件
     * @param string $reportUrl
     * @param int $id
     * @return bool
     */
    public function moveReport(string $reportUrl, int $id): bool
    {
        // 去掉.zip后缀就是解压的文件夹路径
        $path = str_replace('.zip', '/', $reportUrl);
        // 新路径 压缩包所在的目录名+$id
        $newPath = dirname($reportUrl) . "/" . $id;

        $disk = Storage::disk('public');
        // 将解压的文件移动到带id的目录下
        $disk->move($path, $newPath);
        // 删除压缩包
        return $disk->delete($reportUrl);
    }

    /**
     * 列表和总条数
     * @param array $select
     * @return array
     */
    public function getReportList(array $select = ['*']): array
    {
        // 根据developer_app_id进行筛选并将数据根据创建时间进行排序
        $query = $this->report::query()
            ->tap(function ($query) {
                $this->scope->getBuilder($query);
            });

        // $select是查询字段 默认是全部
        $list = (clone $query)->when(!empty($this->scope->getDefaultPage()) && !empty($this->scope->getDefaultPerPage()), function ($query) {
            $query->offset((($this->scope->getPage() - 1) * $this->scope->getPerPage()))->limit($this->scope->getPerPage() + 1);
        })->select($select)->get();
        // 统计总条数
        $total = (clone $query)->count();
        return [$list, $total];
    }

    /**
     * 处理统计数据的趋势
     * @param array $list
     * @return array
     */
    public function setStatisticsTrend(array $list): array
    {
        $perPage = $this->scope->getPerPage();
        $count = count($list);
        // 如果list没有数据则不需要处理统计数据的趋势
        if ($count === 0) {
            return $list;
        }
        // $count <= $perPage 说明最早创建的记录在本页需要特殊处理
        if ($count <= $perPage) {
            foreach (RealMachineReport::STATISTIC_TREND_INDEX as $index) {
                $list[$count - 1][$index] = 0;
            }
        }
        //当isset为true时 说明是第一条数据不需要进行比较
        //当isset为false时 说明不是第一条数据需要与上一条数据进行比较(1为上升、0为与上次一致、-1为下降)
        for ($i = 0; $i < $count - 1; $i++) {
            foreach (RealMachineReport::STATISTIC_TREND_INDEX as $key => $index) {
                if (!isset($list[$i][$index])) {
                    $item = RealMachineReport::STATISTIC_NUM[$key];
                    $list[$i][$index] = DataHelper::cmp($list[$i][$item], $list[$i + 1][$item]);
                }
            }
        }
        // $count = $perPage+1 说明最早创建的记录不在本页需要删除最后一条数据
        if ($count === $perPage + 1) {
            unset($list[$perPage]);
        }
        return $list;
    }

    public function deleteReport($reportId): bool
    {
        return Storage::disk('public')->deleteDirectory($this->reportPath . "/" . $reportId);
    }

}
