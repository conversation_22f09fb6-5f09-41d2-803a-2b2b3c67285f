<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePerfDeviceUseDurationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('perf_device_use_duration', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('device_code')->default('')->comment('设备码');
            $table->integer('duration')->default(0)->comment('使用时长，单位秒');
            $table->date('date')->default(null)->comment('使用日期，Y-m-d');
            $table->timestamps();
            $table->index(['device_code', 'date']);
            $table->index('date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('perf_device_use_duration');
    }
}
