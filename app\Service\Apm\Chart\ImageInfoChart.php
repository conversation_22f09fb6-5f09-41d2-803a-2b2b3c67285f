<?php

/**
 * 获取图片详情数据
 * @desc 获取图片详情数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;
use App\Service\Cos\CosService;

class ImageInfoChart extends BaseChart
{
    /**
     * 获取Builder
     *
     * @return BaseBuilder
     */
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'exception_image',
            ])
            ->where('session_id', $this->reportId)
            ->where('exception_image', '!=', '');
    }

    /**
     * 处理数据
     *
     * @return array
     */
    protected function handleData(): array
    {
        // 获取图片列表
        $images = (new CosService('apm'))->getDirectoryFiles($this->reportId);
        $list = [];
        foreach ($this->result as $item) {
            // 图片不存在
            if (!in_array(urlencode($item['exception_image']), $images)) {
                continue;
            }
            $list[$item['exception_image']] = $item['exception_image'];
        }
        return array_values($list);
    }
}
