<?php
/**
 * InfoException.php
 * <AUTHOR> <<EMAIL>>
 * @date     2020/9/9
 * PhpStorm
 * @desc: 代码逻辑判断, 抛出消息异常, 用于用户操作错误提示
 */

namespace App\Exceptions;

use App\Components\ApiResponse\StatusCode;

class InfoException extends \Exception
{
    public function render($request): array
    {
        $code = $this->getCode();
        $message = $this->getMessage();
        if (empty($message)) {
            $message = StatusCode::$codeMessage[$code] ?? '--';
        }
        return ['code' => $code, 'message' => $message];
    }
}
