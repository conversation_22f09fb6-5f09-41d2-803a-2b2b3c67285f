<?php

/**
 * 获取资源版本评分数据
 * @desc 获取资源版本评分数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceScoreData;

class InnerVersion extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        return $this->getScore();
    }

    /**
     * 获取分数
     *
     * @return array
     */
    protected function getScore(): array
    {
        //获取版本、平均分
        $selectRaw = <<<COLUMNS
{$this->mysql_apm_report_list_table}.inner_version as inner_version,
ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score
COLUMNS;

        return $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw($selectRaw)
            ->where("{$this->mysql_apm_report_list_table}.inner_version", '<>', '') //过滤掉版本号为空的数据
            ->groupBy("{$this->mysql_apm_report_list_table}.inner_version") //按版本号分组
            ->orderBy("inner_version", 'desc') //按版本排序
            ->limit(10) //取前10个
            ->getFromSR();
    }
}
