<?php

/**
 * 性能统计概览
 * @desc 性能统计概览
 * <AUTHOR> <EMAIL>
 * @date 2024/02/18
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\StatValidation;
use App\Service\Apm\Analysis\GetMonitorData;
use App\Service\Apm\Stat\BasicInfo;
use App\Service\Apm\Stat\DeviceModel;
use App\Service\Apm\Stat\GameVersion;
use App\Service\Apm\Stat\InnerVersion;
use App\Service\Apm\Stat\OptimizationAdvice;
use App\Service\Apm\Stat\PerfTrend;
use App\Service\Apm\Stat\Tag;
use Exception;
use Illuminate\Http\JsonResponse;

class ApmPerformanceStatController extends Controller
{
    /**
     * 获取基础信息
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3675
     * @return JsonResponse
     */
    public function basic(): JsonResponse
    {
        //请求参数校验
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->innerVersion()->quality()->gameVersionCode()
            ->validate();

        try {
            //获取数据
            $data = (new BasicInfo($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('获取基础信息接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 优化建议
     *
     * @doc
     * @return JsonResponse
     */
    public function tips(): JsonResponse
    {
        //请求参数校验
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->innerVersion()->quality()->gameVersionCode()
            ->validate();

        try {
            $tips = (new OptimizationAdvice($params))->getTips();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $tips);
        } catch (Exception $e) {
            \Log::error('优化建议接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 性能趋势
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3677
     * @return JsonResponse
     */
    public function trend(): JsonResponse
    {
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->gameVersionCode()->isSimulator()->deviceTier()
            ->innerVersion()->quality()
            ->validate();

        try {
            //获取数据
            $data = (new PerfTrend($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('性能趋势统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 游戏版本
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3680
     * @return JsonResponse
     */
    public function gameVersion(): JsonResponse
    {
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->innerVersion()->quality()->gameVersionCode()
            ->validate();

        try {
            //获取数据
            $data = (new GameVersion($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('游戏版本统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 机型统计接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3681
     * @return JsonResponse
     */
    public function model(): JsonResponse
    {
        //请求参数校验
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->innerVersion()->quality()->gameVersionCode()
            ->validate();

        try {
            //获取数据
            $data = (new DeviceModel($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('机型统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 标签评分统计接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3780
     * @return JsonResponse
     */
    public function tag(): JsonResponse
    {
        //请求参数校验
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->innerVersion()->quality()->gameVersionCode()
            ->validate();

        try {
            //获取数据
            $data = (new Tag($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('标签评分统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 资源版本评分统计接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14226
     * @return JsonResponse
     */
    public function innerVersion(): JsonResponse
    {
        //请求参数校验
        $params = StatValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->innerVersion()->quality()->gameVersionCode()
            ->validate();

        try {
            //获取数据
            $data = (new InnerVersion($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('资源版本评分统计接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取监控数据
     *
     * @return JsonResponse
     */
    public function monitor(): JsonResponse
    {
        $params = request()->all();
        if (empty($params['key']) || $params['key'] != 'sP6XMSA41j7Zn38UkrT9gtfyKcqvOeYm') {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            //获取数据
            $data = (new GetMonitorData($params))->getData();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error('获取监控数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
