<?php

/**
 * 特效检测服务类
 * @desc 特效检测服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/02/08
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use App\Model\Special\Report;
use App\Model\Special\ThresholdConfig;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\Storage;

class SpecialReportService
{
    /**
     * 报告实例对象
     *
     * @var Report
     */
    private $report;

    /**
     * 构造函数
     *
     * @param Report $report
     */
    public function __construct(Report $report)
    {
        $this->report = $report;
    }

    /**
     * 保存报表文件并返回地址
     * @param $report
     * @param string $reportFormat
     * @return string
     */
    public function storeReport($report, string $reportFormat): string
    {
        // 将接收到的json存储到storage/app/public/upload/special-report目录下
        $reportPath = "upload/special-report/" . date("Y-m");
        // json文件命名应该唯一
        // 命名格式为 date("d")."_" . md5($input['report']->getClientOriginalName().now())
        $reportName = date("d") . "_" . md5($report->getClientOriginalName() . now());
        $reportUrl = $reportPath . "/" . $reportName . "." . $reportFormat;
        $report->storeAs(
            $reportPath, $reportName . "." . $reportFormat, 'public'
        );
        //访问的地址为
        //域名/storage/upload/special-report/date("Y-m")/date("d")."_" .md5($report->getClientOriginalName() . now()).json
        return "storage/" . $reportUrl;
    }

    /**
     * 筛选后得出的列表(分页)和总条数
     * @param int $developer_app_id
     * @param int $perPage
     * @param int $page
     * @param $start_created_at
     * @param $end_created_at
     * @return array
     */
    public function getReportList(int $developer_app_id, int $perPage, int $page, $start_created_at, $end_created_at): array
    {
        //根据创建时间进行筛选
        isset($start_created_at, $end_created_at) &&
        $this->report = $this->report->whereBetween('created_at', [$start_created_at, $end_created_at]);
        //根据developer_app_id进行筛选并将数据根据创建时间进行排序
        $query = $this->report->where('developer_app_id', $developer_app_id)->orderBy('created_at', 'desc');
        //处理分页
        $list = $this->paging((clone $query), $page, $perPage);
        //对数据进行处理
        $currentConfig = $this->getCurrentConfig($developer_app_id);
        foreach ($list as $key => $value) {
            $value = array_merge($value, $this->parseJsonFile($value['report_url'], $currentConfig));
            //移除不需要的字段
            unset($value['report_url']);
            //重新赋值
            $list[$key] = $value;
        }
        //处理统计数据的趋势
        $list = $this->setStatisticsTrend($list, $perPage);
        //统计总条数
        $total = (clone $query)->count();
        return [$list, $total];
    }

    /**
     * 处理分页
     * @param $query
     * @param int $page
     * @param int $perPage
     * @return array
     */
    private function paging($query, int $page, int $perPage): array
    {
        //offset 是数据偏移量 (页数-1) × 条数
        //统计数据的趋势需要比较上一条记录,为了方便处理limit多取一条记录
        return $query->offset((($page - 1) * $perPage))->limit($perPage + 1)
            ->get(['report_id', 'report_url', 'remark', 'created_at'])
            ->toArray();
    }

    /**
     * 获取当前阈值配置
     *
     * @param $developerAppId
     * @return array
     */
    public function getCurrentConfig($developerAppId): array
    {
        //从数据库中获取当前阈值配置
        $currentConfig = ThresholdConfig::query()->where('is_current_config', ThresholdConfig::CURRENT)
            ->where('developer_app_id', $developerAppId)
            ->first(['config']);

        //数据库中没有则读取默认配置
        if (empty($this->currentConfig)) {
            $currentConfig = [
                'config' => ThresholdConfig::DEFAULT_CONFIG
            ];
        } else {
            $currentConfig = $currentConfig->toArray();
        }

        return $currentConfig;
    }

    /**
     * 解析json文件的内容
     *
     * @param string $path
     * @param array $config
     * @return array
     */
    public function parseJsonFile(string $path, array $config): array
    {
        //统计结果
        $data = [
            'totality_memory' => 0,
            'most_particle' => 0,
            'average_overdraw' => 0,
            'effect_pixel_reality_fill' => 0,
        ];
        try {
            //读取json文件的内容
            $json = Storage::disk('public')->get($this->checkPath($path));
            //json格式转换为数组
            $jsonArr = json_decode($json, true);
            //筛选出“最高粒子数”、“平均每像素overDraw率统计数量”、“特效实际填充像素点平均值”的阈值
            $maxMostParticle = 0;
            $maxAverageOverdraw = 0;
            $maxEffectPixelRealityFill = 0;
            foreach ($config['config']['rules'] as $item) {
                switch ($item['type']) {
                    case 2:
                        $maxMostParticle = $item['thresholdValues']['val'];
                        break;
                    case 3:
                        $maxAverageOverdraw = $item['thresholdValues']['val'];
                        break;
                    case 4:
                        $maxEffectPixelRealityFill = $item['thresholdValues']['val'];
                        break;
                }
            }
            //判断json解析是否成功
            if ($jsonArr) {
                //循环统计
                foreach ($jsonArr['effectList'] as $item) {
                    $data['totality_memory'] = bcadd($data['totality_memory'], $item['chartlet_memory'], 2);
                    if ($item['most_particle'] > $maxMostParticle) $data['most_particle']++;
                    if ($item['average_overdraw_ratio'] > $maxAverageOverdraw) $data['average_overdraw']++;
                    if ($item['avg_effect_pixel_reality_fill'] > $maxEffectPixelRealityFill) $data['effect_pixel_reality_fill']++;
                }
            }
            //返回
            return $data;
        } catch (FileNotFoundException $e) {
            return $data;
        }
    }

    /**
     * 处理统计数据的趋势
     * @param array $list
     * @param int $perPage
     * @return array
     */
    private function setStatisticsTrend(array $list, int $perPage): array
    {
        $count = count($list);
        // 如果list没有数据则不需要处理统计数据的趋势
        if ($count === 0) {
            return $list;
        }
        // $count <= $perPage 说明最早创建的记录在本页需要特殊处理
        if ($count <= $perPage) {
            $list[$count - 1]['totality_memory_trend'] = 0;
            $list[$count - 1]['most_particle_trend'] = 0;
            $list[$count - 1]['average_overdraw_trend'] = 0;
            $list[$count - 1]['effect_pixel_reality_fill_trend'] = 0;
        }
        //当isset为true时 说明是第一条数据不需要进行比较
        //当isset为false时 说明不是第一条数据需要与上一条数据进行比较(1为上升、0为与上次一致、-1为下降)
        for ($i = 0; $i < $count - 1; $i++) {
            isset($list[$i]['totality_memory_trend']) ||
            $list[$i]['totality_memory_trend'] = $this->cmp($list[$i]['totality_memory'], $list[$i + 1]['totality_memory']);
            isset($list[$i]['most_particle_trend']) ||
            $list[$i]['most_particle_trend'] = $this->cmp($list[$i]['most_particle'], $list[$i + 1]['most_particle']);
            isset($list[$i]['average_overdraw_trend']) ||
            $list[$i]['average_overdraw_trend'] = $this->cmp($list[$i]['average_overdraw'], $list[$i + 1]['average_overdraw']);
            isset($list[$i]['effect_pixel_reality_fill_trend']) ||
            $list[$i]['effect_pixel_reality_fill_trend'] = $this->cmp($list[$i]['effect_pixel_reality_fill'], $list[$i + 1]['effect_pixel_reality_fill']);
        }
        // $count = $perPage+1 说明最早创建的记录不在本页需要删除最后一条数据
        if ($count === $perPage + 1) {
            unset($list[$perPage]);
        }
        return $list;
    }

    /**
     * 删除报告的文件
     *
     * @param $path
     * @return bool
     */
    public function deleteReport($path): bool
    {
        return Storage::disk('public')->delete($this->checkPath($path));
    }

    /**
     * 数据比较生成趋势
     * @param $new
     * @param $old
     * @return int
     */
    private function cmp($new, $old): int
    {
        if ($new > $old) {
            return 1;
        }

        if ($new < $old) {
            return -1;
        }

        return 0;
    }

    /**
     * 检测文件路径
     *
     * @param string $path
     * @return string
     */
    private function checkPath(string $path): string
    {
        //判断是否以，storage/ 这个开头，是则删除
        if (mb_stripos($path, 'storage/') === 0) {
            $path = substr($path, 8);
        }
        return $path;
    }
}
