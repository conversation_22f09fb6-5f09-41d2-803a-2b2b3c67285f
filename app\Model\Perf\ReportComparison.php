<?php

/**
 * 线下性能检测报告对比模型
 * @desc 线下性能检测报告对比模型
 * <AUTHOR> chen<PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/12/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\Perf;

use App\Model\BaseModel;

class ReportComparison extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'perf_comparison';

    /**
     * 可写入的字段
     *
     * @var array
     */
    protected $fillable = [
        'uuid',
        'creator',
        'reports',
    ];

    /**
     * 类型转换
     *
     * @var string[]
     */
    protected $casts = [
        'reports' => 'array'
    ];
}
