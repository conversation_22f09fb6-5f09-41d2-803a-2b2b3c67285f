<?php
/**
 * ShareRecord.php
 *
 * User: Dican
 * Date: 2022/9/7
 * Email: <<EMAIL>>
 */

namespace App\Model;





/**
 * App\Model\ShareRecord
 *
 * @property int $record_id
 * @property bool $type 业务类型 1为检测工具
 * @property int $info_id 业务id
 * @property int $user_id 分享人id
 * @property int $limit 有效期 单位为小时 0则永久
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereInfoId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereRecordId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\ShareRecord whereUserId($value)
 * @mixin \Eloquent
 */
class ShareRecord extends BaseModel
{
    protected $table = 'share_record';
    protected $primaryKey = 'record_id';

    //业务类型 1为检测工具
    const TYPE_CHECKOUT = 1;
    const TYPE = [
        self::TYPE_CHECKOUT,
    ];

    /**
     * 获取签名
     * @return string
     */
    public function getSign(): string
    {
        return sha1($this->checkout_id . config('app.key'));
    }
}
