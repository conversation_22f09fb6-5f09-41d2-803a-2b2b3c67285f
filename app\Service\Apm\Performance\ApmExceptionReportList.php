<?php

/**
 * 获取异常性能报告列表数据
 * @desc 获取异常性能报告列表数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use Illuminate\Support\Facades\DB;

class ApmExceptionReportList extends ApmBase
{
    /**
     * 获取具体设备某一天的数据报告列表
     */
    public function getList(): array
    {
        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_report_list_table}.id AS report_id,
{$this->mysql_apm_report_list_table}.dev_str AS dev_str,
{$this->performance_stat_data_table}.duration AS duration,
ROUND({$this->performance_stat_data_table}.exception_count / ({$this->performance_stat_data_table}.duration / 3600)) AS exception_count,
{$this->performance_stat_data_table}.user_id AS user_id,
{$this->performance_stat_data_table}.tags_info AS tags_info,
{$this->performance_stat_data_table}.max_used_memory AS max_used_memory,
{$this->performance_stat_data_table}.big_jank_count_10 AS big_jank_count_10,
{$this->performance_stat_data_table}.fps_jitter_count_10 AS fps_jitter_count_10,
{$this->mysql_apm_device_list_table}.dev_brand AS dev_brand,
{$this->mysql_apm_device_list_table}.dev_model AS dev_model,
{$this->mysql_apm_report_list_table}.app_version_name AS game_version_code,
{$this->mysql_apm_report_list_table}.inner_version AS inner_version,
{$this->mysql_apm_report_list_table}.quality AS quality,
{$this->performance_score_data_table}.all_score AS all_score,
{$this->mysql_apm_report_list_table}.created_at AS created_at,
{$this->mysql_apm_device_list_table}.os_type AS os_type,
{$this->mysql_apm_device_list_table}.device_tier AS device_tier,
{$this->mysql_apm_device_list_table}.is_simulator AS is_simulator,
{$this->performance_stat_data_table}.sum_fps / {$this->performance_stat_data_table}.num AS avg_fps,
{$this->performance_stat_data_table}.sum_used_memory / {$this->performance_stat_data_table}.num AS avg_used_memory,
{$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time AS stutter
EXPRESSION;

        $builder = MysqlApmReportList::query()
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->join($this->performance_score_data_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where(DB::raw("({$this->performance_stat_data_table}.exception_count / ({$this->performance_stat_data_table}.duration / 3600))"), '>=', $this->getExceptionMarkVal())
            ->where("{$this->performance_stat_data_table}.exception_count", '>', 1)
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->when($this->params['os_type'] ?? null, function ($query) {
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['device_tier'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when($this->params['game_version_code'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when(isset($this->params['is_simulator']), function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->when($this->params['tags_info'] ?? null, function ($query) {
                return $query->whereRaw("array_contains({$this->performance_stat_data_table}.tags_info, '{$this->params['tags_info']}') == 1");
            })
            ->when($this->params['dev_str'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.dev_str", $this->params['dev_str']);
            })
            ->when($this->params['user_id'] ?? null, function ($query) {
                return $query->whereRaw("array_contains({$this->performance_stat_data_table}.user_id, '{$this->params['user_id']}') == 1");
            });

        $count = (clone $builder)
            ->selectRaw('count(1) as count')
            ->firstFromSR();
        // 判断数据是否为空
        if ($count['count'] == 0) {
            return [
                'total' => 0,
                'list' => [],
            ];
        }
        // 获取数据列表
        $this->reportList = (clone $builder)
            ->selectRaw($selectRaw)
            ->orderBy($this->sortField, $this->sortType)
            ->limit($this->perPage)
            ->offset($this->getPageNum())
            ->getFromSR();

        //处理数据
        $this->handleListData();

        //返回数据
        return [
            'total' => $count['count'],
            'list' => $this->reportList,
        ];
    }

    /**
     * 处理列表数据
     *
     * @return void
     */
    protected function handleListData()
    {
        //判断是否有数据
        if (empty($this->reportList)) {
            return;
        }

        foreach ($this->reportList as $key => $item) {
            $this->reportList[$key]['dev_model'] = ApmModelMap::getValue($item['dev_model']);
            $this->reportList[$key]['avg_fps'] = bcadd(sprintf('%f', $item['avg_fps']), 0, 2) ?? 0; // fps均值
            $this->reportList[$key]['avg_used_memory'] = bcadd(sprintf('%f', $item['avg_used_memory']), 0, 2) ?? 0; // 使用内存均值
            $this->reportList[$key]['stutter'] = bcmul(sprintf('%f', $item['stutter']), 100, 2) . '%' ?? '0%'; // 卡帧率
            $this->reportList[$key]['big_jank_count_10'] = bcadd(sprintf('%f', $item['big_jank_count_10']), 0, 2) ?? 0;
            $this->reportList[$key]['fps_jitter_count_10'] = bcadd(sprintf('%f', $item['fps_jitter_count_10']), 0, 2) ?? 0;
            // user_id 和 tags_info 转成数组
            $this->reportList[$key]['user_id'] = json_decode($item['user_id'], true);
            $this->reportList[$key]['tags_info'] = json_decode($item['tags_info'], true);
        }
    }
}
