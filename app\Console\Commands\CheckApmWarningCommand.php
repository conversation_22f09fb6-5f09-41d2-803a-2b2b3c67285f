<?php

/**
 * 预警检查脚本
 * @desc 预警检查脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Jobs\Apm\QuestionMonitorJob;
use App\Jobs\Apm\StatMonitorJob;
use App\Model\Apm\ApmWarning;
use App\Model\Apps;
use Illuminate\Console\Command;

class CheckApmWarningCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:apm:warning';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'APM预警检查脚本';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行逻辑
     *
     * @return void
     */
    public function handle()
    {
        $apps = Apps::query()->get()->toArray();
        //转换成id为key的数组
        $apps = array_column($apps, null, 'id');
        //查询状态开启的预警
        ApmWarning::query()
            ->with('warningRule')
            ->where('status', ApmWarning::START)
            ->orderBy('warning_id')
            ->chunk(1000, function ($warnings) use ($apps) {
                foreach ($warnings as $warning) {
                    $app = $apps[$warning['developer_app_id']] ?? null;
                    //如果应用不存在或者应用已经停用
                    if (empty($app)) {
                        continue;
                    }

                    //设置app信息
                    $warning['app'] = $app;
                    //推入到队列中
                    switch ($warning['monitor_type']) {
                        case ApmWarning::REPORT_MONITOR:
                            StatMonitorJob::dispatch($warning->toArray())->onQueue('apm_warning_queue');
                            break;
                        case ApmWarning::ERROR_MONITOR:
                            QuestionMonitorJob::dispatch($warning->toArray())->onQueue('apm_warning_queue');
                            break;
                    }
                }
            });
    }
}
