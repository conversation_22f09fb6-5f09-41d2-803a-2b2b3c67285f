<?php

namespace App\Model\RealMachine;

use App\Model\BaseModel;
use App\Model\ModelTrait;
use Illuminate\Support\Carbon;

/**
 * App\Model\RealMachine\RealMachineReport
 *
 * @property int $report_id
 * @property int $developer_app_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereReportId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereDeveloperAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property string $device_model 机型设备
 * @property bool $platform 平台（1为PC、2为安卓、3为苹果）
 * @property float $avg_cpu CPU耗时均值
 * @property float $max_mem 设备内存峰值
 * @property string|null $remark 备注
 * @property int $score 评分
 * @property float $avg_mem 设备内存均值
 * @property float $jank_10min jank(10min)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereAvgCpu($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereAvgMem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereDeviceModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereJank10min($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereMaxMem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport wherePlatform($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\RealMachine\RealMachineReport whereScore($value)
 */
class RealMachineReport extends BaseModel
{
    use ModelTrait;

    protected $table = 'real_machine_report';
    protected $primaryKey = 'report_id';
    protected $fillable = [
        'developer_app_id', 'avg_cpu', 'max_mem', 'platform', 'device_model', 'remark', 'score', 'jank_10min', 'avg_mem'
    ];
    public $validateRule = [
        'developer_app_id' => 'required|int',
        'avg_cpu' => 'required|numeric',
        'max_mem' => 'numeric',
        'platform' => 'required|int',
        'device_model' => 'required|string|max:128',
        'remark' => 'string|max:255',
        'avg_mem' => 'required|numeric',
        'jank_10min' => 'required|numeric',
        'score' => 'required|numeric',

    ];
    public const STATISTIC_NUM = ['avg_cpu', 'score', 'jank_10min', 'avg_mem'];

    public const STATISTIC_TREND_INDEX = ['avg_cpu_trend', 'score_trend', 'jank_10min_trend', 'avg_mem_trend'];
}
