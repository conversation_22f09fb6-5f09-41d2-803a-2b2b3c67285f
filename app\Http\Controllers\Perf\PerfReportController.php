<?php

/**
 * 线下性能检测报告控制器
 * @desc 线下性能检测报告控制器
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Perf;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Jobs\Apm\ReCalculatePerformScoreJob;
use App\Model\Perf\Bookmark;
use App\Model\Perf\BookmarkItem;
use App\Model\Perf\Project;
use App\Model\Perf\Report;
use App\Model\Perf\ReportTag;
use App\Service\PerfomanceScrore\ReCalculatePerformScore;
use App\Service\PerfReportService;
use App\Service\PerfReportStatService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PerfReportController extends Controller
{
    /**
     * 报告模型实例
     *
     * @var Report
     */
    private $report;

    /**
     * 报告服务实例
     *
     * @var PerfReportService
     */
    private $reportService;

    /**
     * 初始化
     *
     * @param Report $report
     * @param PerfReportService $reportService
     */
    public function __construct(Report $report, PerfReportService $reportService)
    {
        $this->report = $report;
        $this->reportService = $reportService;
    }

    /**
     * 通用性能检测新增接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2535
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'app_icon' => 'file|mimes:jpeg,jpg,png,bmp,gif',
            'report' => 'required|file'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        $reportFormat = "json";
        if ($input['report']->getClientOriginalExtension() === $reportFormat) {
            try {
                list($input['report_url'], $baseReportPath) = $this->reportService->storeReport($input['report'], $reportFormat);
                //判断是否又上传图片
                if (isset($input['app_icon'])) {
                    list($input['app_icon'], $baseIcon) = $this->reportService->storeReport($input['app_icon'], $input['app_icon']->getClientOriginalExtension());
                }
                DB::connection('tool')->beginTransaction();
                // 判断是否有device_data字段，并且是数组，则将device_data字段转换为json
                if (isset($input['device_data']) && is_array($input['device_data'])) {
                    $input['device_data'] = json_encode($input['device_data'], JSON_UNESCAPED_UNICODE);
                }
                $this->report->store($input, true);
                DB::connection('tool')->commit();

                dispatch((new ReCalculatePerformScoreJob($baseReportPath, $input['ram'])))->onQueue("performance_score_re_calculate");
                //                $job = new ReCalculatePerformScoreJob($baseReportPath, $input['ram'], $input['device']);
                //                $job->handle();

                return $this->response();
            } catch (Exception $e) {
                Log::error('通用性能检测新增接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                    $e->getLine());
                DB::connection('tool')->rollBack();
                return $this->response(StatusCode::C_SYS_EXCAPTION);
            }
        } else {
            return $this->response(StatusCode::C_PARAM_ERROR);
        }
    }

    /**
     * 检测列表接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2533
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $input = $request->all();
        try {
            $page = $input['page'] ?? 1;
            $perPage = $input['per_page'] ?? 15;
            [$list, $total] = $this->reportService->getReportList($perPage, $page, $input);
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            Log::error('通用性能检测列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取报表地址
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2562
     * @param $id
     * @return JsonResponse
     */
    public function getReport($id): JsonResponse
    {
        try {
            $content = $this->report->findOrFail($id);
            if (empty($content['app_icon'])) {
                $content['app_icon'] = "/images/{$content['system']}.png";
            }
            // 判断是否有device_data字段是否不为空，若不为空，则将device_data字段转换为数组
            if (!empty($content['device_data'])) {
                $content['device_data'] = json_decode($content['device_data'], true);
            }
            return $this->response(StatusCode::C_SUCCESS, $content);
        } catch (Exception $e) {
            Log::error('获取通用性能检测报告详情接口报错-reportId:' . $id . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 报告删除接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3958
     * @param $reportId
     * @return JsonResponse
     */
    public function delete($reportId): JsonResponse
    {
        $db = DB::connection('tool');
        try {
            $db->beginTransaction();
            $this->report::query()->where('report_id', $reportId)->delete();
            //删除收藏夹对应数据
            BookmarkItem::query()->where('report_id', $reportId)->delete();
            $db->commit();
            return $this->response();
        } catch (Exception $e) {
            $db->rollBack();
            \Log::error('通用性能检测删除接口报错-reportId:' . $reportId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 报告重命名接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15263
     * @param $reportId
     * @return JsonResponse
     */
    public function rename($reportId): JsonResponse
    {
        $input = request()->all();
        $validator = \Validator::make($input, [
            'title' => 'required|string'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            $this->report::query()->where('report_id', $reportId)->update([
                'title' => $input['title']
            ]);
            return $this->response();
        } catch (Exception $e) {
            \Log::error('通用性能检测修改名称接口报错-reportId:' . $reportId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 项目检测列表接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2533
     * @param Request $request
     * @return JsonResponse
     */
    public function projectList(Request $request): JsonResponse
    {
        $input = $request->all();
        try {
            $page = $input['page'] ?? 1;
            $perPage = $input['per_page'] ?? 15;
            //判断是否有项目id
            $projectId = $input['project_id'] ?? 0;
            $project = Project::query()->find($projectId);
            if (empty($project)) {
                return $this->response(StatusCode::C_SUCCESS, ['list' => [], 'total' => 0]);
            }
            //配置包名
            $input['package_name'] = $project->package_name;
            //获取数据
            [$list, $total] = $this->reportService->getReportList($perPage, $page, $input);
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            Log::error('项目通用性能检测列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 收藏夹检测列表接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2533
     * @param Request $request
     * @return JsonResponse
     */
    public function bookmarkList(Request $request): JsonResponse
    {
        $input = $request->all();
        try {
            $page = $input['page'] ?? 1;
            $perPage = $input['per_page'] ?? 15;
            //判断是否有收藏夹id
            $bookmarkId = $input['bookmark_id'] ?? 0;
            $bookmark = Bookmark::query()->with('items')->where('user_id', Auth::user()->user_id)->find($bookmarkId);
            if (empty($bookmark)) {
                return $this->response(StatusCode::C_SUCCESS, ['list' => [], 'total' => 0]);
            }
            //报告ID
            $input['report_ids'] = $bookmark->items->pluck('report_id')->toArray();
            if (empty($input['report_ids'])) {
                return $this->response(StatusCode::C_SUCCESS, ['list' => [], 'total' => 0]);
            }
            //获取数据
            [$list, $total] = $this->reportService->getReportList($perPage, $page, $input);
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            Log::error('收藏夹通用性能检测列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 新建用例
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15269
     * @param Request $request
     * @return JsonResponse
     */
    public function newReport(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'report_id' => 'required|integer',
            'start' => 'required|integer',
            'end' => 'required|integer',
            'name' => 'required|string'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            //根据报告ID，获取报告信息
            $report = $this->report::query()->where('report_id', $input['report_id'])->firstOrFail();
            $path = $report['report_url'];
            //获取文件的内容
            $fileData = file_get_contents(str_replace('storage', storage_path('app/public'), $path));
            //转为json
            $fileData = json_decode($fileData, true);
            //重新计算数组
            $fileData['Data'] = array_slice($fileData['Data'], $input['start'], $input['end'] - $input['start'] + 1);
            //计算Statistics
            $osTypeValues = ['android' => 1, 'ios' => 2];
            $osType = $osTypeValues[$report['system']] ?? 1;
            $fileData['Statistics'] = (new PerfReportStatService())->analyze($fileData['Data'], $osType);
            //调整ScoreDetail
            $fileData['ScoreDetail']['amount_stutter'] = floatval($fileData['Statistics']['stutter']);
            $fileData['ScoreDetail']['avg_10_big_Jank'] = floatval($fileData['Statistics']['avg10BigJank']);
            $fileData['ScoreDetail']['pss_memory_avg'] = floatval($fileData['Statistics']['avgPss']) / (1024 * 1024);
            $fileData['ScoreDetail']['pss_memory_peak'] = floatval($fileData['Statistics']['pssPeak']) / (1024 * 1024);
            $ramMb = $report['ram'];
            if (empty($ramMb) || $ramMb == 'unknown') {
                $ramMb = '4GB';
            }
            //上传ID
            $uploadId = Str::random(32);
            //修改ID
            $fileData['Id'] = $uploadId;
            //保存到 json 文件
            $filePath = str_replace(basename($path), date("d") . "_" . md5(Str::random(32) . now()) . '.json', $path);
            file_put_contents(str_replace('storage', storage_path('app/public'), $filePath), json_encode($fileData));
            //创建新的数据
            $this->report::create(array_merge($report->toArray(), [
                'report_url' => $filePath,
                'title' => $input['name'],
                'duration' => ($input['end'] - $input['start']),
                'extra' => json_encode([
                    'Statistics' => $fileData['Statistics'],
                    'ScoreV2' => (new ReCalculatePerformScore())->setLevel($fileData['ScoreDetail']['tier'] ?? 2)
                        ->setRam($ramMb)
                        ->setReportData($fileData['Data'])
                        ->setPssMemoryPeak($fileData['ScoreDetail']['pss_memory_peak'])
                        ->setAvg10BigJank($fileData['ScoreDetail']['avg_10_big_Jank'])
                        ->getScore(),
                ]),
                'upload_id' => $uploadId,
                'ram' => $ramMb,
            ]));
            // 重新计算分数
            dispatch((new ReCalculatePerformScoreJob(str_replace('storage/', '', $filePath), $ramMb)))->onQueue("performance_score_re_calculate");
            return $this->response();
        } catch (Exception $e) {
            Log::error('新建用例接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getTraceAsString());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 片段导出
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15436
     */
    public function fragmentExport(Request $request)
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'report_id' => 'required|integer',
            'start' => 'required|integer',
            'end' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            // 数据数组
            $data = [
                ['用例', '项目', '场景', '分数', 'Avg(FPS)', 'FPS>25%占比', 'Jank(卡顿)(/10min)', 'BigJank(严重卡顿)(/10min)', 'Stutter(卡顿率) [%]', 'Peak(Memory) [MB]', 'Avg(Memory) [MB]', 'Avg(AppCPU) [%]', 'Avg(电流)', 'Avg(电池温度)[℃]'],
            ];
            $title = '';
            $report = Report::query()->where('report_id', $input['report_id'])->first();
            if (empty($report)) {
                return "导出失败，报告不存在！";
            }
            //获取文件内容
            $fileData = file_get_contents(str_replace('storage', storage_path('app/public'), $report['report_url']));
            //转为json
            $fileData = json_decode($fileData, true);
            //重新计算数组
            $fileData['Data'] = array_slice($fileData['Data'], $input['start'], $input['end'] - $input['start'] + 1);
            //计算Statistics
            $osTypeValues = ['android' => 1, 'ios' => 2];
            $osType = $osTypeValues[$report['system']] ?? 1;
            $fileData['Statistics'] = (new PerfReportStatService())->analyze($fileData['Data'], $osType);
            //调整ScoreDetail
            $fileData['ScoreDetail']['amount_stutter'] = floatval($fileData['Statistics']['stutter']);
            $fileData['ScoreDetail']['avg_10_big_Jank'] = floatval($fileData['Statistics']['avg10BigJank']);
            $fileData['ScoreDetail']['pss_memory_avg'] = floatval($fileData['Statistics']['avgPss']) / (1024 * 1024);
            $fileData['ScoreDetail']['pss_memory_peak'] = floatval($fileData['Statistics']['pssPeak']) / (1024 * 1024);
            $ramMb = $report['ram'];
            if (empty($ramMb) || $ramMb == 'unknown') {
                $ramMb = '4GB';
            }
            $reportScore = (new ReCalculatePerformScore())->setLevel($fileData['ScoreDetail']['tier'] ?? 2)
                ->setRam($ramMb)
                ->setReportData($fileData['Data'])
                ->setPssMemoryPeak($fileData['ScoreDetail']['pss_memory_peak'])
                ->setAvg10BigJank($fileData['ScoreDetail']['avg_10_big_Jank'])
                ->getScore();
            $data[] = [$report['title'], $report['app_name'], 'ALL', array_sum(array_values($reportScore)), $fileData['Statistics']['avgFps'], $fileData['Statistics']['more25Fps'], $fileData['Statistics']['avg10Jank'], $fileData['Statistics']['avg10BigJank'], $fileData['Statistics']['stutter'], floatval($fileData['Statistics']['pssPeak']) / (1024 * 1024), floatval($fileData['Statistics']['avgPss']) / (1024 * 1024), $fileData['Statistics']['avgAppCpu'], floatval($fileData['Statistics']['avgCurrent']) / 1000, $fileData['Statistics']['avgBatteryTemp']];
            $title .= "{$report['title']}_";
            // 设置HTTP头以告诉浏览器这是一个CSV文件
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="PerfChcek_' . $title . date('Ymd_H_i_s') . '.csv"');
            // 打开输出流
            $output = fopen('php://output', 'w');
            // 遍历数据数组并将每一行写入CSV文件
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            // 关闭输出流
            fclose($output);
            exit;
        } catch (\Exception $e) {
            \Log::error('通用性能检测片段导出接口报错-reportId:' . $input['report_id'] . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 导出
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15279
     */
    public function export(Request $request)
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'report_ids' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            // 数据数组
            $data = [
                ['用例', '项目', '场景', '分数', 'Avg(FPS)', 'FPS>25%占比', 'Jank(卡顿)(/10min)', 'BigJank(严重卡顿)(/10min)', 'Stutter(卡顿率) [%]', 'Peak(Memory) [MB]', 'Avg(Memory) [MB]', 'Avg(AppCPU) [%]', 'Avg(电流)', 'Avg(电池温度)[℃]'],
            ];
            $title = '';
            $ids = explode(',', $input['report_ids']);
            foreach ($ids as $reportId) {
                $report = Report::query()->where('report_id', $reportId)->first();
                //为空跳过
                if (empty($report)) {
                    continue;
                }
                $tag = ReportTag::query()->where('report_id', $reportId)->first();
                //获取文件内容
                $fileData = file_get_contents(str_replace('storage', storage_path('app/public'), $report['report_url']));
                //json转为数据
                $fileData = json_decode($fileData, true);
                //获取每个标签的统计和评分
                $tags = $tag ? $tag['tag'] : [];
                $reportScore = (new ReCalculatePerformScore())->setLevel($fileData['ScoreDetail']['tier'] ?? 2)
                    ->setRam($report['ram'])
                    ->setReportData($fileData['Data'])
                    ->setPssMemoryPeak(floatval($fileData['Statistics']['pssPeak']) / (1024 * 1024))
                    ->setAvg10BigJank(floatval($fileData['Statistics']['avg10BigJank']))
                    ->getScore();
                $data[] = [$report['title'], $report['app_name'], 'ALL', array_sum(array_values($reportScore)), $fileData['Statistics']['avgFps'], $fileData['Statistics']['more25Fps'], $fileData['Statistics']['avg10Jank'], $fileData['Statistics']['avg10BigJank'], $fileData['Statistics']['stutter'], floatval($fileData['Statistics']['pssPeak']) / (1024 * 1024), floatval($fileData['Statistics']['avgPss']) / (1024 * 1024), $fileData['Statistics']['avgAppCpu'], floatval($fileData['Statistics']['avgCurrent']) / 1000, $fileData['Statistics']['avgBatteryTemp']];
                //遍历数据
                foreach ($tags as $item) {
                    $Statistics = (new PerfReportStatService())->analyze(array_slice($fileData['Data'], $item['start'], $item['end'] - $item['start'] + 1), $report['system']);
                    $ScoreV2 = (new ReCalculatePerformScore())->setLevel($fileData['ScoreDetail']['tier'] ?? 2)
                        ->setRam($report['ram'])
                        ->setReportData(array_slice($fileData['Data'], $item['start'], $item['end'] - $item['start'] + 1))
                        ->setPssMemoryPeak(floatval($Statistics['pssPeak']) / (1024 * 1024))
                        ->setAvg10BigJank(floatval($Statistics['avg10BigJank']))
                        ->getScore();
                    $data[] = [$report['title'], $report['app_name'], $item['name'], array_sum(array_values($ScoreV2)), $Statistics['avgFps'], $Statistics['more25Fps'], $Statistics['avg10Jank'], $Statistics['avg10BigJank'], $Statistics['stutter'], floatval($Statistics['pssPeak']) / (1024 * 1024), floatval($Statistics['avgPss']) / (1024 * 1024), $Statistics['avgAppCpu'], floatval($Statistics['avgCurrent']) / 1000, $Statistics['avgBatteryTemp']];
                }
                $data[] = ['', '', '', '', '', '', '', '', '', '', '', '', '', ''];
                $title .= "{$report['title']}_";
            }
            if (count($data) == 1) {
                return "导出失败，报告不存在！";
            }
            // 设置HTTP头以告诉浏览器这是一个CSV文件
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="PerfChcek_' . $title . date('Ymd_H_i_s') . '.csv"');
            // 打开输出流
            $output = fopen('php://output', 'w');
            // 遍历数据数组并将每一行写入CSV文件
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            // 关闭输出流
            fclose($output);
            exit;
        } catch (\Exception $e) {
            \Log::error('通用性能检测导出接口报错-reportId:' . $input['report_ids'] . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
