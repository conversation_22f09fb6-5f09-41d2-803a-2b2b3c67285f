<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApmSwitchChannelTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('apm_switch_channel', function (Blueprint $table) {
            $table->unsignedInteger('switch_id')->default(0)->comment('开关ID');
            $table->string('channel_name')->default('')->comment('渠道名称');
            $table->string('pack_distribute', 2550)->default('')->comment('分包标识');

            $table->index('switch_id');
        });
        \DB::connection('apm')->statement("ALTER TABLE `apm_switch_channel` comment 'apm-开关渠道表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('apm_switch_channel');
    }
}
