<?php

namespace App\Components\Helper;


class DataHelper
{
    public static function asDate($time = null, $default = '')
    {
        is_null($time) && $time = time();
        return $time == 0 ? $default : date('Y-m-d', $time);
    }

    public static function asDateTime($time = null, $default = '')
    {
        is_null($time) && $time = time();
        return $time == 0 ? $default : date('Y-m-d H:i:s', $time);
    }

    /**
     * 两个数相除
     * @param $arg1
     * @param $arg2
     * @param int $precision
     * @return float|int
     */
    public static function divide($arg1, $arg2, $precision = 2)
    {
        if (empty($arg2) || empty($arg1)) return 0;
        return round($arg1 / $arg2, $precision);
    }

    /**
     * 是否本地环境
     * @return bool
     */
    public static function isLocal(): bool
    {
        return config('app.env') == 'local';
    }

    /**
     * 是否开发环境
     * @return bool
     */
    public static function isDev(): bool
    {
        return config('app.env') == 'dev';
    }

    /**
     * 是否预发布
     * @return bool
     */
    public static function isRelease(): bool
    {
        return config('app.env') == 'release';
    }

    /**
     * 是否正式服
     * @return bool
     */
    public static function isProduction(): bool
    {
        return config('app.env') == 'production';
    }

    public static function apiUrl($prefix, $parameters = []): string
    {
        return url($prefix) . (!empty($parameters) ? ('?' . http_build_query($parameters)) : '');
    }

    /**
     * 环比计算
     * @param $now //当前值
     * @param $last //上值
     * @param int $precision //小数位
     * @return float
     */
    public static function linkRatio($now, $last, $precision = 4): float
    {
        return (float)number_format(self::divide($now - $last, $last, $precision) * 100, 2);
    }

    /**
     * 秒转换时间(年、天、小时、分、秒)
     * @param int $second 秒数
     * @return string
     */
    public static function sec2Time(int $second): string
    {
        if ($second >= 31556926) return intval($second / 31556926) . '年';
        if ($second >= 86400) return intval($second / 86400) . '天';
        if ($second >= 3600) return intval($second / 3600) . '小时';
        if ($second >= 60) return intval($second / 60) . '分';
        return $second . '秒';
    }

    /**
     * 数据比较生成趋势
     * @param $now //当前值
     * @param $last //上值
     * @return int
     */
    public static function cmp($now, $last): int
    {
        if ($now > $last) {
            return 1;
        }
        if ($now < $last) {
            return -1;
        }
        return 0;
    }

    /**
     * 企业微信跳转完整链接
     * @param $path
     * @param array $param
     * @param bool $isBase64
     * @return string
     */
    public static function wxUrlApi($path, $param = [], $isBase64 = true): string
    {
        //前端限制登陆态
        $param['auto_login'] = 1;
        $p = http_build_query($param);
        if (!empty($p)) {
            $p = $isBase64 ? '?param=' . base64_encode($p) : '?' . $p;
        }
        $url = url($path) . $p;
        return self::wxUrl($url);
    }

    /**
     * 企业微信跳转链接
     * @param $url
     * @return string
     */
    public static function wxUrl($url): string
    {
        return sprintf(config('services.re-board'), $url);
    }
}
