<?php

/**
 * 获取预警周数据
 * @desc 获取预警周数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/29
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Analysis;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Model\Apm\StarRocks\PerformanceTagScoreData;
use App\Service\Apm\Performance\ApmTrait;
use Carbon\Carbon;

class WarningWeekData
{
    use ApmTrait;

    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        $current = [Carbon::now()->subWeek()->startOfWeek()->toDateTimeString(), Carbon::now()->subWeek()->endOfWeek()->toDateTimeString()];
        $old = [Carbon::now()->subWeeks(2)->startOfWeek()->toDateTimeString(), Carbon::now()->subWeeks(2)->endOfWeek()->toDateTimeString()];
        return $this->calcRateData([
            'current' => $this->getDbData($current),
            'old' => $this->getDbData($old),
            'current_curve' => $this->getDayDbData($current),
            'battle_current' => $this->getBattleStutter($current),
            'battle_old' => $this->getBattleStutter($old),
        ]);
    }

    /**
     * 安全计算除法
     *
     * @param $numerator
     * @param $denominator
     * @param $scale
     * @return string|null
     */
    private function safeDivide($numerator, $denominator, $scale = 0)
    {
        // 检查分母是否为0，如果是，则默认将分母设置为1
        $denominator = $denominator == 0 ? 1 : $denominator;
        // 使用bcdiv进行高精度除法运算
        return bcdiv($numerator, $denominator, $scale);
    }

    /**
     * 计算环比
     *
     * @param $dates
     * @return array
     */
    private function calcRateData(array $data)
    {
        $current = $data['current'];
        $old = $data['old'];

        $current['score_num_rate'] = bcadd(round($this->safeDivide(bcsub($current['score_num'], $old['score_num'], 6), empty($old['score_num']) ? 1 : $old['score_num'], 6) * 100, 2), 0, 2);
        $current['score_rate'] = bcadd(round($this->safeDivide(bcsub($current['score'], $old['score'], 6), empty($old['score']) ? 1 : $old['score'], 6) * 100, 2), 0, 2);
        $current['smoothness_score_rate'] = bcadd(round($this->safeDivide(bcsub($current['smoothness_score'], $old['smoothness_score'], 6), empty($old['smoothness_score']) ? 1 : $old['smoothness_score'], 6) * 100, 2), 0, 2);
        $current['avg_memory_score_rate'] = bcadd(round($this->safeDivide(bcsub($current['avg_memory_score'], $old['avg_memory_score'], 6), empty($old['avg_memory_score']) ? 1 : $old['avg_memory_score'], 6) * 100, 2), 0, 2);
        $current['avg_network_traffic_rate'] = bcadd(round($this->safeDivide(bcsub($current['avg_network_traffic'], $old['avg_network_traffic'], 6), empty($old['avg_network_traffic']) ? 1 : $old['avg_network_traffic'], 6) * 100, 2), 0, 2);
        $current['avg_network_delay_rate'] = bcadd(round($this->safeDivide(bcsub($current['avg_network_delay'], $old['avg_network_delay'], 6), empty($old['avg_network_delay']) ? 1 : $old['avg_network_delay'], 6) * 100, 2), 0, 2);
        $current['battery_power_rate'] = bcadd(round($this->safeDivide(bcsub($current['battery_power'], $old['battery_power'], 6), empty($old['battery_power']) ? 1 : $old['battery_power'], 6) * 100, 2), 0, 2);
        $current['low_score_num'] = bcadd(round($this->safeDivide($current['low_score_num'], empty($current['score_num']) ? 1 : $current['score_num'], 6) * 100, 2), 0, 2);
        $old['low_score_num'] = bcadd(round($this->safeDivide($old['low_score_num'], empty($old['score_num']) ? 1 : $old['score_num'], 6) * 100, 2), 0, 2);
        $current['low_score_num_rate'] = bcadd(round($this->safeDivide(bcsub($current['low_score_num'], $old['low_score_num'], 6), empty($old['low_score_num']) ? 1 : $old['low_score_num'], 6) * 100, 2), 0, 2);
        $current['battle_stutter'] = $data['battle_current'];
        $current['battle_stutter_rate'] = bcadd(round($this->safeDivide(bcsub($data['battle_current'], $data['battle_old'], 6), empty($data['battle_old']) ? 1 : $data['battle_old'], 6) * 100, 2), 0, 2);

        return [
            'current' => $current,
            'old' => $old,
            'current_curve' => $data['current_curve'],
        ];
    }

    /**
     * 获取数据库数据
     *
     * @param $dates
     * @return array
     */
    private function getDbData(array $dates)
    {
        $selectRaw = <<<COLUMNS
count({$this->performance_score_data_table}.session_id) as score_num,
count(distinct {$this->mysql_apm_device_list_table}.dev_str) as dev_num,
sum(case when {$this->performance_score_data_table}.all_score < {$this->standardScore} then 1 else 0 end) as low_score_num,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness_score,
round((sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as avg_memory_score,
round((sum({$this->performance_stat_data_table}.down_traffic_10 + {$this->performance_stat_data_table}.up_traffic_10) / count({$this->performance_score_data_table}.session_id)) / 1024, 2) as avg_network_traffic,
round(sum(case when {$this->performance_stat_data_table}.sum_network_delay > 0 then {$this->performance_stat_data_table}.sum_network_delay / {$this->performance_stat_data_table}.num else 0 end) / sum(case when {$this->performance_stat_data_table}.sum_network_delay > 0 then 1 else 0 end), 2) as avg_network_delay,
round(sum({$this->performance_stat_data_table}.sum_battery_power) / sum({$this->performance_stat_data_table}.num), 2) as battery_power
COLUMNS;
        $data = PerformanceScoreData::query() //查询性能分数表
            ->selectRaw($selectRaw) //计算平均分
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $dates) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type'])
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->firstFromSR();
        foreach ($data as $key => $value) {
            if (empty($value)) {
                $data[$key] = 0;
            }
        }
        return $data;
    }

    /**
     * 获取周数据
     *
     * @param array $dates
     * @return array
     */
    private function getDayDbData(array $dates)
    {
        $selectRaw = <<<COLUMNS
date_format({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d') as date,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness_score
COLUMNS;
        $list = PerformanceScoreData::query() //查询性能分数表
            ->selectRaw($selectRaw) //计算平均分
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $dates) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type'])
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->groupByRaw("date_format({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d')")
            ->orderByRaw("date_format({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d') asc")
            ->getFromSR();
        // 没有日期的数据填充0
        $startDate = date('Y-m-d', strtotime($dates[0]));
        $endDate = date('Y-m-d', strtotime($dates[1]));
        $list = array_column($list, null, 'date');
        $newList = [];
        while ($startDate <= $endDate) {
            if (!isset($list[$startDate])) {
                $newList[$startDate] = [
                    'date' => $startDate,
                    'score' => 0,
                    'smoothness_score' => 0,
                ];
            } else {
                $newList[$startDate] = $list[$startDate];
            }
            $startDate = date('Y-m-d', strtotime("+1 day", strtotime($startDate)));
        }
        $list = array_values($newList);
        // 把日期转为 周一 周二 周三 周四 周五 周六 周日
        $times = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        foreach ($list as $key => $value) {
            $list[$key]['date'] = $times[$key];
        }
        return $list;
    }

    /**
     * 获取标签数据
     *
     * @param array $dates
     * @return string
     */
    private function getBattleStutter(array $dates)
    {
        $selectRaw = <<<EXPRESSION
{$this->performance_tag_score_data_table}.tag,
round(round(sum({$this->performance_tag_stat_data_table}.sum_jank_time) / sum({$this->performance_tag_stat_data_table}.sum_frame_times_time), 4) * 100, 2) as stutter
EXPRESSION;

        $data = PerformanceTagScoreData::query() //查询标签评分表
            ->selectRaw($selectRaw)
            ->join($this->performance_stat_data_table, "{$this->performance_tag_score_data_table}.session_id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->mysql_apm_report_list_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_tag_score_data_table}.session_id") //关联报告表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->join($this->performance_tag_stat_data_table, function ($json) { //关联标签统计表
                $json->on("{$this->performance_tag_stat_data_table}.session_id", '=', "{$this->performance_tag_score_data_table}.session_id") //通过session_id关联
                    ->on("{$this->performance_tag_stat_data_table}.tag", '=', "{$this->performance_tag_score_data_table}.tag"); //通过标签关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $dates) //过滤掉不在时间范围内的数据
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->where("{$this->performance_tag_score_data_table}.tag", '战斗')
            ->groupBy("{$this->performance_tag_score_data_table}.tag") //按标签分组
            ->firstFromSR();
        // 返回数据
        return $data['stutter'] ?? '0.00';
    }
}
