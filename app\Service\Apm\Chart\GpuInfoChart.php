<?php

/**
 * gpuInfo信息图表
 * @desc gpuInfo信息图表
 * <AUTHOR> <EMAIL>
 * @date 2024/08/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class GpuInfoChart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'vram_shared_total',
                'vram_shared_used',
                'vram_used',
                'tags_info',
                'app_state',
                'perf_data_ts',
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['gpuInfo'] = [    //按照时间戳分组
                'vram_shared_total' => $item['vram_shared_total'],
                'vram_shared_used' => $item['vram_shared_used'],
                'vram_used' => $item['vram_used'],
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }
        return array_values($list);
    }
}
