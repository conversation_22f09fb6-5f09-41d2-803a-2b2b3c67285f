<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use Carbon\Carbon;

class ApmRecentPlayerMatchNum extends ApmBase
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        $this->startTime = Carbon::now()->subDays(29)->startOfDay()->toDateTimeString();
        $this->endTime = Carbon::now()->endOfDay()->toDateTimeString();
        $list = $this->getListData();
        $data = [];
        // 遍历日期
        $startTime = strtotime($this->startTime);
        $endTime = strtotime($this->endTime);
        while ($startTime < $endTime) {
            $date = date('Y-m-d', $startTime);
            $data[] = [
                'timestamp' => $date,
                'record_total' => intval($list[$date] ?? 0)
            ];
            $startTime += 86400;
        }
        return $data;
    }

    /**
     * 获取列表数据
     *
     * @return array
     */
    protected function getListData(): array
    {
         $res = MysqlApmReportList::query()
            ->selectRaw("DATE({$this->mysql_apm_report_list_table}.created_at) as timestamp, count(*) as record_total")
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where("{$this->mysql_apm_report_list_table}.dev_str", $this->params['dev_str'])
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->groupBy('timestamp')
            ->getFromSR();

        return array_column($res, 'record_total', 'timestamp');
    }
}
