<?php

/**
 * 搜索数据列表类
 *
 * @desc 搜索数据列表类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\StarRocksDB;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ApmSearchList extends ApmBase
{
    /**
     * 分页大小
     *
     * @var int
     */
    protected $perPage = 5;

    /**
     * 获取子查询SQL
     *
     * @return string
     */
    private function getSubSql(): string
    {
        return StarRocksDB::toSql(DB::table($this->mysql_apm_report_list_table)
                ->join($this->mysql_apm_device_list_table, function ($join) {
                    $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                        ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
                })
                ->leftJoin($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id")
                ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
                ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
                ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->params['start_date'], $this->params['end_date']])
                ->when(isset($this->params['os_type']), function (Builder $query) { // 判断是否传入平台
                    return $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
                })
                ->where(function (Builder $query) {
                    return $query->orWhere("{$this->mysql_apm_report_list_table}.dev_str", 'like', "%{$this->params['keywords']}%")
                        ->orWhere(DB::raw("array_join({$this->performance_stat_data_table}.user_id, ',')"), 'like', "%{$this->params['keywords']}%");
                })
                ->groupBy("{$this->mysql_apm_report_list_table}.developer_app_id", "{$this->mysql_apm_report_list_table}.dev_str")
                ->selectRaw("{$this->mysql_apm_report_list_table}.developer_app_id,{$this->mysql_apm_report_list_table}.dev_str,max({$this->mysql_apm_report_list_table}.id) as id"));
    }

    /**
     * 获取主要查询对象
     *
     * @param $subSql
     * @return Builder
     */
    private function getMainQuery($subSql): Builder
    {
        return DB::table(DB::raw("({$subSql}) as t"))
            ->join($this->mysql_apm_report_list_table, 't.id', '=', "{$this->mysql_apm_report_list_table}.id")
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->leftJoin($this->performance_stat_data_table, 't.id', '=', "{$this->performance_stat_data_table}.session_id")
            ->leftJoin($this->performance_score_data_table, 't.id', '=', "{$this->performance_score_data_table}.session_id");
    }

    /**
     * 获取列表
     *
     * @return array
     */
    public function getList(): array
    {
        // 通用查询条件
        $subSql = $this->getSubSql();

        $mainQuery = $this->getMainQuery($subSql);

        // 获取记录总数
        $totalRes = StarRocksDB::query((clone $mainQuery)->selectRaw('count(*) as total'))->first();
        // 转为数字
        $total = (int) $totalRes['total'];

        //没有记录返回空数组
        if (empty($total)) {
            return [
                'list' => [],
                'total' => 0,
            ];
        }

        //获取数据库数据
        $this->getDbList($mainQuery);

        //处理数据
        $this->handleListData();

        // 返回数据
        return [
            'total' => $total,
            'list' => $this->reportList,
        ];
    }

    /**
     * 获取数据库数据
     *
     * @param $mainQuery
     * @return void
     */
    private function getDbList($mainQuery): void
    {
        //查询列表，需要查询的字段
        $selectRaw = <<<EXPRESSION
t.id AS report_id,
t.dev_str AS dev_str,
{$this->performance_stat_data_table}.duration AS duration,
{$this->mysql_apm_device_list_table}.dev_brand AS dev_brand,
{$this->mysql_apm_device_list_table}.dev_model AS dev_model,
{$this->mysql_apm_report_list_table}.app_version_name AS game_version_code,
{$this->performance_score_data_table}.all_score AS all_score,
{$this->mysql_apm_report_list_table}.created_at AS created_at,
{$this->mysql_apm_device_list_table}.os_type AS os_type,
{$this->performance_stat_data_table}.sum_fps / {$this->performance_stat_data_table}.num AS avg_fps,
{$this->performance_stat_data_table}.sum_used_memory / {$this->performance_stat_data_table}.num AS avg_used_memory,
{$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time AS stutter
EXPRESSION;

        $this->reportList = StarRocksDB::query((clone $mainQuery)
                ->selectRaw($selectRaw)
                ->orderBy($this->sortField, $this->sortType)
                ->limit(request('pre_page', $this->perPage))
                ->offset($this->getPageNum())
        )->get(); // 获取记录列表
    }
}
