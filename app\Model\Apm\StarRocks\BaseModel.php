<?php

/**
 * 调用starRocks查询的模型基础类
 */

namespace App\Model\Apm\StarRocks;

use App\Service\StarRocksService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static BaseBuilder query()
 */
class BaseModel extends Model
{
    /**
     * starRocks服务
     *
     * @var StarRocksService
     */
    protected $starRocks;

    /**
     * 构造函数
     *
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        // 初始化starRocks服务
        $this->starRocks = new StarRocksService();
    }

    /**
     * 解析sql
     *
     * @param Builder $query
     * @return string
     */
    protected function getSqlBindings(Builder $query): string
    {
        $queryBindings = $query->getBindings();
        foreach ($queryBindings as $key => $val) {
            if (!is_int($val) && !is_float($val)) {
                $val = "'" . trim($val) . "'";
            }
            $queryBindings[$key] = $val;
        }
        // 因为%使用vsprintf会有问题, 所以需要转义
        $sql = str_replace('%', '%%', $query->toSql());
        // 替换?为%s
        $tmp = str_replace('?', '%s', $sql);
        //生成sql
        $sql = vsprintf($tmp, $queryBindings);
        //替换`performance_stat_data`.`duration` > 为`performance_stat_data`.`num` > 0 and `performance_stat_data`.`duration` > ，因为需要过滤有问题的数据
        $sql = str_replace('`performance_stat_data`.`duration` > ', '`performance_stat_data`.`num` > 0 and `performance_stat_data`.`duration` > ', $sql);
        //返回sql
        return $sql;
    }

    /**
     * 查询全部执行
     *
     * @param Builder $query
     * @return array
     */
    public function scopeGetFromSR(Builder $query): array
    {
        return $this->starRocks->query($this->getSqlBindings($query));
    }

    /**
     * 查询单条执行
     *
     * @param Builder $query
     * @return array
     */
    public function scopeFirstFromSR(Builder $query): array
    {
        $res = $this->starRocks->query($this->getSqlBindings($query));
        return $res[0] ?? [];
    }

    /**
     * 获取SQL语句
     *
     * @param Builder $query
     * @return string
     */
    public function scopeGetSQL(Builder $query): string
    {
        return $this->getSqlBindings($query);
    }
}
