<?php

/**
 * 性能分析对比基类
 * @desc 性能分析对比基类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Contrast;

use App\Model\Apm\StarRocks\BaseBuilder;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Service\Apm\Performance\ApmTrait;
use Illuminate\Database\Query\Builder;

abstract class BaseContrast
{
    use ApmTrait;

    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 开始时间
     *
     * @var string
     */
    protected $startTime;

    /**
     * 结束时间
     *
     * @var string
     */
    protected $endTime;

    /**
     * 排序字段
     *
     * @var string
     */
    protected $sortField = 'num';

    /**
     * 排序类型
     *
     * @var string
     */
    protected $sortType = 'desc';

    /**
     * 分页大小
     *
     * @var int
     */
    protected $perPage = 10;

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取通用的构造器
     *
     * @return BaseBuilder|Builder|mixed
     */
    protected function getCommonBuilder($params)
    {
        return MysqlApmReportList::query() //查询报告表
            ->join($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->performance_score_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_score_data_table}.session_id") //关联报告评分表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->when(!empty($params['start_date']), function ($query) use ($params) {
                return $query->where("{$this->mysql_apm_report_list_table}.created_at", '>=', $params['start_date']);
            })
            ->when(!empty($params['end_date']), function ($query) use ($params) {
                return $query->where("{$this->mysql_apm_report_list_table}.created_at", '<=', $params['end_date']);
            })
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                return $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['device_model']), function ($query) {
                return $query->whereIn("{$this->mysql_apm_device_list_table}.dev_model", explode(',', $this->params['device_model']));
            })
            ->when(!empty($params['game_version_code']), function ($query) use ($params) { //如果有传版本值，过滤掉不在版本范围内的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $params['game_version_code']);
            })
            ->when(!empty($params['inner_version']), function ($query) use ($params) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $params['inner_version']);
            })
            ->when(!empty($this->params['quality']), function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            });
    }

    /**
     * 获取对比列表
     *
     * @return array
     */
    abstract public function getList();

    /**
     * 获取设备对比列表
     *
     * @return array
     */
    abstract public function getDevList();
}
