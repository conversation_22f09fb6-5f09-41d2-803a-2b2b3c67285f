<?php

/**
 * 通用性能检测统计服务类
 * @desc 通用性能检测统计服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

class PerfReportStatService
{
    /**
     * 统计数据
     *
     * @var array
     */
    private $statData;

    /**
     * 初始化
     */
    public function __construct()
    {
        $this->initStatData();
    }

    /**
     * 初始化统计数据
     *
     * @return void
     */
    private function initStatData()
    {
        $this->statData = [
            'fps_value_total' => 0,
            'fps_25_count' => 0,
            'duration_10_min_jank_first' => 0,
            'duration_10_min_jank' => 0,
            'duration_10_min_janks' => [],
            'duration_10_min_janks_last' => [],
            'duration_10_min_big_jank_first' => 0,
            'duration_10_min_big_jank' => 0,
            'duration_10_min_big_janks' => [],
            'duration_10_min_big_janks_last' => [],
            'last_1_frame_cost' => 0,
            'last_2_frame_cost' => 0,
            'last_3_frame_cost' => 0,
            'score_amount_ts' => 0,
            'jank_time_cost' => 0,
            'pss_memory_peak' => 0,
            'total_outside_pss_memory' => 0,
            'available_memory_peak' => 0,
            'total_outside_available_memory' => 0,
            'total_native_heap' => 0,
            'total_dalvik_heap' => 0,
            'total_dalvik_other' => 0,
            'total_other_mmap' => 0,
            'total_egl_mtrack' => 0,
            'total_gl_mtrack' => 0,
            'total_java_heap' => 0,
            'total_code_mem' => 0,
            'total_stack_mem' => 0,
            'total_graphics_mem' => 0,
            'total_private_other' => 0,
            'total_system_mem' => 0,
            'total_unknown_mem' => 0,
            'total_outside_app_cpu' => 0,
            'total_outside_total_cpu' => 0,
            'app_cpu_60_count' => 0,
            'app_cpu_80_count' => 0,
            'total_outside_battery_current' => 0,
            'battery_current_peak' => 0,
            'total_outside_battery_power' => 0,
            'battery_power_peak' => 0,
            'total_outside_battery_temperature' => 0,
            'battery_temperature_peak' => 0,
            'total_outside_cpu_temperature' => 0,
            'cpu_temperature_peak' => 0,
            'total_outside_gpu_temperature' => 0,
            'total_outside_npu_temperature' => 0,
            'total_outside_network_send' => 0,
            'total_outside_network_recv' => 0,
            'duration_10_min_network_send_first' => 0,
            'duration_10_min_network_send' => 0,
            'duration_10_min_network_sends' => [],
            'duration_10_min_network_sends_last ' => [],
            'duration_10_min_network_recv_first' => 0,
            'duration_10_min_network_recv' => 0,
            'duration_10_min_network_recvs' => [],
            'duration_10_min_network_recvs_last' => [],
            'total_outside_gpu_usage' => 0,
            'total_outside_gpu_counter_fragment' => 0,
            'total_outside_gpu_counter_last_level' => 0,
            'total_outside_gpu_counter_read_band_width' => 0,
            'total_outside_gpu_counter_write_band_width' => 0,
        ];
    }

    /**
     * 分析统计
     *
     * @param array $source
     * @param $platform
     * @return array
     */
    public function analyze(array $source, $platform): array
    {
        $duration_10_min = 10 * 60 * 1000;
        $total_outside = count($source);
        foreach ($source as $item) {
            if (!is_array($item)) {
                continue;
            }
            $ts = isset($item['ts']) ? (int)$item['ts'] : 0;
            $this->statData['current_cost'] = 0;
            // FPS
            $this->processFpsInfo($item, $ts, $duration_10_min);
            $this->statData['score_amount_ts'] += $this->statData['current_cost'];
            //frameTimes 数值为0的情况
            if ($this->statData['score_amount_ts'] == 0) {
                $this->statData['score_amount_ts'] += 1000;
            }
            // Memory
            $this->processMemoryInfo($item);
            // MemoryDetail
            $this->processMemoryDetailInfo($item);
            // CPU
            $this->processCpuInfo($item);
            // GPU
            $this->processGpuUsage($item, $platform);
            // GPU Counter
            $this->processGpuCounterInfo($item);
            // Battery
            $this->processBatteryInfo($item);
            // Temperature
            $this->processTemperature($item);
            // Network
            $this->processNetworkInfo($item, $ts, $duration_10_min);
        }

        $this->handleData($total_outside);

        return $this->getStatistics($total_outside);
    }

    /**
     * 处理 FPS 信息
     *
     * @param $item
     * @param $ts
     * @param $duration_10_min
     */
    private function processFpsInfo($item, $ts, $duration_10_min)
    {
        $movie_frame_cost = (1 / 24) * 1000;
        if (isset($item['fpsInfo']) && is_array($item['fpsInfo'])) {
            $fps_info = $item['fpsInfo'];
            $frame_times = $fps_info['FrameTimes'] ?? null;
            $tmp_jank_time_cost = $fps_info['JankTimeCost'] ?? null;
            $tmp_current_cost = $fps_info['FPSTimeCost'] ?? null;

            if (!$tmp_jank_time_cost) {
                if (is_array($frame_times)) {
                    $last_1_frame_cost = $last_2_frame_cost = $last_3_frame_cost = 0;
                    foreach ($frame_times as $frame_index => $this_frame_cost) {
                        if ($frame_index < 3) {
                            $jank_condition_b = $movie_frame_cost * 2;
                            if ($this_frame_cost > $jank_condition_b) {
                                $this->statData['jank_time_cost'] += $this_frame_cost;
                            }
                        } else {
                            $first_three_frames_avg = array_sum([$last_3_frame_cost, $last_2_frame_cost, $last_1_frame_cost]) / 3;
                            $jank_condition_a = $first_three_frames_avg * 2;
                            $jank_condition_b = $movie_frame_cost * 2;
                            if ($this_frame_cost > $jank_condition_a && $this_frame_cost > $jank_condition_b) {
                                $this->statData['jank_time_cost'] += $this_frame_cost;
                            }
                        }
                        $last_3_frame_cost = $last_2_frame_cost;
                        $last_2_frame_cost = $last_1_frame_cost;
                        $last_1_frame_cost = $this_frame_cost;
                    }
                }
            } else {
                $this->statData['jank_time_cost'] += $tmp_jank_time_cost;
            }

            if (!$tmp_current_cost) {
                if (is_array($frame_times)) {
                    $this->statData['current_cost'] = array_sum($frame_times);
                }
            } else {
                $this->statData['current_cost'] = $tmp_current_cost;
            }

            $tmp_fps = $fps_info['FPS'] ?? 0;
            $this->statData['fps_value_total'] += $tmp_fps;
            if ($tmp_fps > 25) {
                $this->statData['fps_25_count']++;
            }

            $tmp_jank = $fps_info['Jank'] ?? 0;
            $this->statData['duration_10_min_janks_last'][] = $tmp_jank;
            $this->statData['duration_10_min_jank'] += $tmp_jank;
            if ($this->statData['duration_10_min_jank_first'] == 0) {
                $this->statData['duration_10_min_jank_first'] = $ts;
            } else {
                if ($ts - $this->statData['duration_10_min_jank_first'] >= $duration_10_min) {
                    $this->statData['duration_10_min_janks'][] = $this->statData['duration_10_min_jank'];
                    $this->statData['duration_10_min_jank'] = 0;
                    $this->statData['duration_10_min_jank_first'] = 0;
                    $this->statData['duration_10_min_janks_last'] = [];
                }
            }

            $tmp_big_jank = $fps_info['BigJank'] ?? 0;
            $this->statData['duration_10_min_big_janks_last'][] = $tmp_big_jank;
            $this->statData['duration_10_min_big_jank'] += $tmp_big_jank;
            if ($this->statData['duration_10_min_big_jank_first'] == 0) {
                $this->statData['duration_10_min_big_jank_first'] = $ts;
            } else {
                if (($ts - $this->statData['duration_10_min_big_jank_first']) >= $duration_10_min) {
                    $this->statData['duration_10_min_big_janks'][] = $this->statData['duration_10_min_big_jank'];
                    $this->statData['duration_10_min_big_jank'] = 0;
                    $this->statData['duration_10_min_big_jank_first'] = 0;
                    $this->statData['duration_10_min_big_janks_last'] = [];
                }
            }
        }
    }

    /**
     * 处理内存信息
     *
     * @param $item
     */
    private function processMemoryInfo($item)
    {
        if (isset($item['memoryInfo']) && is_array($item['memoryInfo'])) {
            $memory_info = $item['memoryInfo'];
            $pss = $memory_info['Memory'] ?? 0;
            $available = $memory_info['AvailableMemory'] ?? 0;
            $this->statData['total_outside_pss_memory'] += $pss;
            if ($this->statData['pss_memory_peak'] < $pss) {
                $this->statData['pss_memory_peak'] = $pss;
            }
            $this->statData['total_outside_available_memory'] += $available;
            if ($this->statData['available_memory_peak'] < $available) {
                $this->statData['available_memory_peak'] = $available;
            }
        }
    }

    /**
     * 处理内存详细信息
     *
     * @param $item
     */
    private function processMemoryDetailInfo($item)
    {
        if (isset($item['memoryDetailInfo']) && is_array($item['memoryDetailInfo'])) {
            $memory_detail_info = $item['memoryDetailInfo'];
            $this->statData['total_native_heap'] += ($memory_detail_info['Native_Heap'] ?? 0);
            $this->statData['total_dalvik_heap'] += ($memory_detail_info['Dalvik_Heap'] ?? 0);
            $this->statData['total_dalvik_other'] += ($memory_detail_info['Dalvik_Other'] ?? 0);
            $this->statData['total_other_mmap'] += ($memory_detail_info['Other_mmap'] ?? 0);
            $this->statData['total_egl_mtrack'] += ($memory_detail_info['EGL_mtrack'] ?? 0);
            $this->statData['total_gl_mtrack'] += ($memory_detail_info['GL_mtrack'] ?? 0);
            $this->statData['total_java_heap'] += ($memory_detail_info['Java_Heap'] ?? 0);
            $this->statData['total_code_mem'] += ($memory_detail_info['Code'] ?? 0);
            $this->statData['total_stack_mem'] += ($memory_detail_info['Stack'] ?? 0);
            $this->statData['total_graphics_mem'] += ($memory_detail_info['Graphics'] ?? 0);
            $this->statData['total_private_other'] += ($memory_detail_info['Private_Other'] ?? 0);
            $this->statData['total_system_mem'] += ($memory_detail_info['System'] ?? 0);
            $this->statData['total_unknown_mem'] += ($memory_detail_info['Unknown'] ?? 0);
        }
    }

    /**
     * 处理CPU信息
     *
     * @param $item
     */
    private function processCpuInfo($item)
    {
        if (isset($item['cpuInfo']) && is_array($item['cpuInfo'])) {
            $cpu_info = $item['cpuInfo'];
            $app_cpu = $cpu_info['AppCPU'] ?? 0;
            $total_cpu = $cpu_info['TotalCPU'] ?? 0;
            $this->statData['total_outside_app_cpu'] += $app_cpu;
            $this->statData['total_outside_total_cpu'] += $total_cpu;
            if ($app_cpu <= 60) {
                $this->statData['app_cpu_60_count']++;
            }
            if ($app_cpu <= 80) {
                $this->statData['app_cpu_80_count']++;
            }
        }
    }

    /**
     * 处理GPU计数器信息
     *
     * @param $item
     * @param $platform
     */
    private function processGpuUsage($item, $platform)
    {
        if (isset($item['gpuInfo']) && is_array($item['gpuInfo'])) {
            $gpu_info = $item['gpuInfo'];
            $gpu_usage = ($platform == 1) ? ($gpu_info['DeviceUtilization'] ?? 0) : ($gpu_info['GpuUtilization'] ?? 0);
            $this->statData['total_outside_gpu_usage'] += $gpu_usage;
        }
    }

    /**
     * 处理GPU计数器信息
     *
     * @param $item
     */
    private function processGpuCounterInfo($item)
    {
        if (isset($item['gpuCounterInfo']) && is_array($item['gpuCounterInfo'])) {
            $gpu_counter_info = $item['gpuCounterInfo'];
            $this->statData['total_outside_gpu_counter_fragment'] += ($gpu_counter_info['FragmentInputInterpolationLimiter'] ?? 0);
            $this->statData['total_outside_gpu_counter_last_level'] += ($gpu_counter_info['GPULastLevelCacheLimiter'] ?? 0);
            $this->statData['total_outside_gpu_counter_read_band_width'] += ($gpu_counter_info['GPUReadBandwidth'] ?? 0);
            $this->statData['total_outside_gpu_counter_write_band_width'] += ($gpu_counter_info['GPUWriteBandwidth'] ?? 0);
        }
    }

    /**
     * 处理电池信息
     *
     * @param $item
     */
    private function processBatteryInfo($item)
    {
        if (isset($item['batteryInfo']) && is_array($item['batteryInfo'])) {
            $battery_info = $item['batteryInfo'];
            $tmp_current = $battery_info['Current'] ?? 0;
            $tmp_power = $battery_info['Power'] ?? 0;
            $this->statData['total_outside_battery_current'] += $tmp_current;
            if ($this->statData['battery_current_peak'] < $tmp_current) {
                $this->statData['battery_current_peak'] = $tmp_current;
            }
            $this->statData['total_outside_battery_power'] += $tmp_power;
            if ($this->statData['battery_power_peak'] < $tmp_power) {
                $this->statData['battery_power_peak'] = $tmp_power;
            }
        }
    }

    /**
     * 处理Temperature
     *
     * @param $item
     * @return void
     */
    private function processTemperature($item)
    {
        if (isset($item['temperatureInfo']) && is_array($item['temperatureInfo'])) {
            $temperature_info = $item['temperatureInfo'];
            $cpu_temp = $temperature_info['CTemp'] ?? 0;
            $battery_temp = $temperature_info['BTemp'] ?? 0;
            $this->statData['total_outside_cpu_temperature'] += $cpu_temp;
            $this->statData['total_outside_battery_temperature'] += $battery_temp;
            $this->statData['total_outside_gpu_temperature'] += ($temperature_info['GTemp'] ?? 0);
            $this->statData['total_outside_npu_temperature'] += ($temperature_info['NTemp'] ?? 0);
            if ($this->statData['cpu_temperature_peak'] < $cpu_temp) {
                $this->statData['cpu_temperature_peak'] = $cpu_temp;
            }
            if ($this->statData['battery_temperature_peak'] < $battery_temp) {
                $this->statData['battery_temperature_peak'] = $battery_temp;
            }
        }
    }

    /**
     * 处理网络信息的函数
     *
     * @param array $item 包含网络信息的数据项
     * @param float $ts 当前时间戳
     * @param int $duration_10_min 10分钟的持续时间（以秒为单位）
     */
    function processNetworkInfo($item, $ts, $duration_10_min)
    {
        if (isset($item['networkInfo']) && is_array($item['networkInfo'])) {
            $network_info = $item['networkInfo'];
            $send = $network_info['Send'] ?? 0;
            $recv = $network_info['Recv'] ?? 0;
            $this->statData['total_outside_network_send'] += $send;
            $this->statData['total_outside_network_recv'] += $recv;
            $this->statData['duration_10_min_network_sends_last'][] = $send;
            $this->statData['duration_10_min_network_send'] += $send;
            if ($this->statData['duration_10_min_network_send_first'] == 0) {
                $this->statData['duration_10_min_network_send_first'] = $ts;
            } else {
                if ($ts - $this->statData['duration_10_min_network_send_first'] >= $duration_10_min) {
                    $this->statData['duration_10_min_network_sends'][] = $this->statData['duration_10_min_network_send'];
                    $this->statData['duration_10_min_network_send'] = 0;
                    $this->statData['duration_10_min_network_send_first'] = 0;
                    $this->statData['duration_10_min_network_sends_last'] = [];
                }
            }
            $this->statData['duration_10_min_network_recvs_last'][] = $recv;
            $this->statData['duration_10_min_network_recv'] += $recv;
            if ($this->statData['duration_10_min_network_recv_first'] == 0) {
                $this->statData['duration_10_min_network_recv_first'] = $ts;
            } else {
                if ($ts - $this->statData['duration_10_min_network_recv_first'] >= $duration_10_min) {
                    $this->statData['duration_10_min_network_recvs'][] = $this->statData['duration_10_min_network_recv'];
                    $this->statData['duration_10_min_network_recv'] = 0;
                    $this->statData['duration_10_min_network_recv_first'] = 0;
                    $this->statData['duration_10_min_network_recvs_last'] = [];
                }
            }
        }
    }

    /**
     * 获取统计信息
     *
     * @param $total_outside
     * @return array
     */
    private function getStatistics($total_outside): array
    {
        return [
            'avgFps' => bcadd(round($this->statData['fps_value_total'] / $total_outside, 2), 0, 2),
            'more25Fps' => bcadd(0, round($this->statData['more_25_fps'], 2), 2),
            'less25Fps' => bcadd(0, round((100 - $this->statData['more_25_fps']), 2), 2),
            'avg10Jank' => bcadd(0, round($this->statData['avg_10_Jank'], 2), 2),
            'avg10BigJank' => bcadd(0, round($this->statData['avg_10_big_Jank'], 2), 2),
            'stutter' => $this->statData['amount_stutter'],
            // Memory Info
            'pssPeak' => $this->statData['pss_memory_peak'],
            'avgPss' => bcadd(0, round($this->statData['total_outside_pss_memory'] / $total_outside, 2), 2),
            'availablePeak' => $this->statData['available_memory_peak'],
            'avgAvailable' => bcadd(0, round($this->statData['total_outside_available_memory'] / $total_outside, 2), 2),
            // Memory Detail
            'avgNative_Heap' => bcadd(0, round($this->statData['total_native_heap'] / $total_outside, 2), 2),
            'avgDalvik_Heap' => bcadd(0, round($this->statData['total_dalvik_heap'] / $total_outside, 2), 2),
            'avgDalvik_Other' => bcadd(0, round($this->statData['total_dalvik_other'] / $total_outside, 2), 2),
            'avgOther_Mmap' => bcadd(0, round($this->statData['total_other_mmap'] / $total_outside, 2), 2),
            'avgEGL_mtrack' => bcadd(0, round($this->statData['total_egl_mtrack'] / $total_outside, 2), 2),
            'avgGL_mtrack' => bcadd(0, round($this->statData['total_gl_mtrack'] / $total_outside, 2), 2),
            'avgJava_Heap' => bcadd(0, round($this->statData['total_java_heap'] / $total_outside, 2), 2),
            'avgCode' => bcadd(0, round($this->statData['total_code_mem'] / $total_outside, 2), 2),
            'avgStack' => bcadd(0, round($this->statData['total_stack_mem'] / $total_outside, 2), 2),
            'avgGraphics' => bcadd(0, round($this->statData['total_graphics_mem'] / $total_outside, 2), 2),
            'avgPrivate_Other' => bcadd(0, round($this->statData['total_private_other'] / $total_outside, 2), 2),
            'avgSystem' => bcadd(0, round($this->statData['total_system_mem'] / $total_outside, 2), 2),
            'avgUnknown' => bcadd(0, round($this->statData['total_unknown_mem'] / $total_outside, 2), 2),
            // App CPU
            'avgAppCpu' => bcadd(0, round($this->statData['total_outside_app_cpu'] / $total_outside, 2), 2),
            'avgTotalCpu' => bcadd(0, round($this->statData['total_outside_total_cpu'] / $total_outside, 2), 2),
            'less60AppCpu' => bcadd(0, round($this->statData['less_60_app_cpu'], 2), 2),
            'less80AppCpu' => bcadd(0, round($this->statData['less_80_app_cpu'], 2), 2),
            // GPU
            'avgGpuUsage' => bcadd(0, round($this->statData['total_outside_gpu_usage'] / $total_outside, 2), 2),
            // GPU Counter
            'avgFragmentInputInterpolationLimiter' => bcadd(0, round($this->statData['total_outside_gpu_counter_fragment'] / $total_outside, 2), 2),
            'avgGPULastLevelCacheLimiter' => bcadd(0, round($this->statData['total_outside_gpu_counter_last_level'] / $total_outside, 2), 2),
            'avgGPUReadBandwidth' => bcadd(0, round($this->statData['total_outside_gpu_counter_read_band_width'] / $total_outside, 2), 2),
            'avgGPUWriteBandwidth' => bcadd(0, round($this->statData['total_outside_gpu_counter_write_band_width'] / $total_outside, 2), 2),
            // Battery
            'usedElectricity' => $this->statData['total_outside_battery_power'],
            'currentPeak' => $this->statData['battery_current_peak'],
            'avgCurrent' => bcadd(0, round($this->statData['total_outside_battery_current'] / $total_outside, 2), 2),
            'powerPeak' => $this->statData['battery_power_peak'],
            'avgPower' => bcadd(0, round($this->statData['total_outside_battery_power'] / $total_outside, 2), 2),
            // Temperature
            'avgBatteryTemp' => bcadd(0, round($this->statData['total_outside_battery_temperature'] / $total_outside, 2), 2),
            'avgCpuTemp' => bcadd(0, round($this->statData['total_outside_cpu_temperature'] / $total_outside, 2), 2),
            'avgGpuTemp' => bcadd(0, round($this->statData['total_outside_gpu_temperature'] / $total_outside, 2), 2),
            'avgNpuTemp' => bcadd(0, round($this->statData['total_outside_npu_temperature'] / $total_outside, 2), 2),
            'batteryTempPeak' => $this->statData['battery_temperature_peak'],
            'cpuTempPeak' => $this->statData['cpu_temperature_peak'],
            // Network
            'avgSend' => bcadd(0, round($this->statData['total_outside_network_send'] / $total_outside, 2), 2),
            'sumSend' => bcadd(0, round($this->statData['total_outside_network_send'], 2), 2),
            'avg10Send' => bcadd(0, round($this->statData['avg_10_send'], 2), 2),
            'avgRecv' => bcadd(0, round($this->statData['total_outside_network_recv'] / $total_outside, 2), 2),
            'sumRecv' => bcadd(0, round($this->statData['total_outside_network_recv'], 2), 2),
            'avg10Recv' => bcadd(0, round($this->statData['avg_10_recv'], 2), 2),
        ];
    }

    /**
     * 处理数据
     *
     * @return void
     */
    private function handleData($total_outside)
    {
        $this->statData['more_25_fps'] = $this->statData['fps_25_count'] / $total_outside * 100;
        $this->statData['amount_stutter'] = $this->statData['jank_time_cost'] / $this->statData['score_amount_ts'] * 100;
        $this->statData['avg_10_Jank'] = 0;
        if (count($this->statData['duration_10_min_janks_last']) > 0) {
            $this->statData['duration_10_min_janks'][] = array_sum($this->statData['duration_10_min_janks_last']);
        }
        if (count($this->statData['duration_10_min_janks']) > 0) {
            $this->statData['avg_10_Jank'] = array_sum($this->statData['duration_10_min_janks']) / count($this->statData['duration_10_min_janks']);
        }
        $this->statData['avg_10_big_Jank'] = 0;
        if (count($this->statData['duration_10_min_big_janks_last']) > 0) {
            $this->statData['duration_10_min_big_janks'][] = array_sum($this->statData['duration_10_min_big_janks_last']);
        }
        if (count($this->statData['duration_10_min_big_janks']) > 0) {
            $this->statData['avg_10_big_Jank'] = array_sum($this->statData['duration_10_min_big_janks']) / count($this->statData['duration_10_min_big_janks']);
        }
        $this->statData['less_60_app_cpu'] = $this->statData['app_cpu_60_count'] / $total_outside * 100;
        $this->statData['less_80_app_cpu'] = $this->statData['app_cpu_80_count'] / $total_outside * 100;
        $this->statData['avg_10_send'] = 0;
        if (count($this->statData['duration_10_min_network_sends_last']) > 0) {
            $this->statData['duration_10_min_network_sends'][] = array_sum($this->statData['duration_10_min_network_sends_last']);
        }
        if (count($this->statData['duration_10_min_network_sends']) > 0) {
            $this->statData['avg_10_send'] = array_sum($this->statData['duration_10_min_network_sends']) / count($this->statData['duration_10_min_network_sends']);
        }
        $this->statData['avg_10_recv'] = 0;
        if (count($this->statData['duration_10_min_network_recvs_last']) > 0) {
            $this->statData['duration_10_min_network_recvs'][] = array_sum($this->statData['duration_10_min_network_recvs_last']);
        }
        if (count($this->statData['duration_10_min_network_recvs']) > 0) {
            $this->statData['avg_10_recv'] = array_sum($this->statData['duration_10_min_network_recvs']) / count($this->statData['duration_10_min_network_recvs']);
        }
    }
}
