<?php

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class GameInfoChart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'gc_used_memory',
                'gfx_used_memory',
                'total_used_memory',
                'asset_count',
                'game_object_count',
                'setpass_calls_count',
                'triangles_count',
                'batches_count',
                'shadow_casters_count',
                'used_textures_count',
                'lua_memory',
                'animationclip_memory',
                'texture_memory',
                'mono_used_size',
                'game_info_ts',
                'tags_info',
                'app_state',
                'perf_data_ts'
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['gameInfo'][] = [  //按照时间戳分组
                'memory_bytes' => [ //内存信息
                    'gc_used_memory' => $item['gc_used_memory'],    //gc内存
                    'gfx_used_memory' => $item['gfx_used_memory'],  //gfx内存
                    'total_used_memory' => $item['total_used_memory'],  //总内存
                    'lua_memory' => $item['lua_memory'],    //lua内存
                    'animationclip_memory' => $item['animationclip_memory'],    //动画内存
                    'texture_memory' => $item['texture_memory'],    //纹理内存
                    'mono_used_size' => $item['mono_used_size'],    //mono内存
                ],
                'memory_count' => [ //内存数量
                    'asset_count' => $item['asset_count'],  //资源数量
                    'game_object_count' => $item['game_object_count'],  //游戏对象数量
                ],
                'render_count' => [ //渲染数量
                    'setpass_calls_count' => $item['setpass_calls_count'],  //setpass数量
                    'triangles_count' => $item['triangles_count'],  //三角形数量
                    'batches_count' => $item['batches_count'],  //批次数量
                    'shadow_casters_count' => $item['shadow_casters_count'],    //阴影数量
                    'used_textures_count' => $item['used_textures_count'],  //纹理数量
                ],
                'ts' => $item['game_info_ts'],  //游戏时间戳
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }

        return array_values($list);
    }
}
