<?php

/**
 * 分析对比校验类
 * @desc 分析对比校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/06
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;

/**
 * @method static ContrastValidation build()
 */
class ContrastValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): ContrastValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 平台校验
     *
     * @return $this
     */
    public function osType(): ContrastValidation
    {
        $this->rules['os_type'] = 'string';
        return $this;
    }

    /**
     * 是否模拟器校验
     *
     * @return $this
     */
    public function isSimulator(): ContrastValidation
    {
        $this->rules['is_simulator'] = 'nullable|integer';
        return $this;
    }

    /**
     * 设备挡位校验
     *
     * @return $this
     */
    public function deviceTier(): ContrastValidation
    {
        $this->rules['device_tier'] = 'nullable|integer';
        return $this;
    }

    /**
     * 设备型号
     *
     * @return $this
     */
    public function deviceModel(): ContrastValidation
    {
        $this->rules['device_model'] = 'nullable|string';
        return $this;
    }

    /**
     * 画质
     *
     * @return $this
     */
    public function quality(): ContrastValidation
    {
        $this->rules['quality'] = 'nullable|string';
        return $this;
    }

    /**
     * 对比数据
     *
     * @return $this
     */
    public function gameVersionContrastData(): ContrastValidation
    {
        $this->rules['contrast_data'] = 'array';
        $this->rules['contrast_data.*.game_version_code'] = 'required|string';
        $this->rules['contrast_data.*.inner_version'] = 'nullable|string';
        $this->rules['contrast_data.*.start_date'] = 'nullable|string';
        $this->rules['contrast_data.*.end_date'] = 'nullable|string';
        return $this;
    }

    /**
     * 标签对比数据
     *
     * @return $this
     */
    public function tagContrastData(): ContrastValidation
    {
        $this->rules['contrast_data'] = 'array';
        $this->rules['contrast_data.*.tag'] = 'required|string';
        $this->rules['contrast_data.*.game_version_code'] = 'nullable|string';
        $this->rules['contrast_data.*.start_date'] = 'nullable|string';
        $this->rules['contrast_data.*.end_date'] = 'nullable|string';
        return $this;
    }
}
