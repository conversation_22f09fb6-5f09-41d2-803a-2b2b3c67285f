<?php

/**
 * 性能趋势统计
 * @desc 性能趋势统计
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/02/18
 */

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceStatData;
use Carbon\Carbon;

class PerfTrend extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        $list = array_column($this->getStatData(), null, 'date');

        // 判断开始时间和结束时间的范围，如果是一天内，统计维度按一个小时，5天内，统计维度按6个小时，5天以上，统计维度按天
        // 通过Carbon类判断时间间隔，获取统计维度
        $diff = Carbon::parse($this->params['start_time'])->diffInDays($this->params['end_time']);

        switch ($diff) {
            case 0:
                $data = $this->calcData($list);
                $dateList = $this->getHourList();
                break;
            case $diff <= 4:
                $data = $this->calcData($list, false, true);
                $dateList = $this->getSixHourList();
                break;
            default:
                $data = $this->calcData($list, true);
                $dateList = $this->getDayList();
                break;
        }
        // 填充数据
        return $this->fillData($data, $dateList);
    }

    /**
     * 获取统计数据
     *
     * @return array
     */
    protected function getStatData(): array
    {
        //获取日期、cpu、内存、fps、卡顿、帧率、分数、次数、评分次数
        $selectRaw = <<<COLUMNS
DATE_FORMAT({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d %H:00:00') as date,
sum({$this->performance_stat_data_table}.sum_cpu_usage) as cpu,
sum({$this->performance_stat_data_table}.sum_cpu_total_usage) as cpu_total,
sum({$this->performance_stat_data_table}.sum_used_memory) as memory,
sum({$this->performance_stat_data_table}.max_used_memory) as max_memory,
sum({$this->performance_stat_data_table}.sum_mono_used_size / {$this->performance_stat_data_table}.num) as mono_used_size,
sum({$this->performance_stat_data_table}.sum_fps) as fps,
sum({$this->performance_stat_data_table}.sum_lua_memory) as lua_memory,
sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) as sum_stutter,
sum({$this->performance_stat_data_table}.sum_battery_power / {$this->performance_stat_data_table}.sum_fps) as fps_power,
sum({$this->performance_stat_data_table}.big_jank_count_10) as sum_big_jank,
sum({$this->performance_stat_data_table}.fps_jitter_count_10) as sum_fps_jitter,
sum({$this->performance_score_data_table}.all_score) as score,
sum({$this->performance_stat_data_table}.num) as num,
count({$this->performance_stat_data_table}.session_id) as score_num,
COUNT(CASE WHEN {$this->performance_stat_data_table}.sum_battery_power > 0 THEN 1 END) as fps_power_num,
COUNT(CASE WHEN {$this->performance_stat_data_table}.sum_mono_used_size > 0 THEN 1 END) as mono_used_size_num
COLUMNS;

        return PerformanceStatData::query()
            ->selectRaw($selectRaw)
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //连接报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //连接设备表
                $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str");
            })
            ->join($this->performance_score_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //连接分数表
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小耗时的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前应用的数据
            ->when(isset($this->params['os_type']), function ($query) { //过滤掉不是当前平台的数据
                return $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(isset($this->params['game_version_code']), function ($query) { //过滤掉不是当前版本的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->where("{$this->performance_stat_data_table}.jank_count_10", '<=', 2000)
            ->groupByRaw("DATE_FORMAT({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d %H:00:00')")
            ->getFromSR();
    }

    /**
     * 获取天列表
     *
     * @return array
     */
    protected function getDayList(): array
    {
        $list = [];
        $start = strtotime($this->startTime);
        $end = strtotime($this->endTime);
        while ($start <= $end) {
            $list[] = date('Y-m-d', $start);
            $start += 86400;
        }
        return $list;
    }

    /**
     * 获取小时列表
     *
     * @return array
     */
    protected function getHourList(): array
    {
        $list = [];
        $start = strtotime($this->startTime);
        $end = strtotime($this->endTime);
        while ($start <= $end) {
            //判断时间大于当前时间则跳出
            if ($start > time()) {
                break;
            }
            $list[] = date('Y-m-d H:00:00', $start);
            $start += 3600;
        }
        return $list;
    }

    /**
     * 获取6小时列表
     *
     * @return array
     */
    protected function getSixHourList(): array
    {
        $list = [];
        $start = strtotime($this->startTime);
        $end = strtotime($this->endTime);
        while ($start <= $end) {
            //判断时间大于当前时间则跳出
            if ($start > time()) {
                break;
            }
            $list[] = date('Y-m-d H:00:00', $start);
            $start += 3600 * 6;
        }
        return $list;
    }

    /**
     * 计算数据
     *
     * @param array $list 原始数据
     * @param bool $isDay 是否按天统计
     * @param bool $isSixHour 是否6小时统计
     * @return array
     */
    protected function calcData(array $list, bool $isDay = false, bool $isSixHour = false): array
    {
        //按日期归类数据
        $data = [];
        foreach ($list as $item) {
            $carbon = Carbon::parse($item['date']);
            if ($isDay) {
                $date = $carbon->toDateString();
            } else {
                if ($isSixHour) {
                    // 判断当前小时，以6小时为统计维度，看归属哪个小时
                    $hour = $carbon->hour;
                    $date = $carbon->toDateString();
                    if ($hour < 6) {
                        $date .= ' 00:00:00';
                    } elseif ($hour < 12) {
                        $date .= ' 06:00:00';
                    } elseif ($hour < 18) {
                        $date .= ' 12:00:00';
                    } else {
                        $date .= ' 18:00:00';
                    }
                } else {
                    $date = $carbon->toDateTimeString();
                }
            }
            $cpu = $data[$date]['cpu'] ?? 0;
            $cpuTotal = $data[$date]['cpu_total'] ?? 0;
            $memory = $data[$date]['memory'] ?? 0;
            $luaMemory = $data[$date]['lua_memory'] ?? 0;
            $fps = $data[$date]['fps'] ?? 0;
            $stutter = $data[$date]['sum_stutter'] ?? 0;
            $fpsPower = $data[$date]['fps_power'] ?? 0;
            $score = $data[$date]['score'] ?? 0;
            $bigJank = $data[$date]['big_jank'] ?? 0;
            $maxMemory = $data[$date]['max_memory'] ?? 0;
            $monoUsedSize = $data[$date]['mono_used_size'] ?? 0;
            $num = $data[$date]['num'] ?? 0;
            $scoreNum = $data[$date]['score_num'] ?? 0;
            $fpsJitter = $data[$date]['fps_jitter'] ?? 0;
            $fpsPowerNum = $data[$date]['fps_power_num'] ?? 0;
            $monoUsedSizeNum = $data[$date]['mono_used_size_num'] ?? 0;
            // 过滤掉没有数据的日期
            if ($item['num'] == 0) {
                continue;
            }

            $data[$date] = [
                'date' => $date,
                'cpu' => bcadd($cpu, sprintf('%f', $item['cpu']), 2),
                'cpu_total' => bcadd($cpuTotal, sprintf('%f', $item['cpu_total']), 2),
                'memory' => bcadd($memory, sprintf('%f', $item['memory']), 2),
                'fps' => bcadd($fps, sprintf('%f', $item['fps']), 2),
                'sum_stutter' => bcadd($stutter, bcmul(sprintf('%f', $item['sum_stutter']), 100, 4), 2),
                'fps_power' => bcadd($fpsPower, sprintf('%f', $item['fps_power']), 2),
                'big_jank' => bcadd($bigJank, sprintf('%f', $item['sum_big_jank']), 2),
                'fps_jitter' => bcadd($fpsJitter, sprintf('%f', $item['sum_fps_jitter']), 2),
                'max_memory' => bcadd($maxMemory, sprintf('%f', $item['max_memory']), 2),
                'lua_memory' => bcadd($luaMemory, sprintf('%f', $item['lua_memory']), 2),
                'num' => bcadd($num, $item['num'], 0),
                'score' => bcadd($score, sprintf('%f', $item['score']), 2),
                'score_num' => bcadd($scoreNum, $item['score_num'], 0),
                'fps_power_num' => bcadd($fpsPowerNum, $item['fps_power_num'], 0),
                'mono_used_size' => bcadd($monoUsedSize, sprintf('%f', $item['mono_used_size']), 2),
                'mono_used_size_num' => bcadd($monoUsedSizeNum, $item['mono_used_size_num'], 0),
            ];
        }
        return $data;
    }

    /**
     * 填充数据
     *
     * @param array $data
     * @param array $dateList
     * @return array
     */
    public function fillData(array $data, array $dateList): array
    {
        //数据填充
        $newList = [];
        foreach ($dateList as $date) {
            $item = $data[$date] ?? [
                'cpu' => 0,
                'memory' => 0,
                'cpu_total' => 0,
                'lua_memory' => 0,
                'fps' => 0,
                'sum_stutter' => 0,
                'fps_power' => 0,
                'big_jank' => 0,
                'fps_jitter' => 0,
                'max_memory' => 0,
                'score' => 0,
                'num' => 1,
                'score_num' => 1,
                'fps_power_num' => 1,
                'mono_used_size' => 0,
                'mono_used_size_num' => 1,
            ];
            $newList[] = [
                'date' => $date,
                'cpu' => bcdiv($item['cpu'], $item['num'], 2),
                'cpu_total' => bcdiv($item['cpu_total'], $item['num'], 2),
                'memory' => bcdiv($item['max_memory'], $item['score_num'], 2),
                'fps' => bcdiv($item['fps'], $item['num'], 2),
                'lua_memory' => bcdiv($item['lua_memory'], $item['num'], 2),
                'stutter' => bcdiv($item['sum_stutter'], $item['score_num'], 2),
                'fps_power' => bcdiv($item['fps_power'], $item['fps_power_num'] > 0 ? $item['fps_power_num'] : 1, 2),
                'score' => bcdiv($item['score'], $item['score_num'], 2),
                'big_jank' => bcdiv($item['big_jank'], $item['score_num'], 2),
                'fps_jitter' => bcdiv($item['fps_jitter'], $item['score_num'], 2),
                'max_memory' => bcdiv($item['max_memory'], $item['score_num'], 2),
                'mono_used_size' => bcdiv($item['mono_used_size'], $item['mono_used_size_num'] > 0 ? $item['mono_used_size_num'] : 1, 2),
            ];
        }

        return $newList;
    }
}
