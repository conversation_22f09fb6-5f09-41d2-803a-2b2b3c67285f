<?php

/**
 * 性能分析对比
 * @desc 性能分析对比
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/08/06
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\ContrastValidation;
use App\Service\Apm\Contrast\GameCodeVersion;
use App\Service\Apm\Contrast\Tag;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class ApmPerformanceContrastController extends Controller
{
    /**
     *
     * 版本对比分析
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3758
     * @return JsonResponse
     */
    public function gameCodeVersion(): JsonResponse
    {
        $params = ContrastValidation::build()
            ->developerAppId()->osType()->isSimulator()->deviceTier()
            ->deviceModel()->quality()->gameVersionContrastData()->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new GameCodeVersion($params))->getList());
        } catch (Exception $e) {
            Log::error('线上玩家性能版本对比分析接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     *
     * 版本对比分析
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3758
     * @return JsonResponse
     */
    public function gameCodeVersionDev(): JsonResponse
    {
        $params = ContrastValidation::build()
            ->developerAppId()->osType()->isSimulator()->deviceTier()
            ->deviceModel()->quality()->gameVersionContrastData()->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new GameCodeVersion($params))->getDevList());
        } catch (Exception $e) {
            Log::error('线上玩家性能版本对比分析接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 筛选条件
     *
     * @return JsonResponse
     */
    public function gameCodeVersionSelect()
    {
        $params = ContrastValidation::build()->developerAppId()->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new GameCodeVersion($params))->getSelect());
        } catch (Exception $e) {
            Log::error('线上玩家性能版本对比分析筛选接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     *
     * 标签对比分析
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3758
     * @return JsonResponse
     */
    public function tag(): JsonResponse
    {
        $params = ContrastValidation::build()
            ->developerAppId()->osType()->isSimulator()->deviceTier()
            ->deviceModel()->quality()->tagContrastData()->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new Tag($params))->getList());
        } catch (Exception $e) {
            Log::error('线上玩家性能标签对比分析接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     *
     * 标签对比分析
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3758
     * @return JsonResponse
     */
    public function tagDev(): JsonResponse
    {
        $params = ContrastValidation::build()
            ->developerAppId()->osType()->isSimulator()->deviceTier()
            ->deviceModel()->quality()->tagContrastData()->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new Tag($params))->getDevList());
        } catch (Exception $e) {
            Log::error('线上玩家性能标签对比分析接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 筛选条件
     *
     * @return JsonResponse
     */
    public function tagSelect()
    {
        $params = ContrastValidation::build()->developerAppId()->validate();
        try {
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, (new Tag($params))->getSelect());
        } catch (Exception $e) {
            Log::error('线上玩家性能标签对比分析筛选接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
