<?php

/**
 * 线上性能首页网络延迟排行
 * @desc 线上性能首页网络延迟排行
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/04/09
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\StarRocksDB;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApmHomeNetDelay extends ApmBase
{
    /**
     * 获取列表数据
     *
     * @return array
     */
    public function getList($configs, $apps): array
    {
        //获取全部数据
        $allSubSql = StarRocksDB::toSql(
            DB::table($this->mysql_apm_report_list_table)
                ->selectRaw("{$this->mysql_apm_report_list_table}.developer_app_id, {$this->performance_stat_data_table}.duration, round({$this->performance_stat_data_table}.sum_network_delay / {$this->performance_stat_data_table}.num, 2) as net_delay, {$this->mysql_apm_device_list_table}.os_type, {$this->mysql_apm_device_list_table}.dev_str")
                ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
                ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                    return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                        ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
                })
                ->join($this->performance_score_data_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
                ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
                ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0)
                ->where("{$this->performance_stat_data_table}.sum_network_delay", '>', 0)

        );

        $result = StarRocksDB::query(
            DB::table(Db::raw("({$allSubSql}) as t"))
                ->selectRaw('developer_app_id, round(sum(net_delay) / count(1), 2) as net_delay, count(distinct dev_str) as dev_num')
                ->where(function ($query) use ($configs) {
                    foreach ($configs as $id => $duration) {
                        $query = $query->orWhereRaw("(developer_app_id = {$id} and duration > {$duration})");
                    }
                })
                ->when(!empty($this->params['os_type']), function ($query) {
                    return $query->where("os_type", $this->params['os_type']);
                })
                ->groupBy('developer_app_id')
        )->get();

        //返回数据
        return [
            'all' => $this->handleData($result, $apps),
        ];
    }

    /**
     * 处理数据
     *
     * @param array $currentList
     * @param array $apps
     * @return array
     */
    private function handleData($currentList, $apps)
    {
        // 获取设备数量限制，判断结束时间-开始时间是否今天、近7天，近30天
        // 判断是否近7天
        $isNear7Days = Carbon::parse($this->startTime)->isBetween(Carbon::parse($this->endTime)->subDays(7)->toDateString(), Carbon::parse($this->endTime)->toDateString());
        // 判断是否近30天
        $isNear30Days = Carbon::parse($this->startTime)->isBetween(Carbon::parse($this->endTime)->subDays(31)->toDateString(), Carbon::parse($this->endTime)->toDateString());
        $deviceNum = 300;
        if ($isNear7Days) {
            $deviceNum = 600;
        } elseif ($isNear30Days) {
            $deviceNum = 1000;
        }

        $newList = [];
        foreach ($currentList as $item) {
            if (!isset($apps[$item['developer_app_id']])) {
                continue;
            }

            // 过滤设备数量小于设备数量限制的
            if ($item['dev_num'] < $deviceNum) {
                continue;
            }

            // 判断 net_delay 是否小于等于 0
            if ($item['net_delay'] <= 0) {
                continue;
            }

            $newList[] = [
                'net_delay' => $item['net_delay'],
                'app_id' => intval($item['developer_app_id']),
                'app_name' => $apps[$item['developer_app_id']]['app_name'],
                'app_icon' => $apps[$item['developer_app_id']]['app_icon'],
            ];
        }
        //排序
        array_multisort(array_column($newList, 'net_delay'), SORT_DESC, $newList);
        //返回数据
        return $newList;
    }
}
