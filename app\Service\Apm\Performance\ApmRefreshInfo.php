<?php

/**
 * 获取首页刷新信息数据
 * @desc 获取首页刷新信息数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/23
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class ApmRefreshInfo extends ApmBase
{
    /**
     * 获取首页筛选数据
     *
     * @return array
     */
    public function getInfo(): array
    {
        //返回数据
        return [
            'refresh_time' => $this->getRefreshTime(),
            'game_version' => $this->getNewGameVersion(),
            'inner_version' => $this->getNewInnerVersion(),
        ];
    }

    /**
     * 获取刷新时间
     *
     * @return string
     */
    private function getRefreshTime()
    {
        // 数据刷新时间,当前时间的5分钟间隔，例如是04分取00分，09分取05分
        $timestamp = time();
        // 获取当前分钟数
        $minutes = date('i', $timestamp);
        // 计算距离最近的5分钟间隔
        $nearestFiveMinute = floor($minutes / 5) * 5;
        // 构建新的时间戳
        $newTimestamp = mktime(date('H', $timestamp), $nearestFiveMinute, 0, date('n', $timestamp), date('j', $timestamp), date('Y', $timestamp));
        // 刷新时间
        return date('Y-m-d H:i:s', $newTimestamp);
    }

    /**
     * 获取最新版本
     */
    private function getNewGameVersion()
    {
        $key = "apm_home_new_game_version:{$this->params['developer_app_id']}";
        $builder = MysqlApmReportList::query()
            ->selectRaw('app_version_name as game_version_code')
            ->where("developer_app_id", $this->params['developer_app_id'])
            ->where("app_version_name", '!=', '')
            ->whereNotNull("app_version_name")
            ->groupBy('app_version_name');
        // 判断缓存是否有数据
        if (Cache::has($key)) {
            $list = $builder->whereBetween("created_at", [Carbon::now()->startOfDay()->toDateTimeString(), Carbon::now()->endOfDay()->toDateTimeString()]);
        }
        $list = $builder->getFromSR();
        if (empty($list)) {
            return Cache::get($key);
        }
        // 设置缓存
        $value = '';
        foreach ($list as $item) {
            if ($this->compareVersions($item['game_version_code'], $value) > 0) {
                $value = $item['game_version_code'];
            }
        }
        // 设置缓存
        Cache::put($key, $value);
        // 返回最新版本
        return $value;
    }

    /**
     * 比较版本
     *
     * @param $version1
     * @param $version2
     * @return int
     */
    private function compareVersions($version1, $version2): int
    {
        $version1Parts = explode('.', $version1);
        $version2Parts = explode('.', $version2);

        $numParts = max(count($version1Parts), count($version2Parts));

        for ($i = 0; $i < $numParts; $i++) {
            $part1 = isset($version1Parts[$i]) ? intval($version1Parts[$i]) : 0;
            $part2 = isset($version2Parts[$i]) ? intval($version2Parts[$i]) : 0;
            if ($part1 < $part2) {
                return -1;
            } elseif ($part1 > $part2) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * 获取最新资源版本
     *
     * @return string
     */
    private function getNewInnerVersion()
    {
        $key = "apm_home_new_inner_version:{$this->params['developer_app_id']}";
        $builder = MysqlApmReportList::query()
            ->select(['inner_version'])
            ->where("developer_app_id", $this->params['developer_app_id'])
            ->where("inner_version", '!=', '')
            ->whereNotNull("inner_version")
            ->groupBy('inner_version');
        // 判断缓存是否有数据
        if (Cache::has($key)) {
            $list = $builder->whereBetween("created_at", [Carbon::now()->startOfDay()->toDateTimeString(), Carbon::now()->endOfDay()->toDateTimeString()]);
        }
        $list = $builder->getFromSR();
        if (empty($list)) {
            return Cache::get($key);
        }
        // 设置缓存
        $value = 0;
        foreach ($list as $item) {
            $value = max($value, (int) $item['inner_version']);
        }
        // 设置缓存
        Cache::put($key, $value);
        // 返回最新版本
        return $value;
    }
}
