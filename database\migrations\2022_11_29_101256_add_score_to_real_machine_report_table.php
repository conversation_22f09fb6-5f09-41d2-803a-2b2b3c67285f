<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddScoreToRealMachineReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('real_machine_report', function (Blueprint $table) {
            //
            $table->unsignedInteger('score')->default(0)->comment('评分');
            $table->float('avg_mem')->default(0)->comment('设备内存均值');
            $table->float('jank_10min')->default(0)->comment('jank(10min)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('real_machine_report', function (Blueprint $table) {
            //
            $table->dropColumn('score');
            $table->dropColumn('avg_mem');
            $table->dropColumn('jank_10min');
        });
    }
}
