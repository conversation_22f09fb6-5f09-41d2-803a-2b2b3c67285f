<?php

/**
 * 性能趋势统计
 * @desc 性能趋势统计
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/02/18
 */

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceStatData;

class ExportData extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array {}

    /**
     * 导出数据的标题
     *
     * @return array
     */
    public function exportTitle()
    {
        return [
            '时间',
            '性能评分',
            'FPS均值（帧/秒）',
            'Stutter卡顿率（%）',
            'PSS内存峰值均值（MB）',
            'FPS抖动（次/10分钟）',
            'BigJank卡顿（次/10分钟）',
            'Lua_Memory均值（MB）',
            'Mono_Memory均值（MB）',
            'TotalCPU使用峰值均值（%）',
            'FpsPower均值（mw/帧）'
        ];
    }

    /**
     * 获取统计数据
     *
     * @return array
     */
    protected function getStatData(): array
    {
        $selectRaw = <<<COLUMNS
DATE_FORMAT({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d %H:00:00') as date,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter,
round((sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as memory,
round(sum({$this->performance_stat_data_table}.fps_jitter_count_10) / count({$this->performance_stat_data_table}.session_id), 2) as fps_jitter,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_stat_data_table}.session_id), 2) as big_jank,
round((sum({$this->performance_stat_data_table}.sum_lua_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as lua_memory,

sum({$this->performance_stat_data_table}.sum_cpu_usage) as cpu,
sum({$this->performance_stat_data_table}.sum_cpu_total_usage) as cpu_total,

sum({$this->performance_stat_data_table}.max_used_memory) as max_memory,
sum({$this->performance_stat_data_table}.sum_mono_used_size / {$this->performance_stat_data_table}.num) as mono_used_size,



sum({$this->performance_stat_data_table}.sum_battery_power / {$this->performance_stat_data_table}.sum_fps) as fps_power,



sum({$this->performance_stat_data_table}.num) as num,
count({$this->performance_stat_data_table}.session_id) as score_num,
COUNT(CASE WHEN {$this->performance_stat_data_table}.sum_battery_power > 0 THEN 1 END) as fps_power_num,
COUNT(CASE WHEN {$this->performance_stat_data_table}.sum_mono_used_size > 0 THEN 1 END) as mono_used_size_num
COLUMNS;

        return PerformanceStatData::query()
            ->selectRaw($selectRaw)
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //连接报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //连接设备表
                $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str");
            })
            ->join($this->performance_score_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //连接分数表
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小耗时的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前应用的数据
            ->when(isset($this->params['os_type']), function ($query) { //过滤掉不是当前平台的数据
                return $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(isset($this->params['game_version_code']), function ($query) { //过滤掉不是当前版本的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->where("{$this->performance_stat_data_table}.jank_count_10", '<=', 2000)
            ->groupByRaw("DATE_FORMAT({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d %H:00:00')")
            ->getFromSR();
    }
}
