<?php

namespace websocketService;

use websocketService\dbService\MysqlService;
use swoole_websocket_server;
use websocketService\dbService\RedisService;
use websocketService\eventHandler\EventHandlerFactory;
use Redis;

class WebsocketServer
{
    /**
     * @var swoole_websocket_server
     */
    protected $server;
    /**
     * @var array
     */
    protected $cfg;

    /**
     * @var Redis
     */
    protected $redis;

    /**
     * @var \mysqli
     */
    protected $mysql;

    /**配置的app参数
     * @var array
     */
    protected $appInfo;

    //初始化
    public function __construct($debug = true)
    {
        if ($debug) {
            $this->server = new swoole_websocket_server("0.0.0.0", 8085);
        } else {
            $this->server = new swoole_websocket_server("0.0.0.0", 8085, SWOOLE_PROCESS, SWOOLE_SOCK_TCP | SWOOLE_SSL);
            $this->server->set(array(
                'worker_num' => 1,//根据cpu核数定，这里设置为CPU核数的1-4倍最合理
                'daemonize' => false,
                'ssl_cert_file' => '/data/conf/nginx/1_shiyue.com_bundle.crt',
                'ssl_key_file' => '/data/conf/nginx/2_shiyue.com.key',
            ));
        }

        $this->register();
    }

    /**
     * 注册方法
     */
    protected function register()
    {
        //启动事件
        $this->server->on('start', function (swoole_websocket_server $server) {
            echo "Websocket 服务器开启," . date('Y-m-d H:i:s') . PHP_EOL;
        });
        //连接事件
        $this->server->on('open', function (swoole_websocket_server $server, $request) {
            try {
                echo "客户连接信息:" . $request->fd . PHP_EOL;
                $server->push($request->fd, json_encode([
                    'message' => '连接成功',
                    'data' => ['fd' => $request->fd],
                ]));
            } catch (\Exception $exception) {
                echo '工具平台webSocket服务器,客户连接失败,原因:' . $exception->getMessage();
            }
        });

        //消息事件
        $this->server->on('message', function (swoole_websocket_server $server, $frame) {
            try {
                //检查redis工作情况
                $this->checkDbService();
                echo "客户发送信息fd:" . $frame->fd . PHP_EOL;
                echo "客户发送信息data:" . $frame->data . PHP_EOL;
                $data = json_decode($frame->data, true);
                $type = $data['type'];
                //心跳检测
                if ($type == -1) {
                    return;
                }
                $handler = EventHandlerFactory::getSocketMessageHandler($type, $frame, $data);
                //满足推送多个类型，返回数组格式
                $resultData = $handler->returnMessage();
                foreach ($resultData as $result) {
                    $fd = $result['fd'];
                    $returnData = json_encode($result['message'], JSON_UNESCAPED_UNICODE);
                    echo "客户接收信息fd:" . $fd . PHP_EOL . "message:" . $returnData . PHP_EOL;
                    $server->push($fd, $returnData);
                }
                $this->closeDb();
            } catch (\Exception $exception) {
                echo '工具平台webSocket服务器,客户发送/收取信息失败,原因:' . $exception->getMessage();
            }
        });
        //关闭连接事件
        $this->server->on('close', function (swoole_websocket_server $server, $fd) {
            echo "Websocket客户端关闭连接,fd:{$fd}," . date('Y-m-d H:i:s') . PHP_EOL;
        });
    }

    /**
     * 检查redis服务
     */
    protected function checkDbService()
    {
        $this->cfg = require __DIR__ . '/config.php';
        //注册redis服务
        $this->redis = RedisService::getInstance($this->cfg);
        //注册mysql服务
        $this->mysql = MysqlService::getInstance($this->cfg);
    }

    /**
     * 关闭数据库
     */
    protected function closeDb()
    {
        if ($this->redis != null) {
            $this->redis->close();
        }
//        if ($this->mysql != null) {
//            mysqli_close($this->mysql);
//        }
    }

    /**
     * @return mixed
     */
    public function serverStart()
    {
        return $this->server->start();
    }

}





