<?php

namespace App\Service\PerfomanceScrore;

/**
 * bigjank评分类
 */
class BigJankPerformanceScore extends PerformanceScoreService implements PerformanceScore
{
    protected $bigJankCount;
    protected $level;

    public function __construct(int $level, int $bigJankCount)
    {
        $this->level = $level;
        $this->bigJankCount = $bigJankCount;
    }

    /**
     * 以Android的2档机型为例
     * BigJank等于20，分数30*0.7=21
     * BigJank等于99，分数为0
     * BigJank等于05，满分30
     *
     * @param array $data
     * @return string
     */
    public function getPerformanceScore(): int
    {
        $jankCount = $this->bigJankCount;
        // bigJank次数指标
        $count = self::LEVEL_TO_BIGJANK_COUNT[$this->level];
        // 计算分数
        $score = 21;
        if ($jankCount == $count) {
            return $score;
        } elseif ($jankCount < $count) {
            $diff = bcsub($count, $jankCount, 2);
            // 加分
            $score = bcadd(bcmul(bcmul($diff, 100, 2), 0.006, 4), $score, 2);
            // 最高30分
            return min($score, 30);
        } else {
            $diff = bcsub($jankCount, $count, 2);
            // 扣分
            $score = bcsub($score, bcmul(bcmul($diff, 100, 2), 0.0027, 4), 2);
            // 最低0分
            return max($score, 0);
        }
    }
}
