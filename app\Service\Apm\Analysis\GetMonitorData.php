<?php

/**
 * 获取监控数据
 * @desc 获取监控数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/04/24
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Analysis;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Service\Apm\Performance\ApmTrait;
use Carbon\Carbon;

class GetMonitorData
{
    use ApmTrait;

    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        $current = [$this->params['start_time'], $this->params['end_time']];
        $chartData = $this->getChartData($current);
        $dbData = $this->getDbData($current);
        return [
            'chartData' => $chartData,
            'dbData' => $dbData
        ];
    }

    /**
     * 获取数据库数据
     *
     * @param $dates
     * @return array
     */
    private function getDbData(array $dates)
    {
        $selectRaw = <<<COLUMNS
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness,
round((sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as avg_memory,
round((sum({$this->performance_stat_data_table}.down_traffic_10 + {$this->performance_stat_data_table}.up_traffic_10) / count({$this->performance_score_data_table}.session_id)) / 1024, 2) as avg_network_traffic,
round(sum(case when {$this->performance_stat_data_table}.sum_network_delay > 0 then {$this->performance_stat_data_table}.sum_network_delay / {$this->performance_stat_data_table}.num else 0 end) / sum(case when {$this->performance_stat_data_table}.sum_network_delay > 0 then 1 else 0 end), 2) as avg_network_delay,
round(sum({$this->performance_stat_data_table}.sum_battery_power) / sum({$this->performance_stat_data_table}.num), 2) as battery_power,
round(sum({$this->performance_stat_data_table}.sum_battery_temp) / sum({$this->performance_stat_data_table}.num), 2) as battery_temp
COLUMNS;
        $data = PerformanceScoreData::query() //查询性能分数表
            ->selectRaw($selectRaw) //计算平均分
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $dates) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->when(!empty($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->firstFromSR();
        foreach ($data as $key => $value) {
            if (empty($value)) {
                $data[$key] = 0;
            }
        }
        return $data;
    }

    /**
     * 获取折线图数据
     * 按资源版本统计监控指标
     *
     * @param array $dates 日期范围，格式为 [$startDate, $endDate]
     * @return array 按资源版本分组的监控数据
     */
    public function getChartData(array $dates)
    {
        // 修改开始时间，调整为-15天
        $startDate = Carbon::parse($this->params['start_time'])->subDays(15)->toDateTimeString();
        $dates = [$startDate, $this->params['end_time']];

        // SQL 查询：按小时分组统计性能指标
        $selectRaw = <<<COLUMNS
{$this->mysql_apm_report_list_table}.inner_version,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness,
count({$this->performance_stat_data_table}.session_id) as count,
count(distinct {$this->mysql_apm_report_list_table}.dev_str) as dev_str_count
COLUMNS;

        $result = PerformanceScoreData::query() // 查询性能分数表
            ->selectRaw($selectRaw) // 计算各项性能指标的平均值
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") // 关联性能统计表
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") // 关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { // 关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") // 关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); // 关联设备唯一标识
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) // 过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $dates) // 过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) // 只获取当前效能后台Id的数据
            ->when(!empty($this->params['os_type']), function ($json) { // 如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) // 过滤掉模拟器数据
            ->where("{$this->mysql_apm_report_list_table}.inner_version", '!=', '') // inner_version 不为空
            ->groupBy("{$this->mysql_apm_report_list_table}.inner_version") // 按资源版本分组
            ->getFromSR();

        // 根据inner_version进行排序，如果inner_version有小数点则替换成空字符串再进行排序，倒序
        $tempResult = [];
        foreach ($result as $item) {
            // 获取inner_version值作为排序键
            $innerVersion = $item['inner_version'] ?? '';
            // 保存原始数据和索引
            $cleanKey = str_replace('.', '', $innerVersion);
            $tempResult[$cleanKey] = [
                'original_key' => $innerVersion,
                'data' => $item
            ];
        }

        // 使用数值排序，将字符串转换为数字后进行比较
        // 对于纯数字键和日期格式键都能正确排序
        uksort($tempResult, function ($a, $b) {
            // 尝试将键转换为数字进行比较
            $numA = is_numeric($a) ? (float)$a : 0;
            $numB = is_numeric($b) ? (float)$b : 0;
            return $numB <=> $numA; // 使用太空船操作符进行倒序排序
        });

        // 恢复原始结构
        $result = [];
        foreach ($tempResult as $item) {
            $result[$item['original_key']] = $item['data'];
        }

        // 只获取前50个，保留原始键
        return array_slice($result, 0, 50, true);
    }
}
