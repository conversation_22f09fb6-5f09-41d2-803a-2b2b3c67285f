<?php

namespace App\Http\Controllers\Perf;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Perf\BookmarkValidation;
use App\Model\Perf\Bookmark;
use App\Model\Perf\BookmarkItem;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PerfBookmarkController extends Controller
{
    /**
     * 每页显示条数
     *
     * @var int
     */
    const PER_PAGE = 10;

    /**
     * 收藏夹列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3489
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        //请求参数校验
        $params = BookmarkValidation::build()
            ->reportIdNullable()
            ->limit()
            ->page()
            ->validate();

        try {
            //获取数据
            $res = Bookmark::query()
                ->where('user_id', Auth::user()->user_id)
                ->orderByDesc('id')
                ->paginate($params['limit'] ?? self::PER_PAGE);

            $list = $res->items();
            if (!empty($params['report_id'])) {
                $itemList = BookmarkItem::query()
                    ->whereIn('bookmark_id', array_column($list, 'id'))
                    ->where('report_id', $params['report_id'])
                    ->pluck('bookmark_id')
                    ->toArray();
                //标记是否已收藏
                foreach ($list as &$item) {
                    $item['is_bookmark'] = in_array($item['id'], $itemList);
                }
            }

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list, 'total' => $res->total()]);
        } catch (Exception $e) {
            \Log::error('获取收藏夹列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 添加收藏夹
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3492
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request): JsonResponse
    {
        //请求参数校验
        $params = BookmarkValidation::build()
            ->title()
            ->validate();

        try {
            //添加收藏夹
            $bookmark = Bookmark::query()->create([
                'user_id' => Auth::user()->user_id,
                'title' => $params['title'],
            ]);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['id' => $bookmark['id']]);
        } catch (Exception $e) {
            \Log::error('添加收藏夹接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 编辑收藏夹
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3494
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request): JsonResponse
    {
        //请求参数校验
        $params = BookmarkValidation::build()
            ->bookmarkId()
            ->title()
            ->validate();

        try {
            $bookmark = Bookmark::query()->where('user_id',  Auth::user()->user_id)->find($params['bookmark_id']);
            //判断是否存在
            if (empty($bookmark)) return $this->response(StatusCode::C_PARAM_ERROR);
            //修改收藏夹
            $bookmark->update([
                'title' => $params['title'],
            ]);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('编辑收藏夹接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除收藏夹
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3496
     * @param Request $request
     * @return JsonResponse
     */
    public function del(Request $request): JsonResponse
    {
        //请求参数校验
        $params = BookmarkValidation::build()->bookmarkId()->validate();

        $bookmark = Bookmark::query()->where('user_id',  Auth::user()->user_id)->find($params['bookmark_id']);
        //判断是否存在
        if (empty($bookmark)) return $this->response(StatusCode::C_PARAM_ERROR);

        $db = DB::connection('tool');
        try {
            //开启事务
            $db->beginTransaction();
            //删除收藏夹
            Bookmark::query()->where('id', $params['bookmark_id'])->delete();
            //删除收藏夹下的项目
            BookmarkItem::query()->where('bookmark_id', $params['bookmark_id'])->delete();
            //提交事务
            $db->commit();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            //回滚事务
            $db->rollBack();
            \Log::error('删除收藏夹接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 收藏和取消
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3496
     * @param Request $request
     * @return JsonResponse
     */
    public function operate(Request $request): JsonResponse
    {
        //请求参数校验
        $params = BookmarkValidation::build()->bookmarkId()->reportId()->validate();

        $bookmark = Bookmark::query()->where('user_id',  Auth::user()->user_id)->find($params['bookmark_id']);
        //判断是否存在
        if (empty($bookmark)) return $this->response(StatusCode::C_PARAM_ERROR);

        try {
            $item = BookmarkItem::query()
                ->where('bookmark_id', $params['bookmark_id'])
                ->where('report_id', $params['report_id'])
                ->first();
            if (empty($item)) {
                BookmarkItem::query()->create([
                    'bookmark_id' => $params['bookmark_id'],
                    'report_id' => $params['report_id'],
                ]);
            } else {
                //删除收藏夹下的项目
                $item->delete();
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('操作收藏夹接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
