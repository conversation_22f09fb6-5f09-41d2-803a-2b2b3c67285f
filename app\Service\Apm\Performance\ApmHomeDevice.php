<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\StarRocksDB;
use Illuminate\Support\Facades\DB;

class ApmHomeDevice extends ApmBase
{
    /**
     * 获取列表数据
     *
     * @return array
     */
    public function getList($configs): array
    {
        $subSql = StarRocksDB::toSql(DB::table(MysqlApmReportList::TABLE_NAME)
                        ->selectRaw('mysql_apm_report_list.developer_app_id, max(performance_stat_data.duration) as duration')
                        ->join('mysql_apm_device_list', function($join) {
                            $join->on("mysql_apm_device_list.developer_app_id", '=', "mysql_apm_report_list.developer_app_id")
                            ->on("mysql_apm_device_list.dev_str", '=', "mysql_apm_report_list.dev_str");
                        })
                        ->join('performance_stat_data', "performance_stat_data.session_id", '=', "mysql_apm_report_list.id")
                        ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
                        ->groupBy('mysql_apm_report_list.dev_str', 'mysql_apm_report_list.developer_app_id')
                    );

        //获取数据
        $list = StarRocksDB::query(DB::table(Db::raw("({$subSql}) as t"))
            ->selectRaw('developer_app_id, count(*) as num')
            ->where(function($query) use ($configs) {
                foreach ($configs as $id => $duration) {
                    $query = $query->orWhereRaw("(developer_app_id = {$id} and duration > {$duration})");
                }
            })
            ->groupBy('developer_app_id')
        )->get();
        
        //整理数据
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'num' => intval($item['num']),
                'app_id' => intval($item['developer_app_id']),
            ];
        }

        //返回数据
        return $data;
    }
}
