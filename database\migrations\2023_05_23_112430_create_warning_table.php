<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWarningTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('warning', function (Blueprint $table) {
            $table->bigIncrements('warning_id')->comment('预警id');
            $table->unsignedInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedTinyInteger('os_type')->default(0)->comment('平台类型;1为安卓,2为iOS');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态');
            $table->string('name', 128)->comment('预警计划名称');
            $table->json('receiving_group')->comment('接收群地址');
            $table->json('app_version')->comment('app版本');
            $table->json('receiving_person')->comment('接收人员');
            $table->dateTime('last_warning_time')->nullable()->comment('最近预警时间');
            $table->unsignedInteger('trigger_count')->default(0)->comment('触发次数');
            $table->unsignedTinyInteger('monitor_type')->default(2)->comment('监控类型 1：大盘监控 2：问题监控');
            $table->json('receiving_phone')->comment('接收手机');
            $table->timestamps();
            $table->unique(['developer_app_id', 'name']);
            $table->index(['developer_app_id', 'os_type']);
            $table->index(['developer_app_id', 'last_warning_time', 'created_at']);
        });
         \DB::connection('apm')->statement("ALTER TABLE `warning` comment '预警计划表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('warning');
    }
}
