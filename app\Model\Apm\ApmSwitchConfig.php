<?php

/**
 * 开关配置
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class ApmSwitchConfig extends Model
{
    use ModelTrait;

    public $connection = "apm";

    protected $table = 'apm_switch_config';

    protected $primaryKey = 'id';

    protected $guarded = [];

    /**
     * 开关状态,1开启,0关闭
     */
    const STATUS_ON = 1;
    const STATUS_OFF = 0;

    /**
     * 获取开关缓存KEY
     *
     * @param $developerAppId
     * @return string
     */
    private static function getCacheKey($developerAppId): string
    {
        return 'apm_switch_config:' . $developerAppId;
    }

    /**
     * 刷新缓存
     *
     * @param $developerAppId
     * @return void
     */
    public static function flushCache($developerAppId)
    {
        $cacheKey = self::getCacheKey($developerAppId);
        $list = self::query()
            ->where('developer_app_id', $developerAppId)
            ->where('status', 1)
            ->get()->toArray();
        Redis::connection('apm')->set($cacheKey, json_encode($list));
    }
}

