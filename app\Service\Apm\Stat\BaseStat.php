<?php

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\BaseBuilder;
use App\Model\Apm\StarRocks\BaseModel;
use App\Service\Apm\Performance\ApmBase;
use App\Service\Apm\Performance\ApmTrait;

abstract class BaseStat
{
    use ApmTrait;

    /**
     * 标准分数
     *
     * @var int
     */
    protected $standardScore = ApmBase::STANDARD_SCORE;

    /**
     * 开始时间
     *
     * @var string
     */
    protected $startTime;

    /**
     * 结束时间
     *
     * @var string
     */
    protected $endTime;

    /**
     * 结束时间
     *
     * @var array
     */
    protected $params;

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->startTime = "{$params['start_time']} 00:00:00";
        $this->endTime = "{$params['end_time']} 23:59:59";
        $this->params = $params;
    }

    /**
     * 获取通用的查询构造器
     *
     * @param BaseModel $model
     * @return BaseBuilder
     */
    protected function getCommonBuilder(BaseModel $model)
    {
        return $model::query()->join($this->mysql_apm_report_list_table, "{$model->getTable()}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //连接报告表
        ->join($this->mysql_apm_device_list_table, function ($join) { //连接设备表
            $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id")
                ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str");
        })
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //连接性能统计表
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小耗时的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前应用的数据
            ->when(!empty($this->params['os_type']), function ($query) { //过滤掉不是当前平台的数据
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['game_version_code']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                if (is_array($this->params['game_version_code'])) {
                    return $query->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
                } else {
                    return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
                }
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            });
    }
}
