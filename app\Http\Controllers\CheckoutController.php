<?php
/**
 * CheckoutController.php
 *
 * User: Dican
 * Date: 2022/8/15
 * Email: <<EMAIL>>
 */

namespace App\Http\Controllers;


use App\Components\Helper\Curl;
use App\Jobs\CheckoutJob;
use App\Model\Checkout\Checkout;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CheckoutController extends Controller
{
    /**
     * 列表
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2374
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $input = $request->all();
        $list = Checkout::when(!empty($input), function ($query) use ($input) {
            isset($input['platform_type']) && $query->where('platform_type', $input['platform_type']);
            isset($input['title']) && $query->where('title', 'like', '%' . $input['title'] . '%');
            isset($input['start_created_at']) && isset($input['end_created_at']) && $query->whereBetween('created_at', [$input['start_created_at'], $input['end_created_at']]);
        })->orderBy('created_at', 'desc')
        ->paginate($input['per_page'] ?? 15,
            ['checkout_id', 'platform_type', 'name', 'title', 'type', 'url', 'size', 'status', 'estimated_time', 'created_at', 'file_type',
            'release_store']);
        return $this->response(0, ['list' => $list->items(), 'total' => $list->total()]);
    }

    /**
     * 保存检测
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2375
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function store(Request $request): JsonResponse
    {
        $input = $request->all();
        try {
            \DB::connection('tool')->beginTransaction();
            $checkout = new Checkout();
            $input['status'] = Checkout::STATUS_BEGIN;
            if (isset($input['type']) && $input['type'] == Checkout::TYPE_JSON) {
                //读取json文件内容，并变更状态，入库检测报告内容
                $result = Curl::get($input['url']);
                if (!$result) {
                    $input['status'] = Checkout::STATUS_FAIL;
                } else {
                    $input['status'] = Checkout::STATUS_FINISHED;
                    $input['checkout_report'] = $result;
                }
            }
            $checkout->store($input, true);
            \DB::connection('tool')->commit();
            return $this->response();
        } catch (Exception $e) {
            \Log::error('保存检测接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            \DB::connection('tool')->rollBack();
            return $this->response(1005);
        }
    }

    /**
     * 获取检测报告
     * 检测报告内容偏大，不在列表接口返回，单独接口单条返回，降低网络消耗
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2380
     * @param $checkoutId
     * @return JsonResponse
     */
    public function getReport($checkoutId): JsonResponse
    {
        try {
            $checkout = Checkout::findOrFail($checkoutId, ['title', 'checkout_report', 'release_store']);
            return $this->response(0, $checkout);
        } catch (Exception $e) {
            \Log::error('获取检测报告接口报错-checkoutId:' . $checkoutId . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 发起检测
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2402
     * @param Checkout $checkout
     * @return JsonResponse
     */
    public function checkout(Checkout $checkout): JsonResponse
    {
        try {
            if ($checkout->type != Checkout::TYPE_PACKAGE || !in_array($checkout->status, [Checkout::STATUS_BEGIN, Checkout::STATUS_FAIL])) {
                return $this->response(1011);
            }
            //状态改为检测中
            $checkout->status = Checkout::STATUS_RUNNING;
            $checkout->save();
            //调用Python脚本
            CheckoutJob::dispatch($checkout->checkout_id)->onQueue('tool_checkout');
            return $this->response();
        } catch (Exception $e) {
            \Log::error('发起检测接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }
}
