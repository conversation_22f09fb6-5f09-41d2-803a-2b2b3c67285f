<?php

/**
 * 微信群通知
 */

namespace App\Service\Push;

use App\Components\Helper\Curl;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class WXGroupNoticeService
{
    private $url;

    /**
     * @param string $url webhook地址
     */
    public function __construct(string $url)
    {
        $this->url = $url;
    }

    /**
     * 企业微信群通知
     * @param string $content
     * @param string $type markdown或者text
     * @return bool
     */
    public function wxGroupNotify(string $content, string $type = 'text'): bool
    {
        try {
            // @all 提醒所有人
            $list = ["@all"];
            //通知内容
            $string = json_encode($this->messageContent($content, $list, $type));
            //判断编码
            $cur_encode = mb_detect_encoding($string, ['ASCII', 'GB2312', 'GBK', 'BIG5', 'UTF-8']);
            //进行http请求
            $res = Curl::post($this->url, mb_convert_encoding($string, 'UTF-8', $cur_encode));
            //解析结果
            $res = json_decode($res, true);
            //记录日志
            Log::info("发送群机器人通知状态：{$res['errcode']}: url: $this->url, message: {$res['errmsg']}");
            //返回结果
            return $res["errcode"];
        } catch (GuzzleException $e) {
            Log::error('企业微信通知失败：' . PHP_EOL . '错误信息：' . $e->getMessage() . ',代码行数:' . $e->getLine());
            return false;
        }

    }

    /**
     * 按照请求参数的格式拼数据
     * @param $content
     * @param array $list
     * @param string $type
     * @return array
     */
    public function messageContent($content, array $list, string $type = 'text'): array
    {
        return [
            'msgtype' => $type,
            $type => array_merge(['content' => $content], ["mentioned_list" => $list])
        ];
    }
}
