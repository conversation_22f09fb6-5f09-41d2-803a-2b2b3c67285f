<?php

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class TempInfo<PERSON>hart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'battery_temp',
                'cpu_temp',
                'gpu_temp',
                'tags_info',
                'app_state',
                'perf_data_ts',
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['tempInfo'] = [    //按照时间戳分组
                'BTemp' => $item['battery_temp'],   //电池温度
                'CTemp' => $item['cpu_temp'],   //cpu温度
                'GTemp' => min($item['gpu_temp'], '100'),   //gpu温度
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }
        return array_values($list);
    }
}
