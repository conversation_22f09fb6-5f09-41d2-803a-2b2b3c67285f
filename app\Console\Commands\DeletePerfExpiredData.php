<?php

/**
 * 删除线上性能过期的报告数据定时脚本
 * @desc 删除线上性能过期的报告数据定时脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/07/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\Apm\ApmReportList;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DeletePerfExpiredData extends Command
{
    /**
     * 过期日数
     *
     * @var int
     */
    public const EXPIRED_DAY = 7;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:pref:expired:data {id? : 指定删除的ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除线上性能过期的报告数据定时脚本';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            // 打印日志
            Log::info('开始删除线上性能过期的报告数据，当前时间：' . now()->toDateTimeString());
            // 删除数据的日期
            $deleteTime = now()->subDays(self::EXPIRED_DAY);
            // 查询ID
            $maxId = ApmReportList::query()
                ->where('created_at', '>=', (clone $deleteTime)->startOfDay()->toDateTimeString())
                ->where('created_at', '<=', (clone $deleteTime)->endOfDay()->toDateTimeString())
                ->max('id');
            // 判断ID是否存在
            if (empty($maxId)) {
                return;
            }
            // 获取是否有传入ID
            $id = $this->argument('id');
            // 判断id是否存在，并且 id 要比maxID小
            if (!empty($id) && $id <= $maxId) {
                $maxId = (int) $id;
            }
            // 删除小于maxId的数据
            ApmReportList::query()
                ->where('id', '<=', $maxId)
                ->delete();
            // 打印日志
            Log::info("结束删除线上性能过期的报告数据，删除的ID：{$maxId}，当前时间：" . now()->toDateTimeString());
        } catch (\Exception $e) {
            Log::error("删除线上性能过期的报告数据失败，{$e->getTraceAsString()}");
        }
    }
}
