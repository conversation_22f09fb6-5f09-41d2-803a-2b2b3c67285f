<?php

namespace App\Model;

class ThresholdConfig extends BaseModel
{
    use ModelTrait;

    protected $table = 'ab_threshold_config';
    protected $primaryKey = 'threshold_config_id';

    protected $fillable = [
        'developer_app_id', 'name', 'config', 'is_current_config'
    ];

    public $validateRule = [
        'config' => 'required|array',
        'is_current_config' => 'required|int'
    ];

    //是否为当前配置
    public const CURRENT = 1;
    public const NOT_CURRENT = 0;
    public const TYPE = [
        self::NOT_CURRENT,
        self::CURRENT,
    ];

    protected $casts = [
        'config' => 'array',
    ];

}
