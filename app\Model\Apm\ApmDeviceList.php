<?php

/**
 * Created by phpstorm
 * User: liuxr
 * Date: 2023/3/10
 * Time: 17:12
 * TODO:
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApmDeviceList extends Model
{
    use ModelTrait;

    public $connection = "apm";

    protected $table = 'apm_device_list';

    protected $primaryKey = 'id';

    protected $guarded = [];

    /**
     * 平台
     * @var string
     */
    const ANDROID = 1;
    const IOS = 2;
    const PC = 3;
    const MINI = 4;
    const HARMONY = 5;

    /**
     * 平台中文名称映射
     *
     * @var array
     */
    const PLATFORM = [
        0 => '全平台',
        self::ANDROID => 'Android',
        self::IOS => 'iOS',
        self::PC => 'PC',
        self::MINI => '小程序',
        self::HARMONY => 'Harmony',
    ];

    /**
     * 获取设备的性能报告
     */
    public function reports(): HasMany
    {
        return $this->hasMany('App\Model\Apm\ApmReportList', 'dev_str', 'dev_str');
    }
}
