<?php

/**
 * 负责人巡检配置表控制器
 * @desc 负责人巡检配置表控制器
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2025/04/14
 */

namespace App\Http\Controllers\Performance;

use App\Model\Apm\ApmLeaderWebhook;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Service\Apm\Analysis\LeaderStat;

class ApmLeaderWebhookController extends Controller
{
    /**
     * 获取负责人巡检配置列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            $query = ApmLeaderWebhook::query();
            // 根据developer_app_id搜索
            $query->where('developer_app_id', $request->developer_app_id);
            // 分页
            $data = $query->orderBy('id', 'desc')->paginate($request->input('per_page', 15));
            // 返回数据
            return $this->response(0, [
                'total' => $data->total(),
                'list' => $data->items()
            ]);
        } catch (\Exception $e) {
            Log::error('获取负责人巡检配置列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1000, [], $e->getMessage());
        }
    }

    /**
     * 创建负责人巡检配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'frequency' => 'required|integer|in:1,2',
                'range' => 'required|integer|in:1,2,3,4',
                'webhook_urls' => 'required|array',
                'status' => 'required|integer|in:0,1',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 创建负责人巡检配置
            $webhook = new ApmLeaderWebhook($request->only([
                'developer_app_id',
                'frequency',
                'range',
                'webhook_urls',
                'status',
            ]));

            $webhook->operator = Auth::user()->alias;
            $webhook->save();

            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('创建负责人巡检配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 更新负责人巡检配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer',
                'frequency' => 'required|integer|in:1,2',
                'range' => 'required|integer|in:1,2,3,4',
                'webhook_urls' => 'required|array',
                'status' => 'required|integer|in:0,1',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 更新负责人巡检配置
            ApmLeaderWebhook::query()->where('id', $request->id)->update([
                'frequency' => $request->frequency,
                'range' => $request->range,
                'webhook_urls' => $request->webhook_urls,
                'status' => $request->status,
                'operator' => Auth::user()->alias
            ]);
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('更新负责人巡检配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 删除负责人巡检配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer'
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 删除负责人巡检配置
            ApmLeaderWebhook::where('id', $request->id)->delete();
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('删除负责人巡检配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 更新负责人巡检配置状态
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function status(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'id' => 'required|integer',
                'status' => 'required|integer|in:0,1',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 更新负责人巡检配置状态
            ApmLeaderWebhook::query()->where('id', $request->id)->update([
                'status' => $request->status,
                'operator' => Auth::user()->alias
            ]);
            // 返回数据
            return $this->response(0);
        } catch (\Exception $e) {
            Log::error('更新负责人巡检配置状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 统计负责人
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function stat(Request $request)
    {
        try {
            // 参数校验
            $validator = Validator::make($request->toArray(), [
                'developer_app_id' => 'required|integer',
                'start_date' => 'required|date|date_format:Y-m-d',
                'end_date' => 'required|date|date_format:Y-m-d',
            ]);
            if ($validator->fails()) {
                return $this->response(1000, [], $validator->errors()->first());
            }
            // 统计
            $data = (new LeaderStat($request->toArray()))->getStat();
            // 返回数据
            return $this->response(0, $data);
        } catch (\Exception $e) {
            Log::error('统计负责人接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }
}
