<?php

/**
 * 检查apm报告同步脚本
 * @desc 检查apm报告同步脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/11/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Service\Push\WXGroupNoticeService;
use Illuminate\Console\Command;

class CheckApmSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:apm:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查apm报告同步脚本';

    /**
     * 执行逻辑
     *
     * @return void
     */
    public function handle()
    {
        // 获取ApmReport最大时间
        $maxTime = MysqlApmReportList::query()->selectRaw('max(created_at) as created_at')->firstFromSR();
        // 判断最大的时间是否少于当前时间3000秒
        if (strtotime($maxTime['created_at']) < now()->subSeconds(3000)->timestamp) {
            // 发送机器人报警
            $text = "PerfMate同步apm_report_list表有异常，最新的同步时间是：{$maxTime['created_at']}，请检查是否关闭全部开关或者同步脚本报错！";
            $service = new WXGroupNoticeService('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f57039b2-aa55-4d4f-8797-ca45185e1d7b');
            $service->wxGroupNotify($text);
        }
    }
}
