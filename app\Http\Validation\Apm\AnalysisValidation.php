<?php

/**
 * 分析页面校验类
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;

/**
 * @method static AnalysisValidation build()
 */
class AnalysisValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): AnalysisValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 开始时间校验
     *
     * @return $this
     */
    public function startTime(): AnalysisValidation
    {
        $this->rules['start_time'] = 'required|date';
        return $this;
    }

    /**
     * 结束时间校验
     *
     * @return $this
     */
    public function endTime(): AnalysisValidation
    {
        $this->rules['end_time'] = 'required|date';
        return $this;
    }

    /**
     * 平台校验
     *
     * @return $this
     */
    public function osType(): AnalysisValidation
    {
        $this->rules['os_type'] = 'string';
        return $this;
    }

    /**
     * 排序字段校验
     *
     * @return $this
     */
    public function sortField(): AnalysisValidation
    {
        $this->rules['sort_field'] = 'string';
        return $this;
    }

    /**
     * 排序方式校验
     *
     * @return $this
     */
    public function sortType(): AnalysisValidation
    {
        $this->rules['sort_type'] = 'string';
        return $this;
    }

    /**
     * 每页条数校验
     *
     * @return $this
     */
    public function limit(): AnalysisValidation
    {
        $this->rules['limit'] = 'integer|min:10|max:100';
        return $this;
    }

    /**
     * 页码校验
     *
     * @return $this
     */
    public function page(): AnalysisValidation
    {
        $this->rules['page'] = 'integer|min:1';
        return $this;
    }

    /**
     * 是否模拟器校验
     *
     * @return $this
     */
    public function isSimulator(): AnalysisValidation
    {
        $this->rules['is_simulator'] = 'nullable|integer';
        return $this;
    }

    /**
     * 设备挡位校验
     *
     * @return $this
     */
    public function deviceTier(): AnalysisValidation
    {
        $this->rules['device_tier'] = 'nullable|integer';
        return $this;
    }

    /**
     * 版本
     *
     * @return $this
     */
    public function gameVersionCode(): AnalysisValidation
    {
        $this->rules['game_version_code'] = 'nullable|string';
        return $this;
    }

    /**
     * 资源版本
     *
     * @return $this
     */
    public function innerVersion(): AnalysisValidation
    {
        $this->rules['inner_version'] = 'nullable|string';
        return $this;
    }

    /**
     * 画质
     *
     * @return $this
     */
    public function quality(): AnalysisValidation
    {
        $this->rules['quality'] = 'nullable|string';
        return $this;
    }

    /**
     * 过滤top
     *
     * @return $this
     */
    public function filterTop(): AnalysisValidation
    {
        $this->rules['filter_top'] = 'nullable|string';
        return $this;
    }

    /**
     * 主要负责人
     *
     * @return $this
     */
    public function director(): AnalysisValidation
    {
        $this->rules['director'] = 'nullable|string';
        return $this;
    }
}
