<?php

/**
 * 线上性能首页评分排行
 * @desc 线上性能首页评分排行
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\StarRocksDB;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApmHomeStutter extends ApmBase
{
    /**
     * 获取列表数据
     *
     * @return array
     */
    public function getList($configs, $apps): array
    {
        $result = [];
        $dates = [
            "current" => [
                $this->startTime,
                $this->endTime
            ],
            "proportion" => [
                $this->proportionStartTime,
                $this->proportionEndTime
            ],
        ];
        foreach ($dates as $key => $date) {
            //获取全部数据
            $allSubSql = StarRocksDB::toSql(
                DB::table($this->mysql_apm_report_list_table)
                    ->selectRaw("{$this->mysql_apm_report_list_table}.developer_app_id, {$this->performance_score_data_table}.all_score, {$this->performance_stat_data_table}.duration, {$this->performance_stat_data_table}.sum_jank_time, {$this->performance_stat_data_table}.sum_frame_times_time, {$this->mysql_apm_device_list_table}.os_type, {$this->mysql_apm_device_list_table}.dev_str")
                    ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
                    ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                        return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                            ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
                    })
                    ->join($this->performance_score_data_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
                    ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $date)
                    ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0)
                    ->whereNotIn("{$this->mysql_apm_report_list_table}.developer_app_id", [3, 22, 30, 36, 35, 37, 52, 68, 69, 70, 71, 72, 73, 75])
            );

            $result[$key] = StarRocksDB::query(
                DB::table(Db::raw("({$allSubSql}) as t"))
                    ->selectRaw('developer_app_id, round((sum(sum_jank_time / sum_frame_times_time) / count(*)) * 100, 2) as stutter, count(*) as num, count(distinct dev_str) as dev_num')
                    ->where(function ($query) use ($configs) {
                        foreach ($configs as $id => $duration) {
                            $query = $query->orWhereRaw("(developer_app_id = {$id} and duration > {$duration})");
                        }
                    })
                    ->when(!empty($this->params['os_type']), function ($query) {
                        return $query->where("os_type", $this->params['os_type']);
                    })
                    ->groupBy('developer_app_id')
            )->get();
        }

        //返回数据
        return [
            'all' => $this->handleData($result['current'], array_column($result['proportion'], null, 'developer_app_id'), $apps),
        ];
    }

    /**
     * 处理数据
     *
     * @param array $currentList
     * @param array $proportionList
     * @param array $apps
     * @return array
     */
    private function handleData($currentList, $proportionList, $apps)
    {
        // 获取设备数量限制，判断结束时间-开始时间是否今天、近7天，近30天
        // 判断是否近7天
        $isNear7Days = Carbon::parse($this->startTime)->isBetween(Carbon::parse($this->endTime)->subDays(7)->toDateString(), Carbon::parse($this->endTime)->toDateString());
        // 判断是否近30天
        $isNear30Days = Carbon::parse($this->startTime)->isBetween(Carbon::parse($this->endTime)->subDays(31)->toDateString(), Carbon::parse($this->endTime)->toDateString());
        $deviceNum = 300;
        if ($isNear7Days) {
            $deviceNum = 600;
        } elseif ($isNear30Days) {
            $deviceNum = 1000;
        }

        $newList = [];
        foreach ($currentList as $item) {
            if (!isset($apps[$item['developer_app_id']])) {
                continue;
            }

            // 过滤设备数量小于设备数量限制的
            if ($item['dev_num'] < $deviceNum) {
                continue;
            }

            // 获取环比数据
            $proportionItem = $proportionList[$item['developer_app_id']] ?? null;
            if ($proportionItem) {
                $item['proportion'] = $proportionItem['stutter'] != "0" ? bcadd(round(bcdiv(bcsub($item['stutter'], $proportionItem['stutter'], 6), $proportionItem['stutter'], 6) * 100, 2), 0, 2) : bcadd(round(bcmul($item['stutter'], 100, 6), 2), 0, 2);
            } else {
                $item['proportion'] = bcadd(round(bcmul($item['stutter'], 100, 6), 2), 0, 2);
            }

            $newList[] = [
                'proportion' => $item['proportion'],
                'stutter' => $item['stutter'],
                'app_id' => intval($item['developer_app_id']),
                'app_name' => $apps[$item['developer_app_id']]['app_name'],
                'app_icon' => $apps[$item['developer_app_id']]['app_icon'],
            ];
        }
        //排序
        array_multisort(array_column($newList, 'stutter'), SORT_DESC, $newList);
        //返回数据，只要前10个元素
        return $newList;
    }
}
