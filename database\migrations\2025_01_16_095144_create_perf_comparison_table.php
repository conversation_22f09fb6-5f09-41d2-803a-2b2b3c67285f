<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePerfComparisonTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('perf_comparison', function (Blueprint $table) {
            $table->string('uuid', 32)->comment('id');
            $table->string('creator', 64)->default('')->comment('创建者');
            $table->text('reports')->comment('报告数组');
            $table->timestamps();

            $table->unique('uuid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('perf_comparison');
    }
}
