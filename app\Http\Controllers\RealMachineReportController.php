<?php
/**
 * RealMachineReportController.php
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/11
 */

namespace App\Http\Controllers;

use App\Components\Scope\Scope;
use App\Model\RealMachine\RealMachineReport;
use App\Service\RealMachineReportService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class RealMachineReportController extends Controller
{
    private $report;
    private $reportService;
    private $request;

    public function __construct(Request $request)
    {
        $this->report = new RealMachineReport();
        $this->request = $request->all();
        $this->reportService = new RealMachineReportService($this->report, new Scope($request->toArray()));
    }

    /**
     * 上传报告接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2662
     * @return JsonResponse
     */
    public function store(): JsonResponse
    {
        $input = $this->request;
        $validator = Validator::make($input, [
            'report' => 'required|file|mimes:zip'
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }
        // 保存文件并返回保存的路径
        $reportUrl = $this->reportService->storeReport($input['report']);
        // 解压文件
        $this->reportService->extractReport($reportUrl);
        try {
            // 读取brief-report.json数据
            $data = $this->reportService->getData($reportUrl);
            if(empty($data)) {
                return $this->response(1005);
            }
            $input = array_merge($input, $data);
            // 在入库之前校验参数
            $this->report->store($input, true);
        } catch (Exception $e) {
            Log::error('真人真机保存数据报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(1005);
        }
        // 移动文件到对应的id目录下并删除源文件
        $isSuccess = $this->reportService->moveReport($reportUrl, $this->report->report_id);
        if (!$isSuccess) {
            return $this->response(1005);
        }
        return $this->response();
    }

    /**
     * 报告列表接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2663
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }
        try {
            [$list, $total] = $this->reportService->getReportList(['report_id', 'created_at']);
            return $this->response(0, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            Log::error('真人真机报告列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 趋势列表接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2718
     * @return JsonResponse
     */
    public function getTrendList(): JsonResponse
    {
        $validator = Validator::make($this->request, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }
        try {
            [$list, $total] = $this->reportService->getReportList(['report_id', 'created_at','platform', 'device_model',
                'remark', 'avg_cpu', 'score', 'jank_10min', 'avg_mem']);
            // 处理统计数据的趋势
            $list = $this->reportService->setStatisticsTrend($list->toArray());
            return $this->response(0, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            Log::error('真人真机趋势列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 报告更新接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2751
     * @param $reportId
     * @return JsonResponse
     */
    public function updateReport($reportId): JsonResponse
    {
        $input = $this->request;
        $validator = Validator::make($input, [
            'remark' => 'present|max:255'
        ]);
        if ($validator->fails()) {
            return $this->response(1000, [], $validator->errors());
        }
        try {
            $this->report::query()->where('report_id', $reportId)->update(['remark' => $input['remark']]);
            return $this->response();
        } catch (Exception $e) {
            Log::error('真人真机报告更新接口报错-reportId' . $reportId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 报告删除接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2872
     * @param $reportId
     * @return JsonResponse
     */
    public function delete($reportId): JsonResponse
    {
        try {
            $isSuccess = $this->reportService->deleteReport($reportId);
            if (!$isSuccess) {
                return $this->response(1005);
            }
            $this->report::query()->where('report_id', $reportId)->delete();
            return $this->response();
        } catch (Exception $e) {
            Log::error('真人真机删除接口报错-reportId:' . $reportId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }
}
