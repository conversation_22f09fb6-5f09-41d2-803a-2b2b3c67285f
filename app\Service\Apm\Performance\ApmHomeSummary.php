<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\PerformanceScoreData;

class ApmHomeSummary extends ApmBase
{
    /**
     * 获取列表数据
     *
     * @return array
     */
    public function getList(): array
    {
        $res = PerformanceScoreData::query()
            ->selectRaw("DATE({$this->mysql_apm_report_list_table}.created_at) as timestamp, count(*) as total, sum({$this->performance_score_data_table}.all_score) as score")
            ->join($this->mysql_apm_report_list_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
            })
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0)
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->groupBy('timestamp')
            ->getFromSR();

        // 处理数据
        $res = array_column($res, null, 'timestamp');

        // 遍历日期
        $data = [];
        foreach ($res as $key => $item) {
            $data[] = [
                'timestamp' => $key,
                'score' => $item['total'] ? floatval(bcdiv($item['score'], $item['total'], 2)) : 0,
            ];
        }

        //返回数据
        return $data;
    }
}
