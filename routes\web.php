<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::get('/', function () {
    return view('welcome');
});

Route::get('home/apmDevice', 'HomeSummaryController@apmDevice');
Route::get('home/apmScoreList', 'HomeSummaryController@apmScoreList');
Route::get('home/getWarningData', [App\Http\Controllers\Performance\ApmWarningController::class, 'getWarningData']);
Route::get('home/getWarningWeekData', [App\Http\Controllers\Performance\ApmWarningController::class, 'getWarningWeekData']);
Route::get('/api/getMonitorData', [App\Http\Controllers\Performance\ApmPerformanceStatController::class, 'monitor']);

Route::group(['middleware' => 'auth'], function () {
    //检测平台
    Route::group(['prefix' => 'checkout'], function () {
        Route::get('list', 'CheckoutController@list');
        Route::post('store', 'CheckoutController@store');
        Route::get('get-report/{checkoutId}', 'CheckoutController@getReport');
        Route::get('checkout/{checkout}', 'CheckoutController@checkout');
        Route::get('share/{checkoutId}', 'CheckoutController@share');
    });

    //公共接口
    Route::group(['prefix' => 'common'], function () {
        Route::get('share/{infoId}', 'CommonController@share');
        Route::post('shareReport', 'CommonController@shareReport');
    });

    //AB冗余资源检测
    Route::group(['prefix' => 'ab'], function () {
        //阈值配置
        Route::group(['prefix' => 'config'], function () {
            Route::get('current', 'ConfigController@getCurrentConfig');
            Route::get('update-current/{threshold_config_id?}', 'ConfigController@updateCurrentConfig');
            Route::get('list', 'ConfigController@list');
            Route::post('store/{threshold_config_id?}', 'ConfigController@store');
            Route::get('{threshold_config_id}', 'ConfigController@getContent');
            Route::delete('delete/{threshold_config_id}', 'ConfigController@delete');
        });
        //报表
        Route::group(['prefix' => 'report'], function () {
            Route::get('list', 'AssetBundleReportController@list');
            Route::post('update/{report_id}', 'AssetBundleReportController@updateReport');
            Route::get('{report_id}', 'AssetBundleReportController@getReport');
        });
    });

    //真人真机检测
    Route::group(['prefix' => 'realMachine'], function () {
        //报表
        Route::group(['prefix' => 'report'], function () {
            Route::get('list', 'RealMachineReportController@list');
            Route::get('trend-list', 'RealMachineReportController@getTrendList');
            Route::post('update/{report_id}', 'RealMachineReportController@updateReport');
            Route::delete('delete/{report_id}', 'RealMachineReportController@delete');
        });
    });

    //特效检测
    Route::group(['prefix' => 'special'], function () {
        //阈值配置
        Route::group(['prefix' => 'config'], function () {
            Route::get('current', 'SpecialConfigController@getCurrentConfig');
            Route::get('update-current/{threshold_config_id?}', 'SpecialConfigController@updateCurrentConfig');
            Route::get('list', 'SpecialConfigController@list');
            Route::post('store/{threshold_config_id?}', 'SpecialConfigController@store');
            Route::get('{threshold_config_id}', 'SpecialConfigController@getContent');
            Route::delete('delete/{threshold_config_id}', 'SpecialConfigController@delete');
        });
        //报表
        Route::group(['prefix' => 'report'], function () {
            Route::get('list', 'SpecialReportController@list');
            Route::post('update/{report_id}', 'SpecialReportController@updateReport');
            Route::get('{report_id}', 'SpecialReportController@getReport');
            Route::delete('delete/{report_id}', 'SpecialReportController@delete');
        });
    });

    //线上玩家性能数据
    Route::group(['prefix' => 'performance', 'namespace' => 'Performance'], function () {
        //报告
        Route::group(['prefix' => 'apm'], function () {
            Route::get('info', 'ApmPerformanceController@info'); //详情
            Route::get('tag', 'ApmPerformanceController@tag'); //标签
            Route::get('device', 'ApmPerformanceController@device'); //设备
            Route::get('game_version', 'ApmPerformanceController@gameVersion'); //游戏版本
            Route::get('dev_brand', 'ApmPerformanceController@devBrand'); //设备品牌
            Route::get('chart', 'ApmPerformanceController@chart'); //报表
            Route::get('single/device/list', 'ApmPerformanceController@singleDeviceList'); // 单设备最新数据记录列表
            Route::get('recent/device/info', 'ApmPerformanceController@recentDeviceInfo'); // 获取设备近30天数据总和
            Route::get('recent/play/matchNum', 'ApmPerformanceController@recentPlayerMatchNum'); // 获取近30天设备每日对局数
            Route::get('device/daily/performanceList', 'ApmPerformanceController@deviceDailyPerformanceList'); // 获取某日设备的性能数值列表
            Route::get('device/daily/reportList', 'ApmPerformanceController@deviceDailyReportList'); // 获取具体设备某一天的数据报告列表
            Route::get('score/belowEighty/reportList', 'ApmPerformanceController@scoreBelowEightyReportList'); // 获取最近10条低于80分的数据
            // 获取异常数据列表
            Route::get('exception/list', 'ApmPerformanceController@exceptionList');
            // 获取异常数据列表
            Route::get('exception/search', 'ApmPerformanceController@exceptionSearch');
            // 获取首页筛选列表
            Route::get('home/search', 'ApmPerformanceController@homeSearch');
            // 首页刷新数据
            Route::get('home/refresh', 'ApmPerformanceController@homeRefresh');
            // 搜索列表
            Route::get('search_list', 'ApmPerformanceController@searchList'); // 搜索列表
            // 获取详情标签统计
            Route::get('getTagStat', 'ApmPerformanceController@getTagStat');
        });
        //开关
        Route::group(['prefix' => 'switch'], function () {
            Route::get('list', 'ApmPerformanceSwitchController@list'); //列表
            Route::post('add', 'ApmPerformanceSwitchController@add'); //添加
            Route::post('edit', 'ApmPerformanceSwitchController@edit'); //编辑
            Route::post('status', 'ApmPerformanceSwitchController@status'); //状态
            Route::post('del', 'ApmPerformanceSwitchController@del'); //删除
        });
        //全局配置
        Route::group(['prefix' => 'config'], function () {
            Route::get('get', 'ApmPerformanceConfigController@get'); //获取
            Route::post('edit', 'ApmPerformanceConfigController@edit'); //编辑
        });
        //大盘统计
        Route::group(['prefix' => 'stat'], function () {
            Route::get('tips', 'ApmPerformanceStatController@tips'); //优化建议
            Route::get('basic', 'ApmPerformanceStatController@basic'); //基础信息、设备数、上报次数等
            Route::get('trend', 'ApmPerformanceStatController@trend'); //性能趋势
            Route::get('game_version', 'ApmPerformanceStatController@gameVersion'); //游戏版本排行榜
            Route::get('model', 'ApmPerformanceStatController@model'); //机型排行榜
            Route::get('tag', 'ApmPerformanceStatController@tag'); //标签排行榜
            Route::get('inner_version', 'ApmPerformanceStatController@innerVersion'); //资源版本
        });
        //分析
        Route::group(['prefix' => 'analysis'], function () {
            //游戏版本
            Route::group(['prefix' => 'game_version'], function () {
                Route::get('summary', 'ApmGameVersionAnalysisController@summary'); //达标和不达标概况
                Route::get('top', 'ApmGameVersionAnalysisController@top'); //前10条排行榜
                Route::get('list', 'ApmGameVersionAnalysisController@list'); //列表
            });
            //机型
            Route::group(['prefix' => 'model'], function () {
                Route::get('brand_summary', 'ApmDevModelAnalysisController@brandSummary'); //品牌达标和不达标概况
                Route::get('model_summary', 'ApmDevModelAnalysisController@modelSummary'); //机型达标和不达标概况
                Route::get('top', 'ApmDevModelAnalysisController@top'); //前10条排行榜
                Route::get('list', 'ApmDevModelAnalysisController@list'); //列表
                Route::get('export', 'ApmDevModelAnalysisController@export'); //导出
            });
            //标签
            Route::group(['prefix' => 'tag'], function () {
                Route::get('summary', 'ApmTagAnalysisController@summary'); //达标和不达标概况
                Route::get('top', 'ApmTagAnalysisController@top'); //前10条排行榜
                Route::get('list', 'ApmTagAnalysisController@list'); //列表
            });
        });
        //预警
        Route::group(['prefix' => 'warning'], function () {
            Route::get('list', 'ApmWarningController@list'); //列表
            Route::post('store/{warning_id?}', 'ApmWarningController@store'); //添加和编辑
            Route::delete('delete/{warning_id}', 'ApmWarningController@delete'); //删除
            Route::get('update-status/{warning_id}', 'ApmWarningController@updateStatus'); //更新状态
            Route::get('{warning_id}', 'ApmWarningController@getContent'); //获取预警内容
        });
        // 分析对比
        Route::group(['prefix' => 'contrast'], function () {
            Route::get('game_version_list', 'ApmPerformanceContrastController@gameCodeVersion');
            Route::get('game_version_dev_list', 'ApmPerformanceContrastController@gameCodeVersionDev');
            Route::get('game_version_select', 'ApmPerformanceContrastController@gameCodeVersionSelect');
            Route::get('tag_list', 'ApmPerformanceContrastController@tag');
            Route::get('tag_dev_list', 'ApmPerformanceContrastController@tagDev');
            Route::get('tag_select', 'ApmPerformanceContrastController@tagSelect');
        });
        // 负责人巡检配置路由
        Route::group(['prefix' => 'webhook'], function () {
            Route::get('/list', 'ApmLeaderWebhookController@list');
            Route::post('/add', 'ApmLeaderWebhookController@add');
            Route::post('/edit', 'ApmLeaderWebhookController@edit');
            Route::post('/del', 'ApmLeaderWebhookController@delete');
            Route::post('/status', 'ApmLeaderWebhookController@status');
            Route::get('/stat', 'ApmLeaderWebhookController@stat');
        });
    });

    //通用性能检测
    Route::group(['prefix' => 'perf', 'namespace' => 'Perf'], function () {
        //报表
        Route::group(['prefix' => 'report'], function () {
            Route::get('list', 'PerfReportController@list');
            Route::get('project_list', 'PerfReportController@projectList');
            Route::get('bookmark_list', 'PerfReportController@bookmarkList');
            Route::delete('delete/{report_id}', 'PerfReportController@delete');
            Route::post('rename/{report_id}', 'PerfReportController@rename');
            Route::post('new', 'PerfReportController@newReport');
            Route::get('fragment_export', 'PerfReportController@fragmentExport');
            Route::get('export', 'PerfReportController@export');
            Route::get('{report_id}', 'PerfReportController@getReport')->where('report_id', '[0-9]+');
        });
        //项目
        Route::group(['prefix' => 'project'], function () {
            Route::get('list', 'PerfProjectController@list');
            Route::post('add', 'PerfProjectController@add');
            Route::post('edit', 'PerfProjectController@edit');
            Route::post('del', 'PerfProjectController@del');
        });
        //收藏夹
        Route::group(['prefix' => 'bookmark'], function () {
            Route::get('list', 'PerfBookmarkController@list');
            Route::post('add', 'PerfBookmarkController@add');
            Route::post('edit', 'PerfBookmarkController@edit');
            Route::post('del', 'PerfBookmarkController@del');
            Route::post('operate', 'PerfBookmarkController@operate');
        });
        //标签
        Route::group(['prefix' => 'tag'], function () {
            Route::get('get', 'PerfReportTagController@get');
            Route::post('edit', 'PerfReportTagController@edit');
            Route::get('stat', 'PerfReportTagController@stat');
        });
        Route::group(['prefix' => 'comparison'], function () {
            Route::post('store', 'PerfComparisonController@store');
            Route::get('list', 'PerfComparisonController@list');
            Route::get('{uuid}', 'PerfComparisonController@get');
        });
    });

    /**
     * 首页概览统计
     */
    Route::group(['prefix' => 'home'], function () {
        Route::get('abSummary', 'HomeSummaryController@abSummary');
        Route::get('rmSummary', 'HomeSummaryController@rmSummary');
        Route::get('srSummary', 'HomeSummaryController@srSummary');
        Route::get('apmSummary', 'HomeSummaryController@apmSummary');
        //Route::get('apmDevice', 'HomeSummaryController@apmDevice');
        Route::get('apmScore', 'HomeSummaryController@apmScore');
        Route::get('apmStutter', 'HomeSummaryController@apmStutter');
        Route::get('apmNetDelay', 'HomeSummaryController@apmNetDelay');
        //Route::get('apmScoreList', 'HomeSummaryController@apmScoreList');
    });

    /**
     * 引擎组埋点
     */
    Route::group(['prefix' => 'track'], function () {
        Route::get('stat', [App\Http\Controllers\Track\TrackEventController::class, 'stat']);
        Route::get('all', [App\Http\Controllers\Track\TrackEventController::class, 'all']);
    });
});

//对外api接口
Route::group(['prefix' => 'api'], function () {
    //检测平台
    Route::group(['prefix' => 'checkout'], function () {
        Route::get('get-report/{checkoutId}', 'CheckoutController@getReport')->middleware('shareRecord');
    });
});

//API接口
Route::group(['middleware' => 'sign', 'prefix' => 'api'], function () {
    //AB冗余资源检测
    Route::group(['prefix' => 'ab'], function () {
        //获取报表数据
        Route::group(['prefix' => 'report'], function () {
            Route::post('store', 'AssetBundleReportController@store');
        });
    });
    //真人真机检测
    Route::group(['prefix' => 'realMachine'], function () {
        //获取报表数据
        Route::group(['prefix' => 'report'], function () {
            Route::post('store', 'RealMachineReportController@store');
        });
    });
    //特效检测检测
    Route::group(['prefix' => 'special'], function () {
        //获取报表数据
        Route::group(['prefix' => 'report'], function () {
            Route::post('store', 'SpecialReportController@store');
        });
    });
});

//API接口
Route::group(['prefix' => 'api', 'namespace' => 'Perf'], function () {
    //通用性能检测
    Route::group(['prefix' => 'perf'], function () {
        //获取报表数据
        Route::group(['prefix' => 'report'], function () {
            Route::post('store', 'PerfReportController@store')->middleware('perfSign');
            Route::post('store_v2', 'PerfReportController@store')->middleware('perfSignV2');
        });

        // 设备
        Route::group(['prefix' => 'device'], function () {
            Route::post('useDuration', 'PerfDeviceUseDurationController@recordDeviceUserDuration')->middleware('perfSign');
        });
    });
});
