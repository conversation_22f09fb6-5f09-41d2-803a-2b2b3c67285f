<?php

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class FpsInfoChart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->selectRaw(
                'array_length(frame_times) as frame_times_count,
                array_sum(frame_times) as frame_times_sum,
                jank_count,
                big_jank_count,
                jank_time,
                game_info_ts,
                tags_info,
                app_state,
                perf_data_ts'
            )
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['fpsInfo'][] = [   //按照时间戳分组
                'fps' => empty($item['frame_times_sum']) ? 0 : bcmul(bcdiv($item['frame_times_count'], $item['frame_times_sum'], 6), 1000, 2),  //计算fps，保留两位小数，计算规则：帧数 / 帧时间和 * 1000，单位是毫秒所以要乘以1000
                'Jank' => $item['jank_count'],  //卡顿次数
                'BigJank' => $item['big_jank_count'],   //大卡顿次数
                'stutter' => $item['frame_times_sum'] == 0 ? 0 : (bcdiv($item['jank_time'], $item['frame_times_sum'], 2) * 100),    //卡顿时间占比，保留两位小数，计算规则：卡顿时间 / 帧时间和 * 100，单位是百分比所以要乘以100
                'ts' => $item['game_info_ts'],  //游戏时间戳
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }
        return array_values($list);
    }
}
