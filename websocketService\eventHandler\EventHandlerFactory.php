<?php
/**
 * socket处理类的工厂
 **/

namespace websocketService\eventHandler;


class EventHandlerFactory
{
    /**
     * 事件映射处理器map
     * -1限定为心跳检测
     */
    const EventHandlerMap = [
        1 => CheckoutHandler::class,
        2 => CheckoutReportHandler::class,
    ];

    /**获取一个事件处理器
     * @param $type
     * @param $frame
     * @param $message
     * @return mixed
     */
    public static function getSocketMessageHandler($type, $frame, $message)
    {
        $class = self::EventHandlerMap[$type];
        return new $class($frame, $message);
    }
}
