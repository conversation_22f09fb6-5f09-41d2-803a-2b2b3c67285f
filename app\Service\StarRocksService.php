<?php

/**
 * TODO:StarRocks操作方法
 */

namespace App\Service;

use Illuminate\Support\Facades\Log;

class StarRocksService
{
    //连接句柄
    private $conn;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $starRocks = config('starRocks');
        $conn = new \mysqli($starRocks['starRocks_host'], $starRocks['starRocks_username'], $starRocks['starRocks_password'], $starRocks['starRocks_database'], $starRocks['starRocks_port']);
        if ($conn->connect_error) {
            Log::error("starRocks连接失败: " . $conn->connect_error);
            die("starRocks连接失败: " . $conn->connect_error);
        }
        $this->conn = $conn;
    }

    /**
     * 查询
     * @param $sql
     * @return array
     */
    public function query($sql)
    {
        $result = $this->conn->query($sql);
        $res = $result->fetch_all(MYSQLI_ASSOC);
        $result->free_result();
        return $res;
    }

    /**
     * 关闭连接
     */
    public function close()
    {
        $this->conn->close();
    }

    /**
     * 析构函数
     */
    public function __destruct()
    {
        $this->close();
    }
}
