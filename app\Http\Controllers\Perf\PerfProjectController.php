<?php

/**
 * 线下性能检测项目管理控制器
 * @desc 线下性能检测项目管理控制器
 * <AUTHOR> chenji<PERSON><EMAIL>
 * @date 2024/04/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Perf;

use App\Components\ApiResponse\StatusCode;
use App\Components\Helper\Curl;
use App\Http\Controllers\Controller;
use App\Http\Validation\Perf\ProjectValidation;
use App\Model\Perf\Project;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PerfProjectController extends Controller
{
    /**
     * 每页显示条数
     *
     * @var int
     */
    const PER_PAGE = 10;

    /**
     * 项目列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3489
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ProjectValidation::build()
            ->limit()
            ->page()
            ->validate();

        try {
            //获取数据
            $res = Project::query()
                ->orderByDesc('id')
                ->paginate($params['limit'] ?? self::PER_PAGE);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['list' => $res->items(), 'total' => $res->total()]);
        } catch (Exception $e) {
            \Log::error('获取项目列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 添加项目
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3492
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ProjectValidation::build()
            ->title()
            ->packageName()
            ->validate();

        try {
            //添加项目
            Project::query()->create([
                'title' => $params['title'],
                'package_name' => explode(',', $params['package_name']),
                'operator' => Auth::user()->alias,
                'staff_id' => Auth::user()->username,
            ]);
            // 发送企微通知
            $this->sendWxMsg(Auth::user()->alias . " 增加【{$params['title']}】项目配置，增加的包名为：{$params['package_name']}");
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('添加项目接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 编辑项目
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3494
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ProjectValidation::build()
            ->projectId()
            ->title()
            ->packageName()
            ->validate();

        try {
            $project = Project::query()->find($params['project_id']);
            //判断是否存在
            if (empty($project)) return $this->response(StatusCode::C_PARAM_ERROR);
            //修改开关
            $project->update([
                'title' => $params['title'],
                'package_name' => explode(',', $params['package_name']),
                'operator' => Auth::user()->alias,
                'staff_id' => Auth::user()->username,
            ]);
            // 发送企微通知
            $this->sendWxMsg(Auth::user()->alias . " 修改【{$params['title']}】项目配置，修改后包名为：{$params['package_name']}");
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('编辑项目接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除项目
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3496
     * @param Request $request
     * @return JsonResponse
     */
    public function del(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ProjectValidation::build()->projectId()->validate();

        try {
            //删除项目
            $project = Project::query()->where('id', $params['project_id'])->first();
            // 判断是否存在
            if ($project) {
                $project->delete();
                // 发送企微通知
                $this->sendWxMsg(Auth::user()->alias . " 删除【{$project->title}】项目");
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('删除项目接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 发送企业微信通知
     *
     * @param string $msg
     * @return void
     */
    private function sendWxMsg(string $msg)
    {
        // 判断环境
        $envText = '测试环境';
        if (config('app.env') == 'production') {
            $envText = '正式环境';
        }
        // 企业微信 webhook 地址
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3c871c88-db47-4abc-a89a-305f1b4d65c4';
        // 请求参数
        $params = [
            'msgtype' => 'text',
            'text' => [
                'content' => "【{$envText}】PerfCheck 项目配置修改通知！\n通知内容：" . $msg,
            ]
        ];
        // 发送企微通知消息
        $response = Curl::json($url, $params);
        // 打印日志
        Log::info('企业微信机器人信息推送，结果：' . $response);
    }
}
