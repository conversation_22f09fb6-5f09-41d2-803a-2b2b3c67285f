<?php
/**
 * Created by PhpStorm.
 * User: weiming Email: <EMAIL>
 * Date: 2020/4/15
 * Time: 17:01
 */

namespace App\Components\Helper;


class CommonApiHelper
{

    /**
     * 对数据进行签名，签名规则同上
     * 注意:如果需要去除空值，请在传入数据之前自行处理
     * @param array $data 待签名数据
     * @param string $secret 安全码
     * @param string //签名方式
     * @param //function $func 签名方法(默认: 'sha1')
     * @return string 签名字符串
     */
    public static function sign($data, $secret, $func = 'sha1')
    {
        $qs = self::getQueryStringForSign($data);
        $sign = call_user_func_array($func, [$qs . $secret]);
        return $sign;
    }

    /**
     * 生成QueryString
     * @param $data
     * @return string
     */
    public static function getQueryStringForSign($data)
    {
        // 过滤参数
        $params = [];
        foreach ($data as $k => $v) {
            if ('sign' == $k) continue;

            $params[$k] = $v;
        }
        // 排序参数
        ksort($params);
        reset($params);

        // 构建QueryString
        $p = [];
        foreach ($params as $k => $v) {
            $p[] = rawurlencode($k) . "=" . rawurlencode($v);
        }
        //java和php url编码对*处理不同，java保留，php处理
        $encoded = strtr(implode("&", $p), ['%2A' => '*']);
        //Logger::info("签名串：".$encoded);

        return $encoded;
    }

}
