<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeviceCodeDateUniqueIndexToPerfDeviceUseDurationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('perf_device_use_duration', function (Blueprint $table) {
            $table->dropIndex('perf_device_use_duration_device_code_date_index');
            $table->unique(['device_code', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('perf_device_use_duration', function (Blueprint $table) {
            $table->index(['device_code', 'date']);
            $table->dropUnique('perf_device_use_duration_device_code_date_unique');
        });
    }
}
