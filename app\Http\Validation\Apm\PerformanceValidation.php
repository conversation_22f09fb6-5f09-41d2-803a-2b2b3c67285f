<?php

/**
 * 线上性能相关接口的校验类
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;
use Illuminate\Validation\Rule;

/**
 * @method static PerformanceValidation build()
 */
class PerformanceValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): PerformanceValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 报告ID校验
     *
     * @return $this
     */
    public function reportId(): PerformanceValidation
    {
        $this->rules['report_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 类型校验
     *
     * @return $this
     */
    public function type(): PerformanceValidation
    {
        $this->rules['type'] = 'required|string';
        return $this;
    }

    /**
     * 标签校验
     *
     * @return $this
     */
    public function tag(): PerformanceValidation
    {
        $this->rules['tag'] = 'string';
        return $this;
    }

    /**
     * 设备ID校验
     *
     * @return $this
     */
    public function devStr(): PerformanceValidation
    {
        $this->rules['dev_str'] = 'required|string';
        return $this;
    }

    /**
     * 设备ID可以为空校验
     *
     * @return $this
     */
    public function devStrNullable(): PerformanceValidation
    {
        $this->rules['dev_str'] = 'string';
        return $this;
    }

    /**
     * 每页条数校验
     *
     * @return $this
     */
    public function limit(): PerformanceValidation
    {
        $this->rules['limit'] = 'integer|min:10|max:100';
        return $this;
    }

    /**
     * 页码校验
     *
     * @return $this
     */
    public function page(): PerformanceValidation
    {
        $this->rules['page'] = 'integer|min:1';
        return $this;
    }

    /**
     * app版本号校验
     *
     * @return $this
     */
    public function appVersionCode(): PerformanceValidation
    {
        $this->rules['app_version_code'] = 'string';
        return $this;
    }

    /**
     * 游戏版本号校验
     *
     * @return $this
     */
    public function gameVersionCode(): PerformanceValidation
    {
        $this->rules['game_version_code'] = 'string';
        return $this;
    }

    /**
     * 开始时间校验
     *
     * @return $this
     */
    public function startTime(): PerformanceValidation
    {
        $this->rules['start_time'] = 'date';
        return $this;
    }

    /**
     * 结束时间校验
     *
     * @return $this
     */
    public function endTime(): PerformanceValidation
    {
        $this->rules['end_time'] = 'date';
        return $this;
    }

    /**
     * 设备品牌校验
     *
     * @return $this
     */
    public function devBrand(): PerformanceValidation
    {
        $this->rules['dev_brand'] = 'string';
        return $this;
    }

    /**
     * 设备型号校验
     *
     * @return $this
     */
    public function devModel(): PerformanceValidation
    {
        $this->rules['dev_model'] = 'string';
        return $this;
    }

    /**
     * 平台校验
     *
     * @return $this
     */
    public function osType(): PerformanceValidation
    {
        $this->rules['os_type'] = 'string';
        return $this;
    }

    /**
     * 用户标识校验
     *
     * @return $this
     */
    public function uid(): PerformanceValidation
    {
        $this->rules['uid'] = 'string';
        return $this;
    }

    /**
     * 时长校验
     *
     * @return $this
     */
    public function duration(): PerformanceValidation
    {
        $this->rules['duration'] = 'integer|min:1';
        return $this;
    }

    /**
     * 时间允许为空校验
     *
     * @return $this
     */
    public function dateNullable(): PerformanceValidation
    {
        $this->rules['date'] = 'date';
        return $this;
    }

    /**
     * 时间校验
     *
     * @return $this
     */
    public function date(): PerformanceValidation
    {
        $this->rules['date'] = 'required|date';
        return $this;
    }

    /**
     * 纵坐标校验
     *
     * @return $this
     */
    public function dataOrdinate(): PerformanceValidation
    {
        $this->rules['data_ordinate'] = ['required', Rule::in(['fps', 'stutter', 'jank', 'bigJank', 'memory', 'temperature', 'electric'])];
        return $this;
    }

    /**
     * 排序字段校验
     *
     * @return $this
     */
    public function sortFieldNullable(): PerformanceValidation
    {
        $this->rules['sort_field'] = Rule::in(['all_score', 'duration', 'created_at', 'avg_fps', 'avg_used_memory', 'stutter', 'exception_count', 'fps_jitter_count_10']);
        return $this;
    }

    /**
     * 排序方式校验
     *
     * @return $this
     */
    public function sortTypeNullable(): PerformanceValidation
    {
        $this->rules['sort_type'] = Rule::in(['desc', 'asc']);
        return $this;
    }

    /**
     * 是否模拟器校验
     *
     * @return $this
     */
    public function isSimulator(): PerformanceValidation
    {
        $this->rules['is_simulator'] = 'nullable|integer';
        return $this;
    }

    /**
     * 设备挡位校验
     *
     * @return $this
     */
    public function deviceTier(): PerformanceValidation
    {
        $this->rules['device_tier'] = 'nullable|integer';
        return $this;
    }
}
