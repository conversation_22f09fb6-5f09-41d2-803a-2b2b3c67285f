<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApmSwitchConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('apm_switch_config', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedTinyInteger('os_type')->default(1)->comment('平台，1:android、2:ios、3:pc');
            $table->unsignedInteger('device_num')->default(0)->comment('默认为0，0:不做限制');
            $table->string('start_time')->default('')->comment('开始时间');
            $table->string('end_time')->default('')->comment('结束时间');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态，1:开启、0:关闭');
            $table->string('package_name', 2550)->default('')->comment('包名');
            $table->timestamps();

            $table->index('developer_app_id');
        });
        \DB::connection('apm')->statement("ALTER TABLE `apm_switch_config` comment 'apm-开关配置表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('apm_switch_config');
    }
}
