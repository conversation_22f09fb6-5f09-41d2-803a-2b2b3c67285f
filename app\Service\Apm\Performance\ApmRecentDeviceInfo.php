<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use Carbon\Carbon;

class ApmRecentDeviceInfo extends ApmBase
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        $res = $this->getCountAndMaxId();

        $data = [
            'dev_brand' => '',      //设备品牌
            'dev_model' => '',      //设备型号
            'dev_str' => $this->params['dev_str'],  //设备ID
            'game_version_code' => '',      //游戏版本号
            'record_total' => $res['num'],  //记录总数
        ];

        if ($res['id'] == 0) return $data;

        $res = $this->getNewVersionAndModel($res['id']);

        $data['dev_brand'] = $res['dev_brand'];
        $data['dev_model'] = ApmModelMap::getValue($res['dev_model']);
        $data['game_version_code'] = $res['game_version_code'];

        return $data;
    }

    /**
     * 获取数量和最大的报告ID
     *
     * @return int[]
     */
    protected function getCountAndMaxId(): array
    {
        $res = MysqlApmReportList::query()
            ->selectRaw("count({$this->mysql_apm_report_list_table}.id) as num, max({$this->mysql_apm_report_list_table}.id) as id")
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [Carbon::now()->subDays(29)->startOfDay()->toDateTimeString(), Carbon::now()->endOfDay()->toDateTimeString()])
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where("{$this->mysql_apm_report_list_table}.dev_str", $this->params['dev_str'])
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->firstFromSR();

        if (!empty($res)) {
            return $res;
        }

        return [
            'num' => 0,
            'id' => 0
        ];
    }

    /**
     * 获取最新的版本和机型、品牌
     *
     * @param $id
     * @return array
     */
    protected function getNewVersionAndModel($id): array
    {
        return MysqlApmReportList::query()
            ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name as game_version_code,{$this->mysql_apm_device_list_table}.dev_brand as dev_brand,{$this->mysql_apm_device_list_table}.dev_model as dev_model")
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->where("{$this->mysql_apm_report_list_table}.id", $id)
            ->firstFromSR();
    }
}
