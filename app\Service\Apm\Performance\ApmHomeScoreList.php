<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\StarRocksDB;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApmHomeScoreList extends ApmBase
{
    private function getTimeList($type)
    {
        $timeList = ['current' => [], 'latest' => []];
        switch ($type) {
            case 'day':
                $timeList['current'] = [Carbon::now()->startOfDay()->toDateTimeString(), Carbon::now()->endOfDay()->toDateTimeString()];
                $timeList['latest'] = [Carbon::yesterday()->startOfDay()->toDateTimeString(), Carbon::yesterday()->endOfDay()->toDateTimeString()];
                break;
            case 'week':
                $timeList['current'] = [Carbon::now()->startOfWeek()->toDateTimeString(), Carbon::now()->endOfWeek()->toDateTimeString()];
                $timeList['latest'] = [Carbon::now()->subWeek()->startOfWeek()->toDateTimeString(), Carbon::now()->subWeek()->endOfWeek()->toDateTimeString()];
                break;
            case 'month':
                $timeList['current'] = [Carbon::now()->startOfMonth()->toDateTimeString(), Carbon::now()->endOfMonth()->toDateTimeString()];
                $timeList['latest'] = [Carbon::now()->subMonth()->startOfMonth()->toDateTimeString(), Carbon::now()->subMonth()->endOfMonth()->toDateTimeString()];
                break;
        }
        return $timeList;
    }

    /**
     * 获取列表数据
     *
     * @return array
     */
    public function getList($configs, $apps, $type): array
    {
        $timeList = $this->getTimeList($type);
        $subSqls = [];
        foreach ($timeList as $key => $item) {
            $subSqls[$key] = StarRocksDB::toSql(
                DB::table($this->mysql_apm_report_list_table)
                    ->selectRaw("{$this->mysql_apm_report_list_table}.developer_app_id, {$this->performance_score_data_table}.all_score, {$this->performance_stat_data_table}.duration")
                    ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
                    ->join($this->performance_score_data_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")
                    ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                        return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                            ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
                    })
                    ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", $item)
                    ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0)
            );
        }

        //获取数据
        $list['current'] = [];
        $list['latest'] = [];
        foreach ($subSqls as $key => $subSql) {
            $list[$key] = StarRocksDB::query(
                DB::table(Db::raw("({$subSql}) as t"))
                    ->selectRaw('developer_app_id, sum(all_score) as score, count(*) as num')
                    ->where(function ($query) use ($configs) {
                        foreach ($configs as $id => $duration) {
                            $query = $query->orWhereRaw("(developer_app_id = {$id} and duration > {$duration})");
                        }
                    })
                    ->groupBy('developer_app_id')
            )->get();
        }

        //返回数据
        return $this->handleData(array_column($list['current'], null, 'developer_app_id'), array_column($list['latest'], null, 'developer_app_id'), $apps);
    }

    /**
     * 处理数据
     *
     * @param array $currentList
     * @param array $latestList
     * @param array $apps
     * @return array
     */
    private function handleData($currentList, $latestList, $apps)
    {
        $list = [];
        foreach ($apps as $item) {
            $current = $currentList[$item['id']] ?? ['num' => 0];
            $latest = $latestList[$item['id']] ?? ['num' => 0];
            $list[] = [
                'current' => $current['num'] ? floatval(bcdiv($current['score'], $current['num'], 2)) : 0,
                'latest' => $latest['num'] ? floatval(bcdiv($latest['score'], $latest['num'], 2)) : 0,
                'app_id' => intval($item['id']),
            ];
        }
        return $list;
    }
}
