<?php

/**
 * 设备机型信息表
 */

namespace App\Model\MobileDevice;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DeviceName extends Model
{
    protected $connection = 'developer_data';

    protected $table = 'device_name';

    public function deviceConfig(): HasOne
    {
        return $this->hasOne(DeviceConfig::class, 'hardware_model_names', 'hardware_model_names');
    }
}
