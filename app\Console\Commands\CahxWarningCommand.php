<?php

/**
 * 长安幻想预警脚本
 * @desc 长安幻想预警脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/10/15
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Service\Apm\Performance\ApmTrait;
use App\Service\Push\WXGroupNoticeService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CahxWarningCommand extends Command
{
    /**
     * 引入监控的trait
     */
    use ApmTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cahx:warning';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '长安幻想预警脚本';

    /**
     * 请求参数
     *
     * @var array
     */
    private $params = [
        'developer_app_id' => 6,
    ];

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        //获取报告数量、设备数量、平均分、低于标准分数量
        $selectRaw = <<<COLUMNS
{$this->mysql_apm_device_list_table}.os_type,
count(distinct {$this->mysql_apm_report_list_table}.dev_str) as dev_num,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness_score
COLUMNS;
        $startTime = Carbon::now()->startOfDay()->toDateTimeString();
        $endTime = Carbon::now()->endOfDay()->toDateTimeString();

        $list = PerformanceScoreData::query() //查询性能分数表
            ->selectRaw($selectRaw) //计算平均分
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$startTime, $endTime]) //过滤掉不在时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->whereIn("{$this->mysql_apm_device_list_table}.os_type", [1, 2])
            ->groupBy("{$this->mysql_apm_device_list_table}.os_type") //分组
            ->getFromSR();

        $newList = [];
        foreach ($list as $item) {
            $newList[$item['os_type']] = $item;
        }

        // 获取推送的阈值
        $osTypeValue = [
            1 => 5000,
            2 => 5000,
        ];
        // 推送
        foreach ($newList as $osType => $data) {
            // 判断启动设备是否大于指定的值
            if (($data['dev_num'] ?? 0) < $osTypeValue[$osType]) {
                continue;
            }
            // 判断是否已经推送
            if (Cache::has($this->getKey($osType))) {
                continue;
            }
            $this->sendMessage($osType, $data);
        }
    }

    /**
     * 推送消息
     *
     * @param $osType
     * @param $data
     * @return void
     */
    private function sendMessage($osType, $data)
    {
        $text = '';
        // 获取操作系统名称
        $osTypeText = $osType == 1 ? 'Android' : 'iOS';
        $date = date('Y年m月d H:i');

        if ($osType == 1 && $data['smoothness_score'] >= 6) {
            $text = "长安性能预警 请各单位注意 \r\n\r\n<font color='red'>卡顿率高达{$data['smoothness_score']}%（建议值3%，现阶段6%）。{$osTypeText}系统。共统计设备{$data['dev_num']}台</font>\r\n\r\n报警时间：{$date}\r\n* 详情请查看效能后台。该报警同类型问题，一天只触发一次。";
        }
        if ($osType == 2 && $data['smoothness_score'] >= 3) {
            $text = "长安性能预警 请各单位注意 \r\n\r\n<font color='red'>卡顿率高达{$data['smoothness_score']}%（建议值3%）。{$osTypeText}系统。共统计设备{$data['dev_num']}台</font>\r\n\r\n报警时间：{$date}\r\n* 详情请查看效能后台。该报警同类型问题，一天只触发一次。";
        }

        if ($text) {
            $urls = [
                'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=552bd469-008e-48de-b232-4a5cb01a7943',
                'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d8ab1d6b-303c-45e8-8b21-8fddde2f8f15',
            ];
            foreach ($urls as $url) {
                $service = new WXGroupNoticeService($url, true);
                $service->wxGroupNotify($text, 'markdown');
            }
            Cache::put($this->getKey($osType), 1, 86400);
        }
    }

    /**
     * 获取key
     *
     * @param $osType
     * @return string
     */
    private function getKey($osType)
    {
        return 'apm:cahx:warning:' . date('Ymd') . ":" . $osType;
    }
}
