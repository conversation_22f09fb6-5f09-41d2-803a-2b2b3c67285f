<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\ApmGlobalConfig;
use App\Model\Apm\StarRocks\MysqlApmDeviceList;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\PerformanceData;
use App\Model\Apm\StarRocks\PerformanceExtendData;
use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Model\Apm\StarRocks\PerformanceStatData;
use App\Model\Apm\StarRocks\PerformanceTagScoreData;
use App\Model\Apm\StarRocks\PerformanceTagStatData;

trait ApmTrait
{
    /**
     * starRocks库名
     *
     * @var string
     */
    protected $mysql_apm_device_list_table = MysqlApmDeviceList::TABLE_NAME;
    protected $mysql_apm_report_list_table = MysqlApmReportList::TABLE_NAME;
    protected $performance_data_table = PerformanceData::TABLE_NAME;
    protected $performance_stat_data_table = PerformanceStatData::TABLE_NAME;
    protected $performance_score_data_table = PerformanceScoreData::TABLE_NAME;
    protected $performance_tag_score_data_table = PerformanceTagScoreData::TABLE_NAME;
    protected $performance_tag_stat_data_table = PerformanceTagStatData::TABLE_NAME;
    protected $performance_extend_data_table = PerformanceExtendData::TABLE_NAME;

    /**
     * 标准分数
     *
     * @var int
     */
    protected $standardScore = ApmBase::STANDARD_SCORE;

    /**
     * 获取最少时长
     *
     * @return int
     */
    protected function getMinDuration(): int
    {
        //获取全局配置
        $config = ApmGlobalConfig::query()->find($this->params['developer_app_id']) ?? [];
        $minDuration = $config['min_duration'] ?? 0;
        //如果有传入采集时长则取最大值
        if (isset($this->params['duration'])) {
            //单位是分钟要转成秒
            $duration = $this->params['duration'] * 60;
            //取最大值
            $minDuration = max($minDuration, $duration);
        }
        return $minDuration;
    }

    /**
     * 获取异常标记次数阈值
     *
     * @return int
     */
    protected function getExceptionMarkVal(): int
    {
        //获取全局配置
        $config = ApmGlobalConfig::query()->find($this->params['developer_app_id']) ?? [];
        return $config['exception_mark_val'] ?? 80;
    }

    /**
     * 处理列表数据
     *
     * @param array $data
     * @return array
     */
    protected function handleListData(array $data): array
    {
        foreach ($data as $key => $value) {
            //处理dev_model
            if (in_array($key, ['dev_model', 'model'], true)) {
                $data[$key] = ApmModelMap::getValue($value);
            }
            //处理为null的内容
            if (is_null($value)) {
                $data[$key] = "0";
            } elseif (is_array($value)) {
                $data[$key] = $this->handleListData($value);
            }
        }
        return $data;
    }
}
