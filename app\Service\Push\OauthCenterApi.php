<?php

namespace App\Service\Push;

use App\Components\Helper\Curl;
use Illuminate\Support\Facades\Log;

class OauthCenterApi
{
    //一般key
    const KEY = 'oU0lD8GRVpvYfYUq6ensuQtHUkwtE0o3';

    /**
     * 获取实例
     *
     * @return OauthCenterApi
     */
    public static function getInstance(): OauthCenterApi
    {
        return new static();
    }

    /**
     * 微信 信息推送接口
     * @param string|array $username 工号 支持多个 |分割 sy0001|sy0002
     * @param string $message 消息内容 支持a标签和换行
     * @return array
     */
    public function sendWXMsg($username, string $message): array
    {
        is_array($username) && $username = join('|', $username);
        if (config('app.env') == 'production') {
            return $this->sendWXMsgFact($username, $message);
        } elseif (config('app.env') == 'local') {
            Log::info("本地发送企业微信通知: username: {$username}, message: {$message}");
            return [];
        } else {
            return $this->sendWXMsgFact($username, $message);
        }
    }

    /**
     * 真正企微微信推送信息接口
     * @param $username
     * @param $message
     * @return array
     */
    public function sendWXMsgFact($username, $message): array
    {
        $time = time();
        $query = [
            'userid' => $username,
            'message' => $message,
            'dateTime' => $time,
            'sign' => md5($username . self::KEY . $time),
            'id' => 103,
        ];
        $url = $this->getUrl();
        //微信通知推送消息接口
        $ret = Curl::get($url, $query);
        //解析结果
        $ret = json_decode($ret, true);
        //判断是否成功
        if (!$ret['success']) {
            Log::error("发送企业微信请求失败: username: {$username}, message: {$message}");
            return $this->setResult(false, $ret['message']);
        }
        Log::info("发送企业微信通知成功: username: {$username}, message: {$message}");
        return $this->setResult(true, $ret['message']);
    }

    /**
     * 获取地址
     * @return string
     */
    private function getUrl(): string
    {
        return 'https://oauthcenter.shiyuegame.com/push';
    }

    /**
     * 格式话返回
     * @param bool $success
     * @param $message
     * @return array
     */
    private function setResult(bool $success, $message): array
    {
        return compact('success', 'message');
    }
}
