<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsCurrentConfigToAbThresholdConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('ab_threshold_config', function (Blueprint $table) {
            //
            $table->unsignedTinyInteger('is_current_config')->default(0)
                ->comment('是否为当前配置(1是,0不是)');
            $table->unique(['developer_app_id', 'name'], 'uni_developer_app_id_name', 'BTREE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('ab_threshold_config', function (Blueprint $table) {
            $table->dropColumn('is_current_config');
            $table->dropUnique(['developer_app_id', 'name']);
        });
    }
}
