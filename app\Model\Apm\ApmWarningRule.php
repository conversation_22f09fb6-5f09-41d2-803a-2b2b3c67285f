<?php

/**
 * 预警规则
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ApmWarningRule extends Model
{
    use ModelTrait;

    public $connection = "apm";

    protected $table = 'warning_rule';

    protected $primaryKey = 'rule_id';

    protected $guarded = [];

    /**
     * 关联预警表
     */
    public function warning(): BelongsTo
    {
        return $this->belongsTo(ApmWarning::class, 'warning_id', 'warning_id');
    }

    /**
     * 批量插入规则
     *
     * @param $warningId
     * @param $rules
     * @return void
     */
    public function batchStore($warningId, $rules)
    {
        // 如果规则为空, 则不做任何操作
        if (empty($rules)) return;
        // 删除全部规则
        static::query()->where('warning_id', $warningId)->delete();
        // 批量插入规则
        $createdAt = $updatedAt = date('Y-m-d H:i:s');
        // 添加创建时间和更新时间, 以及预警id
        foreach ($rules as &$rule) {
            $rule['warning_id'] = $warningId;
            $rule['created_at'] = $createdAt;
            $rule['updated_at'] = $updatedAt;
        }
        static::query()->insert($rules);
    }
}

