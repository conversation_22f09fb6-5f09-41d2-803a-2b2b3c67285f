<?php

/**
 * 监控数据分布情况脚本
 * @desc 监控数据分布情况脚本
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Console\Commands;

use App\Components\Helper\Curl;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apps;
use App\Model\BaseModel;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MonitorDataCommand extends Command
{
    /**
     * 机器人webhook地址
     *
     * @var string
     */
    const HOOK_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cffbb57a-3a02-407b-b320-fdaecafd20a2';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控数据分布情况脚本';

    /**
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            //打印日志
            Log::info("执行监控数据分布情况开始");
            // 获取昨天时间
            $startDate = Carbon::yesterday()->startOfDay()->toDateTimeString();
            $endDate = Carbon::yesterday()->endOfDay()->toDateTimeString();
            $yesterday = Carbon::yesterday()->toDateString();
            // 组装msg信息
            $content = "PerfMate昨日数据推送【{$yesterday}】：\n";
            // 获取数据
            $list = MysqlApmReportList::query()
                ->selectRaw('mysql_apm_report_list.developer_app_id, mysql_apm_device_list.os_type, count(*) as num, count(distinct mysql_apm_report_list.dev_str) as dev_num')
                ->join('mysql_apm_device_list', function ($join) {
                    $join->on('mysql_apm_device_list.developer_app_id', '=', 'mysql_apm_report_list.developer_app_id')
                        ->on('mysql_apm_device_list.dev_str', '=', 'mysql_apm_report_list.dev_str');
                })
                ->where('mysql_apm_report_list.created_at', '>=', $startDate)
                ->where('mysql_apm_report_list.created_at', '<=', $endDate)
                ->groupBy('mysql_apm_report_list.developer_app_id', 'mysql_apm_device_list.os_type')
                ->getFromSR();
            // 判断数据是否为空
            if (empty($list)) {
                return;
            }
            $list = $this->handleData($list, 'developer_app_id');
            // 获取APP信息
            $apps = Apps::query()->pluck('app_name', 'id');
            // 循环处理数据
            foreach ($list as $item) {
                $appName = $apps[$item['developer_app_id']] ?? '未知';
                // 文字内容
                $text = '';
                // 循环处理数据
                foreach ($item['list'] as $val) {
                    // 系统
                    $osType = BaseModel::OS_TYPE_TEXT[$val['os_type']] ?? '未知';
                    // 文字内容
                    $text .= "【{$osType}】：{$val['num']} 条，{$val['dev_num']} 设备 | ";
                }
                // 把最后的, 去掉
                $text = rtrim($text, " | ");
                // 拼接字符串
                $content .= "{$appName}：{$item['total']} 条，{$item['dev']} 设备 | {$text}\n";
            }
            // 把最后的换行符去掉
            $content = rtrim($content, "\n");
            // 发送请求
            $response = Curl::json(static::HOOK_URL, [
                'msgtype' => 'text',
                'text' => [
                    'content' => $content,
                ],
            ]);
            //打印日志
            Log::info("执行监控数据分布情况完成：{$response}");
        } catch (\Exception $e) {
            Log::error("执行监控数据分布情况脚本报错，错误信息：{$e->getMessage()}，堆栈信息：{$e->getTraceAsString()}");
        }
    }

    /**
     * 处理数据，并返回结果。
     * @param array $data 数据列表。
     * @param string $field 数据列表中的字段名。
     */
    private function handleData($data, $field)
    {
        // 首先，按app分组整合数据
        $list = [];
        foreach ($data as $item) {
            $list[$item[$field]][$field] = $item[$field];
            $list[$item[$field]]['list'][] = $item;
            $list[$item[$field]]['total'] = $item['num'] + ($list[$item[$field]]['total'] ?? 0);
            $list[$item[$field]]['dev'] = $item['dev_num'] + ($list[$item[$field]]['dev'] ?? 0);
        }
        // 根据二维数组的total字段进行排序
        uasort($list, function ($a, $b) {
            return $b['total'] <=> $a['total'];
        });
        // 只要前10条数据，并返回结果
        return array_slice($list, 0, 10);
    }
}
