<?php

/**
 * 机型分析控制器
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\AnalysisValidation;
use App\Service\Apm\Analysis\DevModel;
use Exception;
use Illuminate\Http\JsonResponse;

class ApmDevModelAnalysisController extends Controller
{
    /**
     * 通用处理逻辑
     *
     * @param $func
     * @param $message
     * @return JsonResponse
     */
    protected function commonHandle($func, $message): JsonResponse
    {
        $params = AnalysisValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->gameVersionCode()->innerVersion()
            ->quality()->filterTop()
            ->validate();

        try {
            //获取数据
            $data = call_user_func([new DevModel($params), $func]);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error("{$message},原因:" . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取品牌概览信息
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3869
     * @return JsonResponse
     */
    public function brandSummary(): JsonResponse
    {
        return $this->commonHandle('brandSummary', '获取品牌分析概览接口报错');
    }

    /**
     * 获取机型概览信息
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3870
     * @return JsonResponse
     */
    public function modelSummary(): JsonResponse
    {
        return $this->commonHandle('modelSummary', '获取机型分析概览接口报错');
    }

    /**
     * 获取top排行榜
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3871
     * @return JsonResponse
     */
    public function top(): JsonResponse
    {
        return $this->commonHandle('top', '获取机型分析top排行榜接口报错');
    }

    /**
     * 获取数据列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3872
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        $params = AnalysisValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->sortField()->sortType()->page()->limit()
            ->gameVersionCode()->innerVersion()->quality()->filterTop()
            ->isSimulator()->deviceTier()->validate();

        try {
            //获取数据
            $data = (new DevModel($params))->list();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error("获取版本分析数据列表接口报错,原因:" . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 导出数据列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15080
     * @return JsonResponse
     */
    public function export(): JsonResponse
    {
        $params = AnalysisValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->gameVersionCode()->innerVersion()->quality()->filterTop()
            ->isSimulator()->deviceTier()->sortField()->sortType()->validate();

        try {
            //获取数据
            $data = (new DevModel($params))->export();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, [
                'url' => $data
            ]);
        } catch (Exception $e) {
            \Log::error("导出数据列表接口报错,原因:" . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION, [], $e->getMessage());
        }
    }
}
