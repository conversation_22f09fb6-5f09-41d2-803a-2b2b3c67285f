<?php

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceScoreData;

class GameVersion extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        return $this->getScore();
    }

    /**
     * 获取分数
     *
     * @return array
     */
    protected function getScore(): array
    {
        //获取版本、平均分
        $selectRaw = <<<COLUMNS
{$this->mysql_apm_report_list_table}.app_version_name as game_version,
ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score
COLUMNS;

        return $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw($selectRaw)
            ->where("{$this->mysql_apm_report_list_table}.app_version_name", '<>', '') //过滤掉版本号为空的数据
            ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name") //按版本号分组
            ->orderByRaw("CAST(SPLIT({$this->mysql_apm_report_list_table}.app_version_name, '.')[1] AS INT) DESC,CAST(SPLIT({$this->mysql_apm_report_list_table}.app_version_name, '.')[2] AS INT) DESC,CAST(SPLIT({$this->mysql_apm_report_list_table}.app_version_name, '.')[3] AS INT) DESC") //按版本号倒序排列
            ->limit(10) //取前10个
            ->getFromSR();
    }
}
