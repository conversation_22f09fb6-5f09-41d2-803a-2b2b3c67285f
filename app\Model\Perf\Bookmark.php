<?php

namespace App\Model\Perf;

use App\Model\BaseModel;
use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Bookmark extends BaseModel
{
    use ModelTrait;

    protected $table = 'perf_bookmark';

    protected $primaryKey = 'id';

    protected $fillable = [
        'user_id',
        'title',
    ];

    /**
     * 获取收藏夹的内容列表
     */
    public function items(): HasMany
    {
        return $this->hasMany(BookmarkItem::class, 'bookmark_id', 'id');
    }
}
