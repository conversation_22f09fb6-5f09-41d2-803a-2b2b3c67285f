<?php

/**
 * 线下性能检测报告对比控制器
 * @desc 线下性能检测报告对比控制器
 * <AUTHOR> chen<PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/12/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Perf;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Model\Perf\Report;
use App\Model\Perf\ReportComparison;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PerfComparisonController extends Controller
{
    /**
     * 保存对比接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15285
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'reports' => 'required|array',
            'reports.*.name' => 'required|string',
            'reports.*.id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            $uuid = Str::random(32);
            ReportComparison::query()->create([
                'uuid' => $uuid,
                'reports' => $request->input('reports'),
                'creator' => Auth::user()->username,
            ]);
            //返回
            return $this->response(StatusCode::C_SUCCESS, ['uuid' => $uuid]);
        } catch (\Exception $e) {
            \Log::error('通用性能检测保存对比接口报错:,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取对比分析详情
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15286
     * @param $uuid
     * @return JsonResponse
     */
    public function get($uuid): JsonResponse
    {
        try {
            $comparison = ReportComparison::query()->where('uuid', $uuid)->firstOrFail();
            //获取所有报告信息
            $reports = Report::query()->whereIn('report_id', array_column($comparison['reports'], 'id'))->get();
            //返回
            return $this->response(StatusCode::C_SUCCESS, $reports);
        } catch (\Exception $e) {
            \Log::error('通用性能检测获取对比分析详情接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 对比列表接口
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15287
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        try {
            //根据developer_app_id进行筛选并将数据根据创建时间进行排序
            $query = ReportComparison::query()
                ->orderBy('created_at', 'desc')
                // ->where('creator', Auth::user()->username)
                ->paginate($request->input('per_page', 15));
            return $this->response(StatusCode::C_SUCCESS, ['list' => $query->items(), 'total' => $query->total()]);
        } catch (\Exception $e) {
            \Log::error('通用性能检测对比列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
