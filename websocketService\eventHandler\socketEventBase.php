<?php
/**
 * 功能说明:
 * 提供一些基础的工具,mysql.redis等
 **/

namespace websocketService\eventHandler;


use websocketService\dbService\MysqlService;
use Redis;
use websocketService\dbService\RedisService;

abstract class socketEventBase
{
    public static $type;

    /**
     * @var mixed
     */
    protected $cfg;

    /**
     * @var array
     */
    protected $message;

    /**
     * @var Redis
     */
    protected $redis;

    /**
     * @var \mysqli
     */
    protected $mysql;

    /**
     * @var mixed
     */
    protected $frame;

    public function __construct($frame, $message)
    {
        $this->cfg = require __DIR__ . '/../config.php';
        //注册redis服务
        $this->redis = RedisService::getInstance($this->cfg);
        $this->mysql = MysqlService::getInstance($this->cfg);
        $this->frame = $frame;
        $this->message = $message;
    }
}
