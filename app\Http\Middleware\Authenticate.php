<?php

/**
 * 权限检验中间件
 * @desc 权限检验中间件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/13
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Middleware;

use App\Service\ShareService;
use Closure;
use Illuminate\Support\Facades\Auth;

class Authenticate
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function handle($request, Closure $next, $guard = null)
    {
        //判断请求参数中是否有share_token，并且share_token是否有效
        if (!Auth::guard($guard)->check() && !ShareService::create($request)->check()) {
            return response()->json(['message' => '账号未登录', 'code' => 1002, 'data' => []], 401);
        }

        return $next($request);
    }
}
