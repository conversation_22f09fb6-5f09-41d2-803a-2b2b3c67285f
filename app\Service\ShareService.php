<?php

/**
 * 分享服务类
 * @desc 分享服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/13
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ShareService
{
    /**
     * 分享token的key
     *
     * @var string
     */
    public const SHARE_TOKEN_KEY = 'share_token';

    /**
     * 分享referer的key
     *
     * @var string
     */
    public const SHARE_REFERER_KEY = 'share_referer';

    /**
     * 过期时间，默认一年
     *
     * @var int
     */
    private $expireTime = 365 * 24 * 60 * 60;

    /**
     * 请求对象
     *
     * @var Request
     */
    private $request;

    /**
     * 私有构造函数
     */
    private function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * 创建
     *
     * @param Request $request
     * @return ShareService
     */
    public static function create(Request $request): ShareService
    {
        return new static($request);
    }

    /**
     * 设置过期时间
     *
     * @param $expireTime
     * @return $this
     */
    public function expireTime($expireTime): ShareService
    {
        $this->expireTime = $expireTime;
        return $this;
    }

    /**
     * 建造
     *
     * @return string
     * @throws Exception
     */
    public function build(): string
    {
        //1、随机生成字符串
        $token = Str::random(32);
        //2、获取浏览器的Referer
        $referer = $this->request->input(self::SHARE_REFERER_KEY);
        //3、判断是否有Referer, 抛出异常
        if (empty($referer)) {
            throw new \RuntimeException('没有获取到Referer');
        }
        //4、添加缓存
        Cache::add($this->getRedisKey($token), $referer, $this->expireTime);
        //5、返回token
        return $token;
    }

    /**
     * 获取redis的key
     *
     * @param $token
     * @return string
     */
    private function getRedisKey($token): string
    {
        return sprintf("share_token:%s", $token);
    }

    /**
     * 检查
     *
     * @return bool
     */
    public function check(): bool
    {
        $params = $this->request->all();
        //1、判断token是否存在
        $referer = Cache::get($this->getRedisKey($params[self::SHARE_TOKEN_KEY] ?? ''));
        //2、判断token是否为空
        if (empty($referer)) {
            return false;
        }
        //3、获取浏览器的Referer
        $newReferer = $params[self::SHARE_REFERER_KEY] ?? '';
        //4、判断Referer
        return $referer === $newReferer;
    }
}
