<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApmDeviceListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('apm_device_list', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->unsignedTinyInteger('os_type')->default(1)->comment('平台，1:android、2:ios、3:pc');
            $table->string('dev_brand')->default('')->comment('设备品牌');
            $table->string('dev_model')->default('')->comment('设备型号');
            $table->string('dev_str')->default('')->comment('设备标识');
            $table->string('dev_cpu_model')->default('')->comment('设备CPU型号');
            $table->string('dev_gpu_model')->default('')->comment('设备GPU型号');
            $table->unsignedMediumInteger('dev_cpu_count')->default(0)->comment('设备可用CPU核心数');
            $table->unsignedTinyInteger('is_simulator')->default(0)->comment('是否模拟器， 1:模拟器、0:真机');
            $table->string('screen_resolution')->default('')->comment('屏幕分辨率');
            $table->timestamps();

            $table->index('dev_str');
            $table->index('developer_app_id');
            $table->index('created_at');
        });
        \DB::connection('apm')->statement("ALTER TABLE `apm_device_list` comment 'apm-性能设备数据'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('apm_device_list');
    }
}
