<?php
/**
 * ReportService.php
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/9/14
 */

namespace App\Service;

use App\Model\Report;

class ReportService
{
    private $report;

    public function __construct(Report $report)
    {
        $this->report = $report;
    }

    /**
     * 保存报表文件并返回地址
     * @param $report
     * @param string $reportFormat
     * @return string
     */
    public function storeReport($report, string $reportFormat): string
    {
        // 将接收到的json存储到storage/app/public/upload/report目录下
        $reportPath = "upload/report/" . date("Y-m");
        // json文件命名应该唯一
        // 命名格式为 date("d")."_" . md5($input['report']->getClientOriginalName().now())
        $reportName = date("d") . "_" . md5($report->getClientOriginalName() . now());
        $reportUrl = $reportPath . "/" . $reportName . "." . $reportFormat;
        $report->storeAs(
            $reportPath, $reportName . "." . $reportFormat, 'public'
        );
        //访问的地址为
        //域名/storage/upload/report/date("Y-m")/date("d")."_" .md5($report->getClientOriginalName() . now()).json
        return "storage/" . $reportUrl;
    }

    /**
     * 筛选后得出的列表(分页)和总条数
     * @param int $developer_app_id
     * @param int $perPage
     * @param int $page
     * @param $start_created_at
     * @param $end_created_at
     * @return array
     */
    public function getReportList(int $developer_app_id, int $perPage, int $page, $start_created_at, $end_created_at): array
    {
        //根据创建时间进行筛选
        isset($start_created_at, $end_created_at) &&
        $this->report = $this->report->whereBetween('created_at', [$start_created_at, $end_created_at]);
        //根据developer_app_id进行筛选并将数据根据创建时间进行排序
        $query = $this->report->where('developer_app_id', $developer_app_id)->orderBy('created_at', 'desc');
        //处理分页
        $list = $this->paging((clone $query), $page, $perPage);
        //处理统计数据的趋势
        $list = $this->setStatisticsTrend($list, $perPage);
        //统计总条数
        $total = (clone $query)->count();
        return [$list, $total];
    }

    /**
     * 处理分页
     * @param $query
     * @param int $page
     * @param int $perPage
     * @return array
     */
    private function paging($query, int $page, int $perPage): array
    {
        //offset 是数据偏移量 (页数-1) × 条数
        //统计数据的趋势需要比较上一条记录,为了方便处理limit多取一条记录
        return $query->offset((($page - 1) * $perPage))->limit($perPage + 1)
            ->get(['report_id', 'all_ab_num', 'dependence_ab_num', 'redundancy_ab_num',
                'all_resource_num', 'redundancy_resource_num', 'redundancy_resource_size', 'remark', 'created_at', 'platform'])
            ->toArray();
    }

    /**
     * 处理统计数据的趋势
     * @param array $list
     * @param int $perPage
     * @return array
     */
    private function setStatisticsTrend(array $list, int $perPage): array
    {
        $count = count($list);
        // 如果list没有数据则不需要处理统计数据的趋势
        if ($count === 0) {
            return $list;
        }
        // $count <= $perPage 说明最早创建的记录在本页需要特殊处理
        if ($count <= $perPage) {
            $list[$count - 1]['all_ab_num_trend'] = 0;
            $list[$count - 1]['dependence_ab_num_trend'] = 0;
            $list[$count - 1]['redundancy_ab_num_trend'] = 0;
            $list[$count - 1]['all_resource_num_trend'] = 0;
            $list[$count - 1]['redundancy_resource_num_trend'] = 0;
            $list[$count - 1]['redundancy_resource_size_trend'] = 0;
        }
        //当isset为true时 说明是第一条数据不需要进行比较
        //当isset为false时 说明不是第一条数据需要与上一条数据进行比较(1为上升、0为与上次一致、-1为下降)
        for ($i = 0; $i < $count - 1; $i++) {
            isset($list[$i]['all_ab_num_trend']) ||
            $list[$i]['all_ab_num_trend'] = $this->cmp($list[$i]['all_ab_num'], $list[$i + 1]['all_ab_num']);
            isset($list[$i]['dependence_ab_num_trend']) ||
            $list[$i]['dependence_ab_num_trend'] = $this->cmp($list[$i]['dependence_ab_num'],
                $list[$i + 1]['dependence_ab_num']);
            isset($list[$i]['redundancy_ab_num_trend']) ||
            $list[$i]['redundancy_ab_num_trend'] = $this->cmp($list[$i]['redundancy_ab_num'],
                $list[$i + 1]['redundancy_ab_num']);
            isset($list[$i]['all_resource_num_trend']) ||
            $list[$i]['all_resource_num_trend'] = $this->cmp($list[$i]['all_resource_num'],
                $list[$i + 1]['all_resource_num']);
            isset($list[$i]['redundancy_resource_num_trend']) ||
            $list[$i]['redundancy_resource_num_trend'] = $this->cmp($list[$i]['redundancy_resource_num'],
                $list[$i + 1]['redundancy_resource_num']);
            isset($list[$i]['redundancy_resource_size_trend']) ||
            $list[$i]['redundancy_resource_size_trend'] = $this->cmp($list[$i]['redundancy_resource_size'],
                $list[$i + 1]['redundancy_resource_size']);
        }
        // $count = $perPage+1 说明最早创建的记录不在本页需要删除最后一条数据
        if ($count === $perPage + 1) {
            unset($list[$perPage]);
        }
        return $list;
    }

    /**
     * 数据比较生成趋势
     * @param $new
     * @param $old
     * @return int
     */
    private function cmp($new, $old): int
    {
        if ($new > $old) {
            return 1;
        }

        if ($new < $old) {
            return -1;
        }

        return 0;
    }


}
