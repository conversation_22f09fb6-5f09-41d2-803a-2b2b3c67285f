<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlertColumnsLengthToPerfReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       Schema::connection('tool')->table('perf_report', function (Blueprint $table) {
            $table->string('creator', 255)->default('')->comment('创建者')->change();
            $table->string('device', 255)->default('')->comment('设备')->change();
            $table->string('device_model', 255)->default('')->comment('设备型号')->change();
            $table->string('cpu', 255)->default('')->comment('中央处理器')->change();
            $table->string('gpu', 255)->default('')->comment('图形处理器')->change();
            $table->string('system', 255)->default('')->comment('系统类型')->change();
            $table->string('os_version', 255)->default('')->comment('系统版本')->change();
            $table->string('app_version', 255)->default('')->comment('软件版本')->change();
            $table->string('package_name', 255)->default('')->comment('包名')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
       Schema::connection('tool')->table('perf_report', function (Blueprint $table) {
            $table->string('creator', 50)->default('')->comment('创建者')->change();
            $table->string('device', 50)->default('')->comment('设备')->change();
            $table->string('device_model', 50)->default('')->comment('设备型号')->change();
            $table->string('cpu', 50)->default('')->comment('中央处理器')->change();
            $table->string('gpu', 50)->default('')->comment('图形处理器')->change();
            $table->string('system', 20)->default('')->comment('系统类型')->change();
            $table->string('os_version', 20)->default('')->comment('系统版本')->change();
            $table->string('app_version', 20)->default('')->comment('软件版本')->change();
            $table->string('package_name', 50)->default('')->comment('包名')->change();
        });
    }
}
