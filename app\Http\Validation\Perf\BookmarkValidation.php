<?php

namespace App\Http\Validation\Perf;

use App\Http\Validation\BaseValidation;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

/**
 * @method static BookmarkValidation build()
 */
class BookmarkValidation extends BaseValidation
{
    /**
     * 提示信息
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'title.unique' => '该收藏夹名称已存在',
        ];
    }

    /**
     * 收藏夹ID校验
     *
     * @return $this
     */
    public function bookmarkId(): BookmarkValidation
    {
        $this->rules['bookmark_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 分页条数校验
     *
     * @return $this
     */
    public function limit(): BookmarkValidation
    {
        $this->rules['limit'] = 'integer|min:10|max:10000';
        return $this;
    }

    /**
     * 标题校验
     *
     * @return $this
     */
    public function title(): BookmarkValidation
    {
        $this->rules['title'] = [
            'required',
            'string',
            Rule::unique('tool.perf_bookmark')->where(function (Builder $query) {
                $request = request();
                return $query->where('user_id', Auth::user()->user_id)
                    ->when($request->has('bookmark_id'), function (Builder $query) use ($request) {
                        return $query->where('id', '<>', $request->input('bookmark_id'));
                    });
            }),
        ];
        return $this;
    }

    /**
     * 报告ID校验
     *
     * @return $this
     */
    public function reportId(): BookmarkValidation
    {
        $this->rules['report_id'] = 'required|string';
        return $this;
    }

    /**
     * 报告ID允许为空校验
     *
     * @return $this
     */
    public function reportIdNullable(): BookmarkValidation
    {
        $this->rules['report_id'] = 'nullable|integer';
        return $this;
    }

    /**
     * 页码校验
     *
     * @return $this
     */
    public function page(): BookmarkValidation
    {
        $this->rules['page'] = 'integer|min:1';
        return $this;
    }
}
