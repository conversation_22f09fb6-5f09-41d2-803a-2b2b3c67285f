<?php

/**
 * 预警监控基类
 * @desc 预警监控基类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/07/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs\Apm;

use App\Model\Apm\ApmDeviceList;
use App\Model\Apm\ApmWarning;
use App\Service\Push\OauthCenterApi;
use App\Service\Push\VoiceHelper;
use App\Service\Push\WXGroupNoticeService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

abstract class BaseMonitorJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 1800;

    /**
     * 监控数据
     *
     * @var array
     */
    public $monitorData = [];

    /**
     * 预警规则
     *
     * @var array
     */
    public $apmWarning = [];

    /**
     * 平台
     *
     * @var array
     */
    public $platform;

    /**
     * 版本
     *
     * @var string
     */
    public $version;

    /**
     * 日期
     *
     * @var string
     */
    public $date;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $apmWarning)
    {
        //预警规则
        $this->apmWarning = $apmWarning;
        //平台
        $this->platform = ApmDeviceList::PLATFORM[$this->apmWarning['os_type']];
        //版本
        $this->version = empty($this->apmWarning['app_version']) ? "全版本" : implode(',', $this->apmWarning['app_version']);
        //日期
        $this->date = date('Y-m-d H:i');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //1、判断是否需要监控
        if (!$this->checkMonitor()) {
            return;
        }

        //2、获取监控数据
        $this->getMonitorData();
        //3、判断是否需要报警
        if (!$this->checkWarning()) {
            return;
        }

        //4、报警
        $this->warning();
        //5、触发次数自增+1，并更新最近预警时间
        $this->updateWarningInfo();
    }

    /**
     * 检查是否需要监控
     *
     * @return bool
     */
    abstract protected function checkMonitor(): bool;

    /**
     * 获取监控的数据
     *
     * @return void
     */
    abstract protected function getMonitorData();

    /**
     * 检查是否需要报警
     *
     * @return bool
     */
    abstract protected function checkWarning(): bool;

    /**
     * 获取接收者的信息
     *
     * @return string
     */
    abstract protected function getPersonMessage(): string;

    /**
     * 获取接收群的信息
     *
     * @return string
     */
    abstract protected function getGroupMessage(): string;

    /**
     * 获取接收手机的信息
     *
     * @return string
     */
    abstract protected function getPhoneMessage(): string;

    /**
     * 报警
     *
     * @return void
     */
    protected function warning()
    {
        //邮件预警
        if (!empty($this->apmWarning['receiving_person'])) {
            OauthCenterApi::getInstance()->sendWXMsg($this->apmWarning['receiving_person'], $this->getPersonMessage());
        }
        //微信群预警
        if (!empty($this->apmWarning['receiving_group'])) {
            foreach ($this->apmWarning['receiving_group'] as $url) {
                $service = new WXGroupNoticeService($url);
                $service->wxGroupNotify($this->getGroupMessage(), 'markdown');
            }
        }
        //电话预警
        if (!empty($this->apmWarning['receiving_phone'])) {
            VoiceHelper::callPhone([$this->getPhoneMessage()], $this->apmWarning['receiving_phone'], 1475525);
        }
    }

    /**
     * 更新预警信息
     *
     * @return void
     */
    protected function updateWarningInfo()
    {
        ApmWarning::query()
            ->where('warning_id', $this->apmWarning['warning_id'])
            ->increment('trigger_count', 1, [
                'last_warning_time' => date('Y-m-d H:i:s'),
            ]);
    }

    /**
     * 获取推送跳转地址
     *
     * @return string
     */
    protected function getUrl($params = ''): string
    {
        // 拼接跳转的url
        $baseUrl = 'https://auth-pro-api.shiyue.com/oauth2/wechat-login?appId=1818223957164789760&gotoUri=https%3A%2F%2Fdeveloper-manager.shiyue.com%2Fauth-pro%3Fredirect_url%3D';

        if ($params) {
            $params .= '&auto_login=1';
        } else {
            $params .= '?auto_login=1';
        }

        return $baseUrl . base64_encode(config('app.front_url') . "/console/my-app/{$this->apmWarning['developer_app_id']}/apm/apm-overview" . $params);
    }
}
