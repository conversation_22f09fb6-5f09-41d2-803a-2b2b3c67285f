<?php

namespace App\Service\Push;

use Illuminate\Support\Facades\Log;
use Qcloud\Sms\TtsVoiceSender;

class VoiceHelper
{

    /**
     * 语音消息调用函数
     *
     * @param $params
     * @param $mobiles
     * @param int $templateId
     * @return void
     */
    public static function callPhone($params, $mobiles, int $templateId = 0): void
    {
        // 语音消息应用 SDK AppID
        $appid = config('qcloudsms.APPID');
        // 语音消息应用 App Key
        $appKey = config('qcloudsms.APPKEY');
        // 语音模板 ID，需要在语音消息控制台中申请
        if (empty($templateId)) {
            $templateId = config('qcloudsms.TEMPLATEID');  // NOTE: 这里的模板 ID`7839`只是示例，真实的模板 ID 需要在语音消息控制台中申请
        }

        foreach ($mobiles as $mobile) {
            try {
                $tvsender = new TtsVoiceSender($appid, $appKey);
                $result = $tvsender->send("86", "{$mobile}", $templateId, $params);
                $ret = json_decode($result, true);
                if ($ret["result"] == 0 && $ret["errmsg"] == "OK") {
                    Log::info('语音info:' . $result);
                } else {
                    Log::error('语音error：' . $ret["errmsg"]);
                }
            } catch (\Exception $e) {
                Log::error('语音代码error：' . $e->getMessage());
            }
        }
    }
}
