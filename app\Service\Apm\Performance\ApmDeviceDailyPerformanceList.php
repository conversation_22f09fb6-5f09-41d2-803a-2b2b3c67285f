<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\PerformanceData;
use Carbon\Carbon;

class ApmDeviceDailyPerformanceList extends ApmBase
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        return $this->handleData($this->getOriginData());
    }

    /**
     * 获取查询的字段
     *
     * @return string
     */
    protected function getSelectRaw(): string
    {
        $selectRaw = [
            'fps' => "sum(({$this->performance_data_table}.frame_times_length / {$this->performance_data_table}.frame_times_sum) * 1000) / count({$this->performance_data_table}.session_id)",
            'stutter' => "sum({$this->performance_data_table}.jank_time / {$this->performance_data_table}.frame_times_sum) / count({$this->performance_data_table}.session_id)",
            'jank' => "sum({$this->performance_data_table}.jank_count) / count({$this->performance_data_table}.jank_count)",
            'bigJank' => "sum({$this->performance_data_table}.big_jank_count) / count({$this->performance_data_table}.big_jank_count)",
            'memory' => "sum({$this->performance_data_table}.used_memory) / count({$this->performance_data_table}.used_memory)",
            'temperature' => "sum({$this->performance_data_table}.battery_temp) / count({$this->performance_data_table}.battery_temp)",
            'electric' => "sum({$this->performance_data_table}.battery_level) / count({$this->performance_data_table}.battery_level)",
        ];
        return $selectRaw[$this->params['data_ordinate']] ?? '';
    }

    /**
     * 获取原数据
     *
     * @return array
     */
    protected function getOriginData(): array
    {
        return PerformanceData::query()
            ->selectRaw("{$this->getSelectRaw()} as value, STR_TO_DATE(DATE_FORMAT({$this->performance_data_table}.date, '%Y-%m-%d %H:%i:00'), '%Y-%m-%d %H:%i:%s') as timestamp")
            ->join($this->performance_stat_data_table, "$this->performance_data_table.session_id", '=', "$this->performance_stat_data_table.session_id")
            ->whereRaw("$this->performance_data_table.session_id in (
                select id from mysql_apm_report_list
                where developer_app_id = {$this->params['developer_app_id']}
                AND dev_str = '{$this->params['dev_str']}'
                AND DATE(created_at) = '{$this->params['date']}'
            )")
            ->where("$this->performance_data_table.date", '>=', Carbon::parse($this->params['date'] . ' 00:00:00')->subDays()->toDateTimeString())
            ->where("$this->performance_data_table.date", '<=', Carbon::parse($this->params['date'] . ' 23:59:59')->addDays()->toDateTimeString())
            ->where("$this->performance_stat_data_table.duration", '>', $this->getMinDuration())
            ->groupBy('timestamp')
            ->orderBy('timestamp')
            ->getFromSR();
    }

    /**
     * 处理数据
     *
     * @param array $res
     * @return array
     */
    protected function handleData(array $res): array
    {
        // 处理数据，保留两位小数
        foreach ($res as $key => $item) {
            if ($this->params['data_ordinate'] == 'stutter') {
                $res[$key]['value'] = round((double)$item['value'] * 100, 2);
            } else {
                $res[$key]['value'] = round((double)$item['value'], 2);
            }
        }
        return $res;
    }
}
