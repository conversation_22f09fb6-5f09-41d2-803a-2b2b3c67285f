<?php

/**
 * 获取异常性能报告筛选数据
 * @desc 获取异常性能报告筛选数据
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/19
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\StarRocksDB;
use Illuminate\Support\Facades\DB;

class ApmExceptionSearch extends ApmBase
{
    /**
     * 获取异常性能报告筛选数据
     *
     * @return array
     */
    public function getSearch(): array
    {
        $builder = MysqlApmReportList::query()
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where(DB::raw("({$this->performance_stat_data_table}.exception_count / ({$this->performance_stat_data_table}.duration / 3600))"), '>=', $this->getExceptionMarkVal())
            ->where("{$this->performance_stat_data_table}.exception_count", '>', 1)
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->when($this->params['os_type'] ?? null, function ($query) {
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['device_tier'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when($this->params['game_version_code'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when(isset($this->params['is_simulator']), function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->when($this->params['tags_info'] ?? null, function ($query) {
                return $query->whereRaw("array_contains({$this->performance_stat_data_table}.tags_info, '{$this->params['tags_info']}') == 1");
            })
            ->when($this->params['dev_str'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.dev_str", $this->params['dev_str']);
            })
            ->when($this->params['user_id'] ?? null, function ($query) {
                return $query->whereRaw("array_contains({$this->performance_stat_data_table}.user_id, '{$this->params['user_id']}') == 1");
            });
        //返回数据
        return [
            'tags_info' => array_column(StarRocksDB::query(DB::table(DB::raw("(" . (clone $builder)->getSQL() . ") t, UNNEST(tags_info)"))
                    ->whereRaw("null_or_empty(unnest) = 0")
                    ->where("unnest", '!=', '')
                    ->groupBy("tag") //标签分组
                    ->selectRaw("unnest as tag") //标签
            )->get(), 'tag'),
            'game_version_code' => array_column((clone $builder)
                    ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name as game_version_code")
                    ->whereRaw("null_or_empty({$this->mysql_apm_report_list_table}.app_version_name) = 0")
                    ->where("{$this->mysql_apm_report_list_table}.app_version_name", '!=', '')
                    ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
                    ->getFromSR(), 'game_version_code'),
            'inner_version' => array_column((clone $builder)
                    ->selectRaw("{$this->mysql_apm_report_list_table}.inner_version")
                    ->whereRaw("null_or_empty({$this->mysql_apm_report_list_table}.inner_version) = 0")
                    ->where("{$this->mysql_apm_report_list_table}.inner_version", '!=', '')
                    ->groupBy("{$this->mysql_apm_report_list_table}.inner_version")
                    ->getFromSR(), 'inner_version'),
            'quality' => array_column((clone $builder)
                    ->selectRaw("{$this->mysql_apm_report_list_table}.quality")
                    ->whereRaw("null_or_empty({$this->mysql_apm_report_list_table}.quality) = 0")
                    ->where("{$this->mysql_apm_report_list_table}.quality", '!=', '')
                    ->groupBy("{$this->mysql_apm_report_list_table}.quality")
                    ->getFromSR(), 'quality'),
        ];
    }
}
