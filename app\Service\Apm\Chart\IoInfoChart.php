<?php

/**
 * io信息图表
 * @desc io信息图表
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/08/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class IoInfoChart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'io_read_bytes',
                'io_read_count',
                'io_write_bytes',
                'io_write_count',
                'tags_info',
                'app_state',
                'perf_data_ts',
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['ioInfo'] = [    //按照时间戳分组
                'io_read_bytes' => $item['io_read_bytes'],
                'io_read_count' => $item['io_read_count'],
                'io_write_bytes' => $item['io_write_bytes'],
                'io_write_count' => $item['io_write_count'],
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }
        return array_values($list);
    }
}
