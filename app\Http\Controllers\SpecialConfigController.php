<?php

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use Exception;
use App\Model\Special\ThresholdConfig;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SpecialConfigController extends Controller
{
    private $thresholdConfig;

    public function __construct(ThresholdConfig $thresholdConfig)
    {
        $this->thresholdConfig = $thresholdConfig;
    }

    /**
     * 配置编辑接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3214
     * @param Request $request
     * @param null $thresholdConfigId
     * @return JsonResponse
     */
    public function store(Request $request, $thresholdConfigId = null): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
            'name' => 'required|string|max:64',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        try {
            \DB::connection('tool')->beginTransaction();
            if ($thresholdConfigId) {
                // 如果 threshold_config_id 存在则是编辑
                $thresholdConfig = $this->thresholdConfig->where('developer_app_id', $input['developer_app_id'])
                    ->where('threshold_config_id', $thresholdConfigId)->first();
                if ($thresholdConfig === null) {
                    return $this->response(StatusCode::C_THRESHOLD_CONFIG_UPDATE_ERROR);
                }
            } else {
                // 阈值配置名称判断唯一
                if ($this->thresholdConfig->where('developer_app_id', $input['developer_app_id'])
                    ->where('name', $input['name'])->exists()) {
                    return $this->response(StatusCode::C_THRESHOLD_CONFIG_NAME_REPETITION);
                }
                // 如果 threshold_config_id 不存在则是新增
                $thresholdConfig = $this->thresholdConfig;
            }
            $thresholdConfig->store($input, true);
            \DB::connection('tool')->commit();
            return $this->response(StatusCode::C_SUCCESS, $thresholdConfig->threshold_config_id);
        } catch (Exception $e) {
            \Log::error('保存特效配置接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            \DB::connection('tool')->rollBack();
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 阈值配置列表接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3215
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        try {
            $this->thresholdConfig->createDefaultConfig($input['developer_app_id']);
            $list = $this->thresholdConfig->where('developer_app_id', $input['developer_app_id'])
                ->orderBy('created_at', 'desc')->get(['threshold_config_id', 'name', 'is_current_config']);
            return $this->response(StatusCode::C_SUCCESS, $list);
        } catch (Exception $e) {
            \Log::error('特效阈值配置列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }

    }

    /**
     * 获取阈值配置详情
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3216
     * @param $thresholdConfigId
     * @return JsonResponse
     */
    public function getContent($thresholdConfigId): JsonResponse
    {
        try {
            $content = $this->thresholdConfig->findOrFail($thresholdConfigId, ['name', 'config']);
            return $this->response(StatusCode::C_SUCCESS, $content);
        } catch (Exception $e) {
            \Log::error('获取特效阈值配置详情接口报错-thresholdConfigId:' . $thresholdConfigId . ',原因:' . $e->getMessage() .
                ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除阈值配置接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3217
     * @param $thresholdConfigId
     * @return JsonResponse
     */
    public function delete($thresholdConfigId): JsonResponse
    {
        try {
            $this->thresholdConfig->where('threshold_config_id', $thresholdConfigId)->delete();
            return $this->response();
        } catch (\Exception $e) {
            \Log::error('删除特效阈值配置接口报错,id:' . $thresholdConfigId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取当前配置接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3218
     * @param Request $request
     * @return JsonResponse
     */
    public function getCurrentConfig(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        try {
            $this->thresholdConfig->createDefaultConfig($input['developer_app_id']);
            $currentConfig = $this->thresholdConfig->where('is_current_config', ThresholdConfig::CURRENT)
                ->where('developer_app_id', $input['developer_app_id'])
                ->first(['threshold_config_id', 'name', 'config']);
            return $this->response(StatusCode::C_SUCCESS, $currentConfig);
        } catch (Exception $e) {
            \Log::error('特效阈值配置列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 应用当前配置接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3219
     * @param Request $request
     * @param $threshold_config_id
     * @return JsonResponse
     */
    public function updateCurrentConfig(Request $request, $threshold_config_id = null): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        try {
            \DB::connection('tool')->beginTransaction();
            //更新指定developer_app_id 的 is_current_config 字段
            if ($threshold_config_id) {
                //将threshold_config_id 的 is_current_config 字段设置为1 其余设置为0
                $this->thresholdConfig->where('developer_app_id', $input['developer_app_id'])
                    ->where('threshold_config_id', '!=', $threshold_config_id)
                    ->update(['is_current_config' => ThresholdConfig::NOT_CURRENT]);
                $this->thresholdConfig->where('developer_app_id', $input['developer_app_id'])
                    ->where('threshold_config_id', $threshold_config_id)
                    ->update(['is_current_config' => ThresholdConfig::CURRENT]);
            } else {
                //如果$threshold_config_id=null 则将所有 is_current_config 字段设置为0
                $this->thresholdConfig->where('developer_app_id', $input['developer_app_id'])
                    ->update(['is_current_config' => ThresholdConfig::NOT_CURRENT]);
            }
            \DB::connection('tool')->commit();
            return $this->response();
        } catch (Exception $e) {
            \Log::error('特效保存配置接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            \DB::connection('tool')->rollBack();
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
