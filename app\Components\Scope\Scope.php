<?php
/**
 * Scope.php
 *
 * User: Dican
 * Date: 2022/9/22
 * Email: <<EMAIL>>
 */

namespace App\Components\Scope;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Scope
{
    // 格式: Y-m-d
    private $startTime;
    private $endTime;
    // 分页: 每页多少行 第几页
    private $perPage;
    private $page;
    // 效能appId
    private $developerAppId;
    // 默认开始、结束日期
    private $defaultStartDate;
    private $defaultEndDate;
    // 默认分页、页数
    private $defaultPerPage;
    private $defaultPage;

    public function __construct($params = [])
    {
        //参数key都是下划线, 将参数key改成小驼峰
        foreach ($params as $k => $v) {
            $k = Str::camel($k);
            if (property_exists($this, $k)) {
                $this->{$k} = $v;
            }
        }

        $this->defaultStartDate = $params['start_created_at'] ?? null;
        $this->defaultEndDate = $params['end_created_at'] ?? null;
        $this->defaultPerPage = $params['per_page'] ?? null;
        $this->defaultPage = $params['page'] ?? null;

        $this->perPage = request()->input('per_page', 15);
        $this->page = request()->input('page', 1);
        $this->startTime = request()->input('start_created_at', now()->subDay()->toDateString()) . " 00:00:00";
        $this->endTime = request()->input('end_created_at', date('Y-m-d')) . " 23:59:59";
    }

    /**
     * @return int
     */
    public function getPerPage(): int
    {
        return $this->perPage;
    }

    /**
     * @return int
     */
    public function getPage(): int
    {
        return $this->page;
    }

    /**
     * @return int|null
     */
    public function getDefaultPerPage(): ?int
    {
        return $this->defaultPerPage;
    }

    /**
     * @return int|null
     */
    public function getDefaultPage(): ?int
    {
        return $this->defaultPage;
    }

    public function getBuilder(Builder $builder)
    {
        return $builder->when($this->developerAppId, function ($query) {
            $query->where('developer_app_id', $this->developerAppId);
        })->when(!empty($this->defaultStartDate) && !empty($this->defaultEndDate), function ($query) {
            $query->whereBetween('created_at', [$this->startTime, $this->endTime]);
        })->orderBy('created_at', 'desc');
    }
}
