<?php
/**
 * CheckoutHandler.php
 *
 * User: Dican
 * Date: 2022/8/17
 * Email: <<EMAIL>>
 */

namespace websocketService\eventHandler;


class CheckoutHandler extends socketEventBase implements eventInterface
{
    public static $type = 1;

    public function returnMessage(): array
    {
        $fd = $this->frame->fd;
        $checkoutIds = join(',', $this->message['checkout_ids']);
        //新增、更新
        $this->redis->hSet(self::getRedisKey(), $fd, $checkoutIds);
        return [
            ['fd' => $fd, 'message' => ['type' => self::$type, 'data' => self::getCheckout($checkoutIds, $this->mysql)]]
        ];
    }

    /**
     * 获取检测数据
     * @param string $checkoutIds
     * @param $mysql
     * @return array
     */
    public static function getCheckout(string $checkoutIds, $mysql): array
    {
        $checkouts = [];
        $sql = sprintf("
            SELECT checkout_id, status
            FROM checkout
            WHERE checkout_id in (%s)",
            $checkoutIds
        );
        $query = $mysql->query($sql);
        while ($result = $query->fetch_object()) {
            $checkouts[] = $result;
        }
        return $checkouts;
    }

    /**
     * 获取redis key
     * @return string
     */
    public static function getRedisKey(): string
    {
        return 'tool_websocket_type:1';
    }
}
