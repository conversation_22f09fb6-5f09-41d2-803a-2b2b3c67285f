<?php

namespace App\Service\Apm\Stat;

use App\Model\Apm\ApmDeviceList;
use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Model\Apm\StarRocks\StarRocksDB;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OptimizationAdvice extends BaseStat
{
    /**
     * 优化建议
     *
     * @return string[]
     */
    public function getTips(): array
    {
        $data = [];
        $avgScore = $this->getAvgScore();
        if (!empty($avgScore)) {
            $data[] = [
                'text' => $avgScore,
                'type' => '',
            ];
        }
        $gameVersion = $this->getLastGameVersion();
        if (!empty($gameVersion)) {
            $data[] = [
                'text' => $gameVersion,
                'type' => 'game_version',
            ];
        }
        $innerVersion = $this->getLastInnerVersion();
        if (!empty($innerVersion)) {
            $data[] = [
                'text' => $innerVersion,
                'type' => 'inner_version',
            ];
        }
        $brand = $this->getLastBrand();
        if (!empty($brand)) {
            $data[] = [
                'text' => $brand,
                'type' => 'model',
            ];
        }
        $model = $this->getLastModel();
        if (!empty($model)) {
            $data[] = [
                'text' => $model,
                'type' => 'model',
            ];
        }
        $tags = $this->getLastTag();
        if (!empty($tags)) {
            $data[] = [
                'text' => $tags,
                'type' => 'tag',
            ];
        }
        return $data;
    }

    /**
     * 获取最近性能平均分
     *
     * @return string
     */
    public function getAvgScore(): string
    {
        $day = Carbon::parse($this->endTime)->addSeconds()->diffInDays(Carbon::parse($this->startTime));
        $timeList = [
            [
                'start_time' => $this->startTime,
                'end_time' => $this->endTime,
            ],
            [
                'start_time' => Carbon::parse($this->startTime)->subDays($day)->toDateTimeString(),
                'end_time' => Carbon::parse($this->endTime)->subDays($day)->toDateTimeString(),
            ],
        ];
        $result = [];
        foreach ($timeList as $time) {
            $list = PerformanceScoreData::query() //查询性能分数表
                ->selectRaw("round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score, round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps_score, round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness_score, round((sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as avg_memory_score, round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_stat_data_table}.session_id), 2) as big_jank_score, round(sum(case when {$this->mysql_apm_device_list_table}.device_tier = 1 then {$this->performance_score_data_table}.all_score else 0 end) / sum(case when {$this->mysql_apm_device_list_table}.device_tier = 1 then 1 else 0 end), 2) as high_score,
round(sum(case when {$this->mysql_apm_device_list_table}.device_tier = 2 then {$this->performance_score_data_table}.`all_score` else 0 end) / sum(case when {$this->mysql_apm_device_list_table}.device_tier = 2 then 1 else 0 end), 2) as middle_score,
round(sum(case when {$this->mysql_apm_device_list_table}.device_tier = 3 then {$this->performance_score_data_table}.all_score else 0 end) / sum(case when {$this->mysql_apm_device_list_table}.device_tier = 3 then 1 else 0 end), 2) as low_score") //计算平均分
                ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
                ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
                ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                    $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                        ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
                })
                ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
                ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$time['start_time'], $time['end_time']]) //过滤掉不在时间范围内的数据
                ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
                ->when(!empty($this->params['os_type']), function ($query) { //如果有传平台值，过滤掉不在平台范围内的数据
                    $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
                })
                ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                    return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
                })
                ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                    return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
                })
                ->when(!empty($this->params['game_version_code']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                    if (is_array($this->params['game_version_code'])) {
                        return $query->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
                    } else {
                        return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
                    }
                })
                ->when($this->params['inner_version'] ?? null, function ($query) {
                    return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
                })
                ->when($this->params['quality'] ?? null, function ($query) {
                    return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
                })
                ->firstFromSR();
            $result[] = $list;
        }
        // 处理结果
        return $this->handleAvgText($result);
    }

    /**
     * 处理总体趋势文案
     *
     * @param $result
     * @return string
     */
    private function handleAvgText($result)
    {
        //判断是否有数据
        if (empty($result[0]['score'])) {
            return '';
        }
        // 设置整体分数
        $this->standardScore = $result[0]['score'];
        // 提示文案
        $text = "所选时段总体性能平均分{$result[0]['score']}分（低档机型{$result[0]['low_score']}分，中档机型{$result[0]['middle_score']}分，高档机型{$result[0]['high_score']}分）";
        // 判断是否有环比
        if (empty($result[1]) || empty($result[1]['score'])) {
            return $text;
        }
        // 判断趋势
        $trend = $result[0]['score'] > $result[1]['score'] ? '上升' : '下降';
        // 继续拼接字符串
        // 对比机型是上升还是下降
        $modelTrend = [];
        if ($result[0]['low_score'] > $result[1]['low_score']) {
            $modelTrend['low'] = [
                'trend' => '上升',
                'value' => round($result[0]['low_score'] - $result[1]['low_score'], 2),
            ];
        } else {
            $modelTrend['low'] = [
                'trend' => '下降',
                'value' => round($result[1]['low_score'] - $result[0]['low_score'], 2),
            ];
        }
        if ($result[0]['middle_score'] > $result[1]['middle_score']) {
            $modelTrend['middle'] = [
                'trend' => '上升',
                'value' => round($result[0]['middle_score'] - $result[1]['middle_score'], 2),
            ];
        } else {
            $modelTrend['middle'] = [
                'trend' => '下降',
                'value' => round($result[1]['middle_score'] - $result[0]['middle_score'], 2),
            ];
        }
        if ($result[0]['high_score'] > $result[1]['high_score']) {
            $modelTrend['high'] = [
                'trend' => '上升',
                'value' => round($result[0]['high_score'] - $result[1]['high_score'], 2),
            ];
        } else {
            $modelTrend['high'] = [
                'trend' => '下降',
                'value' => round($result[1]['high_score'] - $result[0]['high_score'], 2),
            ];
        }
        // 计算变化率
        $round = round(bcdiv(abs($result[0]['score'] - $result[1]['score']), $result[1]['score'], 6) * 100, 2);
        // 文案
        $text .= "相比上一周期（{$result[1]['score']}分）呈现【{$trend}】趋势（{$round}%）。其中低档机型{$modelTrend['low']['trend']}{$modelTrend['low']['value']}分，中档机型{$modelTrend['middle']['trend']}{$modelTrend['middle']['value']}分，高档机型{$modelTrend['high']['trend']}{$modelTrend['high']['value']}分。";
        // 拼接文案
        $text .= $this->getMetricText($result, $trend);
        return $text;
    }

    /**
     * 获取指标文案
     *
     * @param $result
     * @param $trend
     * @return string
     */
    private function getMetricText($result, $trend)
    {
        // 判断哪些指标影响评分
        $maxField = '';
        $maxValue = 0;
        $trendText = '下降';
        $fields = ['smoothness_score', 'avg_memory_score', 'big_jank_score'];
        // 判断趋势
        if ($trend == '上升') {
            foreach ($fields as $field) {
                if (($result[0][$field] - $result[1][$field]) < 0) {
                    if (round($result[1][$field] - $result[0][$field], 2) > $maxValue) {
                        $maxField = $field;
                        $maxValue = round($result[1][$field] - $result[0][$field], 2);
                    }
                }
            }
            $field = 'fps_score';
            if (($result[0][$field] - $result[1][$field]) > 0) {
                if (round($result[0][$field] - $result[1][$field], 2) > $maxValue) {
                    $maxField = $field;
                    $maxValue = round($result[0][$field] - $result[1][$field], 2);
                    $trendText = '上升';
                }
            }
        } else {
            foreach ($fields as $field) {
                if (($result[1][$field] - $result[0][$field]) < 0) {
                    if (round($result[0][$field] - $result[1][$field], 2) > $maxValue) {
                        $maxField = $field;
                        $maxValue = round($result[0][$field] - $result[1][$field], 2);
                        $trendText = '上升';
                    }
                }
            }
            $field = 'fps_score';
            if (($result[1][$field] - $result[0][$field]) > 0) {
                if (round($result[1][$field] - $result[0][$field], 2) > $maxValue) {
                    $maxField = $field;
                    $maxValue = round($result[1][$field] - $result[0][$field], 2);
                }
            }
        }
        // 判断是否为空
        if (empty($maxField)) {
            return '';
        }
        // 获取字段名称
        $filed = ['fps_score' => 'FPS', 'smoothness_score' => '卡顿率', 'avg_memory_score' => '内存均值', 'big_jank_score' => 'BigJank'][$maxField];
        // 拼接文案
        return "主要影响因素是{$filed}指标从{$result[1][$maxField]}{$trendText}到了{$result[0][$maxField]}。";
    }

    /**
     * 获取最新版本标准分的版本号
     *
     * @return string
     */
    public function getLastGameVersion(): string
    {
        // 获取最近两个版本
        $versions = $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name")
            ->orderByDesc("{$this->mysql_apm_report_list_table}.app_version_name")
            ->where("{$this->mysql_apm_report_list_table}.app_version_name", '!=', '')
            ->limit(2) // 只获取前两个版本
            ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
            ->getFromSR();
        // 判断是否为空
        if (empty($versions)) {
            return '';
        }
        // 只取 app_version_name 的数据
        $versions = array_column($versions, 'app_version_name');
        // 获取数据
        $gameVersion = $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name, ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score")
            ->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", $versions)
            ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
            ->orderByDesc("{$this->mysql_apm_report_list_table}.app_version_name")
            ->getFromSR();
        // 判断是否为空
        if (empty($gameVersion)) {
            return '';
        }
        $text = "最新游戏应用版本{$gameVersion[0]['app_version_name']}的性能平均分为{$gameVersion[0]['score']}分";
        // 判断是否有第二个版本
        if (count($gameVersion) > 1) {
            // 判断第一个版本的分数是否小于第二个版本分数
            if ($gameVersion[0]['score'] < $gameVersion[1]['score']) {
                $text .= "，相比上一个版本同周期呈【下降】趋势";
            } else {
                $text .= "，相比上一个版本同周期呈【上升】趋势";
            }
            $round = round(bcdiv(abs($gameVersion[0]['score'] - $this->standardScore), $this->standardScore, 6) * 100, 2);
            // 判断分数是否低于整体分数
            if ($gameVersion[0]['score'] > $this->standardScore) {
                $text .= "，高于整体平均分（{$round}%）";
            } else {
                $text .= "，低于整体平均分（{$round}%）";
            }
            if ($gameVersion[0]['score'] < $gameVersion[1]['score'] && $gameVersion[0]['score'] < $this->standardScore) {
                $text .= "，建议加强性能优化工作";
            } else if ($gameVersion[0]['score'] > $gameVersion[1]['score'] && $gameVersion[0]['score'] < $this->standardScore) {
                $text .= "，仍需继续优化以提升性能";
            } else if ($gameVersion[0]['score'] > $gameVersion[1]['score'] && $gameVersion[0]['score'] > $this->standardScore) {
                $text .= "，希望继续保持并发掘更多优化空间";
            } else if ($gameVersion[0]['score'] < $gameVersion[1]['score'] && $gameVersion[0]['score'] > $this->standardScore) {
                $text .= "，建议关注两个版本的更新差异定位性能问题";
            }
        }
        // 返回数据
        return $text . '。';
    }

    /**
     * 获取最新资源版本标准分的版本号
     *
     * @return string
     */
    public function getLastInnerVersion(): string
    {
        // 获取最近两个版本
        $versions = $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw("{$this->mysql_apm_report_list_table}.inner_version")
            ->orderByDesc("{$this->mysql_apm_report_list_table}.inner_version")
            ->where("{$this->mysql_apm_report_list_table}.inner_version", '!=', '')
            ->limit(2) // 只获取前两个版本
            ->groupBy("{$this->mysql_apm_report_list_table}.inner_version")
            ->getFromSR();
        // 判断是否为空
        if (empty($versions)) {
            return '';
        }
        // 只取 app_version_name 的数据
        $versions = array_column($versions, 'inner_version');
        // 获取数据
        $innerVersion = $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw("{$this->mysql_apm_report_list_table}.inner_version, ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score")
            ->whereIn("{$this->mysql_apm_report_list_table}.inner_version", $versions)
            ->groupBy("{$this->mysql_apm_report_list_table}.inner_version")
            ->orderByDesc("{$this->mysql_apm_report_list_table}.inner_version")
            ->getFromSR();
        // 判断是否为空
        if (empty($innerVersion)) {
            return '';
        }
        $text = "最新游戏资源版本{$innerVersion[0]['inner_version']}的性能平均分为{$innerVersion[0]['score']}分，";
        // 判断是否有第二个版本
        if (count($innerVersion) > 1) {
            // 判断第一个版本的分数是否小于第二个版本分数
            if ($innerVersion[0]['score'] < $innerVersion[1]['score']) {
                $text .= "，相比上一个版本同周期呈【下降】趋势";
            } else {
                $text .= "，相比上一个版本同周期呈【上升】趋势";
            }
            $round = round(bcdiv(abs($innerVersion[0]['score'] - $this->standardScore), $this->standardScore, 6) * 100, 2);
            // 判断分数是否低于整体分数
            if ($innerVersion[0]['score'] > $this->standardScore) {
                $text .= "，高于整体平均分（{$round}%）";
            } else {
                $text .= "，低于整体平均分（{$round}%）";
            }
            if ($innerVersion[0]['score'] < $innerVersion[1]['score'] && $innerVersion[0]['score'] < $this->standardScore) {
                $text .= "，建议加强性能优化工作";
            } else if ($innerVersion[0]['score'] > $innerVersion[1]['score'] && $innerVersion[0]['score'] < $this->standardScore) {
                $text .= "，仍需继续优化以提升性能";
            } else if ($innerVersion[0]['score'] > $innerVersion[1]['score'] && $innerVersion[0]['score'] > $this->standardScore) {
                $text .= "，希望继续保持并发掘更多优化空间";
            } else if ($innerVersion[0]['score'] < $innerVersion[1]['score'] && $innerVersion[0]['score'] > $this->standardScore) {
                $text .= "，建议关注两个版本的更新差异定位性能问题";
            }
        }
        // 返回数据
        return $text . '。';
    }

    /**
     * 获取最近三个版本标准分的机型
     *
     * @return string
     */
    public function getLastModel(): string
    {
        // 获取数据
        $model = $this->getOriginData("{$this->mysql_apm_device_list_table}.dev_model, ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score", "{$this->mysql_apm_device_list_table}.dev_model");
        // 判断是否为空
        if (empty($model)) {
            return '';
        }
        foreach ($model as &$item) {
            $item['score'] = round(bcdiv(abs($item['score'] - $this->standardScore), $this->standardScore, 6) * 100, 2);
        }
        $text = "近期（" . implode('、', array_column($model, 'dev_model')) . "）机型的性能平均分均低于整体平均分（" . implode('%、', array_column($model, 'score')) . "%）。建议重点优化这些机型的内存管理和图形渲染路径，以改善性能表现。";
        // 返回数据
        return $text;
    }

    /**
     * 获取最近三个版本小于标准分的品牌
     *
     * @return string
     */
    public function getLastBrand(): string
    {
        $osType = $this->params['os_type'] ?? 0;
        $brand = [];
        // 判断是否是IOS，只有安卓才需要比较品牌
        if ($osType != ApmDeviceList::IOS) {
            // 获取数据
            $brand = $this->getOriginData("{$this->mysql_apm_device_list_table}.dev_brand, ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score", "{$this->mysql_apm_device_list_table}.dev_brand");
        }
        // 判断是否为空
        if (empty($brand)) {
            return '';
        }
        foreach ($brand as &$item) {
            $item['score'] = round(bcdiv(abs($item['score'] - $this->standardScore), $this->standardScore, 6) * 100, 2);
        }
        $text = "近期（" . implode('、', array_column($brand, 'dev_brand')) . "）品牌的性能平均分均低于整体平均分（" . implode('%、', array_column($brand, 'score')) . "%）。建议针对这些品牌进行专项优化，包括与品牌方沟通获取技术支持以及优化针对特定硬件配置的游戏性能。";
        // 返回数据
        return $text;
    }

    /**
     * 获取最近三个小于标准分的标签
     *
     * @return string
     */
    public function getLastTag(): string
    {
        // 获取数据
        $tags = $this->getOriginData("{$this->performance_tag_score_data_table}.tag, ROUND(sum({$this->performance_tag_score_data_table}.all_score) / count({$this->performance_tag_score_data_table}.session_id), 2) as score", "{$this->performance_tag_score_data_table}.tag", 'score asc', $this->performance_tag_score_data_table);
        // 判断是否为空
        if (empty($tags)) {
            return '';
        }
        foreach ($tags as &$item) {
            $item['score'] = round(bcdiv(abs($item['score'] - $this->standardScore), $this->standardScore, 6) * 100, 2);
        }
        $text = "近期涉及（" . implode('、', array_column($tags, 'tag')) . "）等标签的场景性能平均分均低于整体平均分（" . implode('%、', array_column($tags, 'score')) . "%）。建议针对这些场景的代码逻辑进行优化，减少不必要的计算并优化网络请求，以提高性能表现。";
        // 返回数据
        return $text;
    }

    /**
     * 获取源数据
     *
     * @param string $selectRaw
     * @param $groupBy
     * @param string $orderByRaw
     * @param string $table
     * @return array
     */
    protected function getOriginData(string $selectRaw, $groupBy, string $orderByRaw = '', string $table = PerformanceScoreData::TABLE_NAME): array
    {
        $builder = DB::table($table)
            ->join($this->performance_stat_data_table, "$table.session_id", '=', "{$this->performance_stat_data_table}.session_id") //关联统计表
            ->join($this->mysql_apm_report_list_table, "{$this->mysql_apm_report_list_table}.id", '=', "$table.session_id") //关联性能报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id") //关联效能后台Id
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str"); //关联设备字符串
            })
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //时间范围
            ->when(!empty($this->params['os_type']), function ($query) { //如果有传平台值，过滤掉不在平台范围内的数据
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['game_version']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                return $query->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->groupBy($groupBy);
        // 排序
        if ($orderByRaw) {
            $builder->orderByRaw($orderByRaw);
        }
        // 子查询条件
        $subSql = StarRocksDB::toSql($builder->selectRaw($selectRaw));
        // 主查询
        $mainQuery = DB::table(DB::raw("({$subSql}) as t"))
            ->where('score', '<', $this->standardScore) //过滤掉大于标准分的数据
            ->orderBy('score') //按照分数升序
            ->limit(3); //取前三条
        // 返回数据
        return StarRocksDB::query($mainQuery)->get();
    }
}
