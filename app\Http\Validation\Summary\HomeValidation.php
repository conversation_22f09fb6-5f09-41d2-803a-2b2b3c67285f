<?php

namespace App\Http\Validation\Summary;

use App\Http\Validation\BaseValidation;

/**
 * @method static HomeValidation build()
 */
class HomeValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): HomeValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 开始时间校验
     *
     * @return $this
     */
    public function startDate(): HomeValidation
    {
        $this->rules['start_date'] = 'required|date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 结束时间校验
     *
     * @return $this
     */
    public function endDate(): HomeValidation
    {
        $this->rules['end_date'] = 'required|date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 类型校验
     *
     * @return $this
     */
    public function type(): HomeValidation
    {
        $this->rules['type'] = 'required|in:day,week,month';
        return $this;
    }

    /**
     * 操作系统类型校验
     *
     * @return $this
     */
    public function osType(): HomeValidation
    {
        $this->rules['os_type'] = 'integer';
        return $this;
    }

    /**
     * 首页类型校验
     *
     * @return $this
     */
    public function homeType(): HomeValidation
    {
        $this->rules['type'] = 'required|in:TODAY,LATEST7,LATEST30';
        return $this;
    }

    /**
     * 首页类型校验
     *
     * @return $this
     */
    public function homeTypeNull(): HomeValidation
    {
        $this->rules['type'] = 'nullable|in:TODAY,LATEST7,LATEST30';
        return $this;
    }
}
