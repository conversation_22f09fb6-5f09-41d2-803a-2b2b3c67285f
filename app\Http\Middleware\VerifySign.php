<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;

class VerifySign
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if (empty($request->input('timestamp')) || !$request->hasHeader('signature')) {
                throw new \RuntimeException('缺少必传参数', 2000);
            }
            //一分钟有效
            if ($request->timestamp < time() - 60 || $request->timestamp > time() + 60) {
                throw new \RuntimeException('请求超时', 2001);
            }
            //验签
            $this->verifySign($request);
        } catch (Exception $exception) {
            return response()->json(['data' => '', 'message' => $exception->getMessage(), 'code' => $exception->getCode()]);
        }
        return $next($request);
    }

    /**
     * 验证签名
     * @param Request $request
     * @throws Exception
     */
    private function verifySign(Request $request): void
    {
        $data = $request->all();
        $timestamp = $data['timestamp'];
        unset($data['report'], $data['timestamp']);
        $payload = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK | JSON_PRETTY_PRINT);
        $sign = sha1(implode(":", [$payload, env('API_SECRET'), $timestamp]));
        if ($sign != $request->header('signature')) {
            throw new \RuntimeException('非法请求', 2002);
        }
    }
}
