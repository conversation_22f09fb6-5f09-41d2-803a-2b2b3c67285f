<?php

/**
 * COS服务类
 * @desc COS服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Cos;

use Qcloud\Cos\Client;

class CosService
{
    /**
     * 设置最大遍历出多少个对象, 一次listObjects最大支持1000
     *
     * @var int
     */
    const MAX_KEYS = 1000;

    /**
     * COS配置信息
     *
     * @var array
     */
    private $cosConfig;

    /**
     * COS客户端对象
     *
     * @var Client
     */
    private $client;

    /**
     * 初始化
     */
    public function __construct(string $tag)
    {
        $this->initCosConfig($tag);
        $this->client = new Client($this->cosConfig);
    }

    /**
     * 初始化配置参数
     *
     * @return void
     */
    private function initCosConfig(string $tag)
    {
        $config = config("cos.{$tag}");
        $this->cosConfig = [
            'region' => $config['region'],
            'schema' => 'https', //协议头部，默认为http
            'credentials' => [
                'secretId' => $config['secret_id'],
                'secretKey' => $config['secret_key'],
            ],
            'bucket' => $config['bucket'],
        ];
    }

    /**
     * 获取listObjects的参数
     *
     * @param string $nextMarker
     * @param int $maxKeys
     * @return array
     */
    private function getListObjectsArgs(string $nextMarker, string $directory, int $maxKeys = self::MAX_KEYS): array
    {
        return [
            'Bucket' => $this->cosConfig['bucket'],
            'Delimiter' => '',
            'EncodingType' => 'url',
            'Marker' => urldecode($nextMarker),
            'Prefix' => "{$directory}/",
            'MaxKeys' => $maxKeys,
        ];
    }

    /**
     * 获取文件夹下所有文件名称
     *
     * @return array
     */
    public function getDirectoryFiles(string $directory): array
    {
        //标记下个开始的位置
        $nextMarker = '';
        //是否结束
        $isTruncated = true;
        //文件列表
        $files = [];
        //开始循环
        while ($isTruncated) {
            //获取目录下的文件信息
            $result = $this->client->listObjects($this->getListObjectsArgs($nextMarker, $directory));
            //是否结束
            $isTruncated = $result['IsTruncated'] ?? false;
            //标记下个开始的位置
            $nextMarker = $result['NextMarker'] ?? '';
            //判断Contents是否为空，为空跳出循环
            if (empty($result['Contents'])) {
                break;
            }
            //添加到数组中
            $files = array_merge($result['Contents'], $files);
        }
        //新的文件数组集合
        $newFiles = [];
        //循环处理数据
        foreach ($files as $item) {
            $value = str_replace("{$directory}/", "", $item['Key']);
            // 判断是否为空
            if (!empty($value)) {
                $newFiles[] = $value;
            }
        }
        //返回结果
        return $newFiles;
    }
}
