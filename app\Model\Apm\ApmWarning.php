<?php

/**
 * 预警
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApmWarning extends Model
{
    use ModelTrait;

    public $connection = "apm";

    protected $table = 'warning';

    protected $primaryKey = 'warning_id';

    protected $guarded = [];

    protected $casts = [
        'app_version' => 'json',
        'receiving_person' => 'json',
        'receiving_phone' => 'json',
        'receiving_group' => 'json',
    ];

    //状态 是否开启
    public const START = 1;
    public const CLOSE = 0;

    //监控类型 1：大盘监控 2：问题监控
    const REPORT_MONITOR = 1;
    const ERROR_MONITOR = 2;

    /**
     * 关联预警规则
     */
    public function warningRule(): HasMany
    {
        return $this->hasMany(ApmWarningRule::class, 'warning_id', 'warning_id');
    }
}

