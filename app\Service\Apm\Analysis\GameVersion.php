<?php

/**
 * 游戏版本分析
 * @desc 游戏版本分析
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/01
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Analysis;

use App\Model\Apm\StarRocks\BaseBuilder;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Service\Apm\Performance\ApmBase;
use Illuminate\Database\Query\Builder;

class GameVersion extends BaseAnalysis
{
    /**
     * 获取通用的构造器
     *
     * @return BaseBuilder|Builder|mixed
     */
    protected function getCommonBuilder()
    {
        return MysqlApmReportList::query() //查询报告表
            ->join($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->performance_score_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_score_data_table}.session_id") //关联报告评分表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->where("{$this->mysql_apm_report_list_table}.app_version_name", '<>', '') //过滤掉版本号为空的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['game_version_code']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            });
    }

    /**
     * 版本概况，根据传入的时间筛选游戏版本数据，区分达标和不达标，返回列表
     *
     * @return array
     */
    public function summary(): array
    {
        // 获取数据
        $list = $this->getCommonBuilder()
            ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name as game_version_code, round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score, count({$this->mysql_apm_report_list_table}.id) as num") //查询版本号、平均分、报告数
            ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name") //根据版本号分组
            ->orderBy('num', 'desc') //按报告数倒序
            ->getFromSR();
        // 根据分数划分达标和不达标
        $substandard = $standard = [];
        foreach ($list as $item) {
            if ($item['score'] < ApmBase::STANDARD_SCORE) {
                $substandard[] = $item;
            } else {
                $standard[] = $item;
            }
        }
        // 返回数据
        return [
            'substandard' => $substandard,
            'standard' => $standard,
            'standard_score' => ApmBase::STANDARD_SCORE,
        ];
    }

    /**
     * 根据传入的时间筛选游戏版本数据，版本前10个，根据报告数排序
     *
     * @return array
     */
    public function top(): array
    {
        return $this->getCommonBuilder()
            ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name as game_version_code, round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score, count({$this->mysql_apm_report_list_table}.id) as num") //查询版本号、平均分、报告数
            ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name") //根据版本号分组
            ->orderBy('num', 'desc') //根据报告数倒序
            ->limit(10) //取前10个
            ->getFromSR();
    }

    /**
     * 获取版本数据列表，评分、fps、内存、卡顿率、cpu的列表
     *
     * @return array
     */
    public function list(): array
    {
        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_report_list_table}.app_version_name as game_version_code,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
count({$this->mysql_apm_report_list_table}.id) as num,
round(sum({$this->performance_stat_data_table}.sum_cpu_usage) / sum({$this->performance_stat_data_table}.num), 2) as cpu,
round(sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num), 2) as memory,
round(sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_score_data_table}.session_id), 2) as max_memory,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_score_data_table}.session_id), 2) as big_jank,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter
EXPRESSION;

        $builder = $this->getCommonBuilder()
            ->selectRaw($selectRaw)
            ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name"); //根据版本号分组
        // 判断筛选条件是否为空
        if (!empty($this->params['filter_top'])) {
            // 判断筛选条件
            if ($this->params['filter_top'] == 'top') {
                $builder->orderBy('score', 'desc');
            } else {
                $builder->orderBy('score', 'asc');
            }
            $builder->limit(10);
        } else {
            $builder->orderBy($this->sortField, $this->sortType);
        }
        // 返回处理后的数据
        return $this->handleListData($builder->getFromSR());
    }
}
