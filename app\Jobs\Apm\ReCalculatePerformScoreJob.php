<?php

namespace App\Jobs\Apm;

use App\Model\Perf\Report;
use App\Service\PerfomanceScrore\BigJankPerformanceScore;
use App\Service\PerfomanceScrore\MaxMemoryPercentPerformanceScore;
use App\Service\PerfomanceScrore\SmoothnessPerformanceScore;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ReCalculatePerformScoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    //文件绝对路径
    protected $fullPath;
    // 文件项目路径
    protected $reportPath;
    // 运行内存
    protected $ram;
    // 运行内存 - mb
    protected $ramMb;
    // 原始数据
    protected $data;
    // 替换的数据
    protected $replaceData;

    /**
     * 构造函数
     *
     * @param string $reportPath
     * @param string $ram
     */
    public function __construct(string $reportPath, string $ram)
    {
        $this->reportPath = $reportPath;
        $this->ram = $ram;
    }

    /**
     * 处理函数
     * @return void
     */
    public function handle()
    {
        try {
            //读取前端传入json
            if (!$this->readFile()) {
                return;
            }

            // 检查是否满足重新计算条件
            if (!$this->canReScore()) {
                return;
            }

            // 重新评分
            $this->reScore();

            // rewrite to json
            $this->rewriteJson();

            // rewrite to db
            $this->rewriteDB();
        } catch (\Throwable $e) {
            \Log::error('VirtualCoroutineJob:' . $e->getMessage() . ' file: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    /**
     * 读取文件内容
     * @return bool
     */
    private function readFile()
    {
        $this->fullPath = storage_path('app/public/') . $this->reportPath;
        $file = file_get_contents($this->fullPath);
        if (empty($file)) {
            return false;
        }

        $this->data = json_decode($file, true);
        return true;
    }

    /**
     * 检查是否满足重新计算分数条件
     *
     * 根据是否有传运行最大ram判定，有传且 > 0GB 走新规
     * @return bool
     */
    private function canReScore(): bool
    {
        if (empty($this->ram)) {
            return false;
        }

        $isMatch = preg_match("/^([0-9]+([.]+[0-9]+)?)?\s?GB$/i", $this->ram, $match);
        if (!$isMatch || floatval($match[1]) == 0) {
            return false;
        }

        $this->ramMb = floatval($match[1]) * 1024;
        return true;
    }

    /**
     * 重新计算分数
     * @return void
     */
    private function reScore()
    {
        if (empty($this->data['ScoreDetail'])) {
            return;
        }
        $level = $this->data['ScoreDetail']['tier'];
        $jankCount = $totalFrameCount = 0;
        foreach ($this->data['Data'] as $dataForSecond) {
            $jankCount += $dataForSecond['fpsInfo']['Jank'];
            $jankCount += $dataForSecond['fpsInfo']['BigJank'];
            $totalFrameCount += count($dataForSecond['fpsInfo']['FrameTimes']);
        }

        $smoothnessScore = (new SmoothnessPerformanceScore($level, $jankCount, $totalFrameCount))->getPerformanceScore();
        $maxMemoryScore = (new MaxMemoryPercentPerformanceScore($level, $this->data['ScoreDetail']['pss_memory_peak'], $this->ramMb))->getPerformanceScore();
        $bigJankScore = (new BigJankPerformanceScore($level, $this->data['ScoreDetail']['avg_10_big_Jank']))->getPerformanceScore();
        $this->replaceData = [
            'smoothness_score' => $smoothnessScore,
            'peak_memory_score' => $maxMemoryScore,
            'big_jank_score' => $bigJankScore,
        ];
    }

    /**
     * 复写到 json
     * @return void
     */
    private function rewriteJson()
    {
        if (!empty($this->replaceData)){
            $this->data['ScoreV2'] = $this->replaceData;
            file_put_contents($this->fullPath, json_encode($this->data));
        }
    }

    /**
     * 复写到 json
     * @return void
     */
    private function rewriteDB()
    {
        if (!empty($this->replaceData)) {
            $smt = Report::query()->select(['report_id', 'extra'])->where("report_url", "storage/" . $this->reportPath)->first();
            if (empty($smt)) {
                return;
            }
            $newExtra = json_decode($smt->extra, true);
            $newExtra['ScoreV2'] = $this->replaceData;
            Report::query()->where('report_id', $smt->report_id)->update(['extra' => json_encode($newExtra)]);
        }
    }
}
