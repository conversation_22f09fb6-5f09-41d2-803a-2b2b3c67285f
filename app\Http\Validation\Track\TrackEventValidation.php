<?php

/**
 * 引擎组埋点校验类
 * @desc 引擎组埋点校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/03/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation\Track;

use App\Http\Validation\BaseValidation;

/**
 * @method static TrackEventValidation build()
 */
class TrackEventValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): TrackEventValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * label
     *
     * @return $this
     */
    public function label(): TrackEventValidation
    {
        $this->rules['label'] = 'required|string';
        return $this;
    }

    /**
     * 应用名称
     *
     * @return $this
     */
    public function appName(): TrackEventValidation
    {
        $this->rules['app_name'] = 'nullable|string';
        return $this;
    }

    /**
     * 创建时间
     *
     * @return $this
     */
    public function startDate(): TrackEventValidation
    {
        $this->rules['start_date'] = 'required|string';
        return $this;
    }

    /**
     * 结束时间
     *
     * @return $this
     */
    public function endDate(): TrackEventValidation
    {
        $this->rules['end_date'] = 'required|string';
        return $this;
    }
}
