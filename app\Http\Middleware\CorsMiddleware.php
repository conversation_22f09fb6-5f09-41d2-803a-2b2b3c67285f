<?php

namespace App\Http\Middleware;

use Closure;

class CorsMiddleware
{
    /**
     * 跨域中间建
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $origin = $request->server('HTTP_ORIGIN') ? $request->server('HTTP_ORIGIN') : '';
        header("Access-Control-Allow-Origin:" . $origin);
        header("Access-Control-Allow-Credentials:true");
        header("Access-Control-Allow-Methods:POST,GET,OPTIONS,PUT,DELETE");
        header("Access-Control-Allow-Headers:Content-Type,Access-Token");
        header("Access-Control-Expose-Headers:*");

        return $next($request);
    }
}
