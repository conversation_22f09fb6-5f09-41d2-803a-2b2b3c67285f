<?php

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * 统一接口数据结构返回
     * @param int $code
     * @param array $data
     * @param null $message
     * @param int $status
     * @return JsonResponse
     */
    public function response(int $code = 0, $data = [], $message = null, int $status = 200): JsonResponse
    {
        $message = $message ?? StatusCode::getErrorMessage($code);

        return response()->json(compact('code', 'message', 'data'), $status);
    }
}
