<?php

namespace App\Model;

class Report extends BaseModel
{
    use ModelTrait;

    protected $table = 'ab_report';
    protected $primaryKey = 'report_id';

    protected $fillable = [
        'developer_app_id',
        'report_url',
        'all_ab_num',
        'dependence_ab_num',
        'redundancy_ab_num',
        'all_resource_num',
        'redundancy_resource_num',
        'redundancy_resource_size',
        'remark',
        'platform'
    ];
    public $validateRule = [
        'developer_app_id' => 'required|int',
        'report_url' =>'required|string',
        'all_ab_num'=> 'required|int',
        'dependence_ab_num'=> 'required|int',
        'redundancy_ab_num'=> 'required|int',
        'all_resource_num'=> 'required|int',
        'redundancy_resource_num'=> 'required|int',
        'redundancy_resource_size'=> 'required|int',
        'remark' =>'string|max:255'
    ];
}
