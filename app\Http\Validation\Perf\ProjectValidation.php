<?php

namespace App\Http\Validation\Perf;

use App\Http\Validation\BaseValidation;
use Illuminate\Database\Query\Builder;
use Illuminate\Validation\Rule;

/**
 * @method static ProjectValidation build()
 */
class ProjectValidation extends BaseValidation
{
    /**
     * 提示信息
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'title.unique' => '该项目名称已存在',
        ];
    }

    /**
     * 项目ID校验
     *
     * @return $this
     */
    public function projectId(): ProjectValidation
    {
        $this->rules['project_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 分页条数校验
     *
     * @return $this
     */
    public function limit(): ProjectValidation
    {
        $this->rules['limit'] = 'integer|min:10|max:10000';
        return $this;
    }

    /**
     * 标题校验
     *
     * @return $this
     */
    public function title(): ProjectValidation
    {
        $this->rules['title'] = [
            'required',
            'string',
            Rule::unique('tool.perf_project')->where(function (Builder $query) {
                $request = request();
                return $query->when($request->has('project_id'), function (Builder $query) use ($request) {
                        return $query->where('id', '<>', $request->input('project_id'));
                    });
            }),
        ];
        return $this;
    }

    /**
     * 包名校验
     *
     * @return $this
     */
    public function packageName(): ProjectValidation
    {
        $this->rules['package_name'] = 'required|string';
        return $this;
    }

    /**
     * 页码校验
     *
     * @return $this
     */
    public function page(): ProjectValidation
    {
        $this->rules['page'] = 'integer|min:1';
        return $this;
    }
}
