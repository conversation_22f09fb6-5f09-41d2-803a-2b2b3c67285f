<?php

namespace App\Service\PerfomanceScrore;

abstract class PerformanceScoreService
{
    //档位
    const LEVEL_LOW = 1;
    const LEVEL_MEDIUM = 2;
    const LEVEL_HIGH = 3;

    // 卡帧率
    const PROPORTION_LOW = 2;
    const PROPORTION_MEDIUM = 2;
    const PROPORTION_HIGH = 2;

    // bigjank次数
    const BIGJANK_LIMIT_LOW = 20;
    const BIGJANK_LIMIT_MEDIUM = 20;
    const BIGJANK_LIMIT_HIGH = 20;

    // 占用内存百分比
    const MAX_MEMORY_LOW = 60;
    const MAX_MEMORY_MEDIUM = 60;
    const MAX_MEMORY_HIGH = 60;

    // 系统
    const OS_ANDROID = 'android';
    const OS_IOS = 'ios';

    /**
     * 档位对应卡顿率占比
     */
    const LEVEL_TO_PROPORTION = [
        self::LEVEL_LOW => self::PROPORTION_LOW,
        self::LEVEL_MEDIUM => self::PROPORTION_MEDIUM,
        self::LEVEL_HIGH => self::PROPORTION_HIGH,
    ];

    /**
     * 档位对应bigJank次数
     */
    const LEVEL_TO_BIGJANK_COUNT = [
        self::LEVEL_LOW => self::BIGJANK_LIMIT_LOW,
        self::LEVEL_MEDIUM => self::BIGJANK_LIMIT_MEDIUM,
        self::LEVEL_HIGH => self::BIGJANK_LIMIT_HIGH,
    ];

    /**
     * 档位对应内存消耗指标
     */
    const LEVEL_TO_MEMORY = [
        self::LEVEL_LOW => self::MAX_MEMORY_LOW,
        self::LEVEL_MEDIUM => self::MAX_MEMORY_MEDIUM,
        self::LEVEL_HIGH => self::MAX_MEMORY_HIGH,
    ];


}
