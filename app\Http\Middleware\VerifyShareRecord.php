<?php
/**
 * VerifyShareRecord.php
 *
 * User: Dican
 * Date: 2022/9/8
 * Email: <<EMAIL>>
 */

namespace App\Http\Middleware;


use App\Model\ShareRecord;
use Closure;

class VerifyShareRecord
{
    public function handle($request, Closure $next)
    {
        try {
            $recordId = $request->input('share_record_id');
            $sign = $request->input('sign');
            if (empty($recordId) || empty($sign)) {
                return response()->json(['data' => [], 'message' => '缺少头部必传参数', 'code' => 1000]);
            }
            $record = ShareRecord::findOrFail($recordId);
            if ($record->getSign() != $sign) {
                return response()->json(['data' => [], 'message' => '验签不通过', 'code' => 1006]);
            }
            if ($record->limit != 0 && time() > (strtotime($record->created_at) + 3600 * $record->limit)) {
                return response()->json(['data' => [], 'message' => '分享链接已失效', 'code' => 1006]);
            }
        } catch (\Exception $exception) {
            return response()->json(['data' => [], 'message' => '非法操作数据', 'code' => 1011]);
        }
        return $next($request);
    }
}
