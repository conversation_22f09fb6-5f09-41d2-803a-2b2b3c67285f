<?php

/**
 * 大盘监控
 * @desc 大盘监控
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/07/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Jobs\Apm;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Service\Apm\Performance\ApmTrait;
use Carbon\Carbon;

class StatMonitorJob extends BaseMonitorJob
{
    /**
     * 引入监控的trait
     */
    use ApmTrait;

    /**
     * 开始时间
     *
     * @var string
     */
    protected $startTime;

    /**
     * 结束时间
     *
     * @var string
     */
    protected $endTime;

    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 是否按天
     *
     * @var bool
     */
    protected $isDay = true;

    /**
     * 构造函数
     *
     * @param array $apmWarning
     */
    public function __construct(array $apmWarning)
    {
        parent::__construct($apmWarning);
        // 判断预警执行周期
        if (empty($apmWarning['schedule_time']) || $apmWarning['schedule_time'] == 86400) {
            //设置开始时间和结束时间
            $this->startTime = Carbon::yesterday()->startOfDay()->toDateTimeString();
            $this->endTime = Carbon::yesterday()->endOfDay()->toDateTimeString();
        } else {
            $this->isDay = false;
            //设置开始时间和结束时间
            $this->startTime = Carbon::yesterday()->subDays(6)->startOfDay()->toDateTimeString();
            $this->endTime = Carbon::yesterday()->endOfDay()->toDateTimeString();
        }
    }

    /**
     * 检查是否需要监控
     *
     * @return bool
     */
    protected function checkMonitor(): bool
    {
        //获取当前时间的时间戳
        $currentTime = time();
        //获取10点的时间戳
        $tenHourTime = strtotime(date('Y-m-d 10:00:00'));
        //1、判断当前时间是否大于等于10点，小于不执行
        if ($currentTime < $tenHourTime) {
            return false;
        }

        //2、获取创建时间
        $createdAt = Carbon::parse($this->apmWarning['created_at']);
        //3、如果创建时间是今天，并且创建时间是10点后，不执行
        if (($createdAt->toDateString() == Carbon::now()->toDateString()) && ($tenHourTime < $createdAt->timestamp)) {
            return false;
        }

        // 判断预警执行周期
        if (!empty($this->apmWarning['schedule_time']) && $this->apmWarning['schedule_time'] == 604800) {
            // 判断是否周一
            if (Carbon::now()->dayOfWeek != 1) {
                return false;
            }
        }

        //4、判断是否有上次时间的时间戳，有的话判断当前时间是否大于等于上次时间的第二天的0点，小于不执行
        if (!empty($this->apmWarning['last_warning_time'])) {
            //获取上次时间的第二天的0点的时间戳
            $time = Carbon::parse($this->apmWarning['last_warning_time'])->addDays()->startOfDay()->timestamp;
            //当前时间小于上次时间的第二天的0点，不执行
            if ($currentTime < $time) {
                return false;
            }
        }
        //5、可以执行
        return true;
    }

    /**
     * 获取监控的数据
     *
     * @return void
     */
    protected function getMonitorData()
    {
        //参数
        $this->params = [
            'developer_app_id' => $this->apmWarning['developer_app_id'],
            'os_type' => $this->apmWarning['os_type'],
            'game_version' => $this->apmWarning['app_version'],
        ];
        // 获取优化建议
        $this->monitorData['optimization_advice'] = $this->getAvgScore();
        // 计算环比
        $keys = ['dev_num', 'score_num', 'low_score_num', 'score'];
        foreach ($keys as $key) {
            // 判断 match_num 是否为0
            if (empty($this->monitorData['laster'][$key])) {
                $this->monitorData["{$key}_mom"] = bcmul($this->monitorData['current'][$key], 100, 2);
            } else {
                $this->monitorData["{$key}_mom"] = bcmul(bcdiv(bcsub($this->monitorData['current'][$key], $this->monitorData['laster'][$key], 4), $this->monitorData['laster'][$key], 4), 100, 2);
            }
        }
        $this->setText();
    }

    /**
     * 设置文案
     *
     * @return void
     */
    private function setText()
    {
        $keys = ['dev_num_mom', 'score_num_mom', 'low_score_num_mom', 'score_mom'];
        foreach ($keys as $key) {
            // 判断上升还是下降
            if ($this->monitorData[$key] > 0) {
                $this->monitorData[$key] = "<font color='info'>↑" . abs($this->monitorData[$key]) . "%</font>";
            } else {
                $this->monitorData[$key] = "<font color='red'>↓" . abs($this->monitorData[$key]) . "%</font>";
            }
        }
        $dateText = $this->isDay ? '昨日' : '上周';
        // 设置文案
        // $this->monitorData['text'] = "App名称：{$this->apmWarning['app']['app_name']}\n平台：{$this->platform}\n版本：{$this->version}\n情况概览：{$dateText}联网设备数为{$this->monitorData['current']['dev_num']}(环比{$this->monitorData['dev_num_mom']})，共收集了{$this->monitorData['current']['score_num']}次性能数据(环比{$this->monitorData['score_num_mom']})，其中低于标准分(80分)的次数为{$this->monitorData['current']['low_score_num']}(环比{$this->monitorData['low_score_num_mom']})，整体平均分是{$this->monitorData['current']['score']}(环比{$this->monitorData['score_mom']})。";
        // // 判断是否有优化建议
        // if (!empty($this->monitorData['optimization_advice'])) {
        //     $this->monitorData['text'] .= "\n优化建议：{$this->monitorData['optimization_advice']}";
        // }
        // 设置文案
        // 不达标率
        $failureRate = round(bcdiv($this->monitorData['current']['low_score_num'], $this->monitorData['current']['score_num'], 6) * 100, 2);
        $this->monitorData['text'] = "App名称：{$this->apmWarning['app']['app_name']}\n平台：{$this->platform}\n版本：{$this->version}\n情况概览：{$dateText}联网设备数为{$this->monitorData['current']['dev_num']}，共收集了{$this->monitorData['current']['score_num']}次性能数据，其中低于标准分(80分)的次数为{$this->monitorData['current']['low_score_num']}，整体平均分是{$this->monitorData['current']['score']}(建议值80)，游戏运行次数性能不达标率为：$failureRate%。其中：\n";
        $this->monitorData['text'] .= "    游戏卡顿率（建议值不高于2%）：{$this->monitorData['current']['smoothness_score']}%\n";
        $this->monitorData['text'] .= "    内存平均峰值（建议值不高于1600M）：{$this->monitorData['current']['avg_memory_score']}MB\n";
        if (!empty($this->monitorData['current']['battery_power'])) {
            $this->monitorData['text'] .= "    功耗平均峰值：{$this->monitorData['current']['battery_power']}mW";
        }
    }

    /**
     * 获取最近性能平均分
     *
     * @return string
     */
    private function getAvgScore(): string
    {
        $timeList = [
            [
                'start_time' => $this->startTime,
                'end_time' => $this->endTime,
            ],
        ];
        if ($this->isDay) {
            $timeList[] = [
                'start_time' => Carbon::parse($this->startTime)->subDays()->toDateTimeString(),
                'end_time' => Carbon::parse($this->endTime)->subDays()->toDateTimeString(),
            ];
        } else {
            $timeList[] = [
                'start_time' => Carbon::parse($this->startTime)->subWeeks()->toDateTimeString(),
                'end_time' => Carbon::parse($this->endTime)->subWeeks()->toDateTimeString(),
            ];
        }
        $result = [];
        //获取报告数量、设备数量、平均分、低于标准分数量
        $selectRaw = <<<COLUMNS
count({$this->performance_score_data_table}.session_id) as score_num,
count(distinct {$this->mysql_apm_report_list_table}.dev_str) as dev_num,
sum(case when {$this->performance_score_data_table}.all_score < {$this->standardScore} then 1 else 0 end) as low_score_num,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps_score,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as smoothness_score,
round((sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as avg_memory_score,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_stat_data_table}.session_id), 2) as big_jank_score,
round(sum({$this->performance_stat_data_table}.sum_battery_power) / sum({$this->performance_stat_data_table}.num), 2) as battery_power
COLUMNS;
        foreach ($timeList as $time) {
            $list = PerformanceScoreData::query() //查询性能分数表
                ->selectRaw($selectRaw) //计算平均分
                ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") //关联性能统计表
                ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") //关联性能报告表
                ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                    return $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id") //关联效能后台id
                        ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str"); //关联设备唯一标识
                })
                ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉小于最小时长的数据
                ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$time['start_time'], $time['end_time']]) //过滤掉不在时间范围内的数据
                ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前效能后台Id的数据
                ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
                ->when(!empty($this->params['os_type']), function ($query) { //如果有传平台值，过滤掉不在平台范围内的数据
                    $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
                })
                ->when(!empty($this->params['game_version']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                    return $query->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version']);
                })
                ->firstFromSR();
            $result[] = $list;
        }
        $this->monitorData['current'] = $result[0];
        $this->monitorData['laster'] = $result[1];
        // 处理结果
        return $this->handleAvgText($result);
    }

    /**
     * 处理总体趋势文案
     *
     * @param $result
     * @return string
     */
    private function handleAvgText($result)
    {
        //判断是否有数据
        if (empty($result[0]['score'])) {
            return '';
        }
        // 判断是否有环比
        if (empty($result[1]) || empty($result[1]['score'])) {
            return '';
        }
        // 判断趋势
        $trend = $result[0]['score'] > $result[1]['score'] ? '提升' : '下降';
        // 计算变化率
        $round = round(bcdiv(abs($result[0]['score'] - $result[1]['score']), $result[1]['score'], 6) * 100, 2);
        // 文案
        $text = "整体平均分环比{$trend}{$round}%，";
        // 拼接文案
        $text .= $this->getMetricText($result, $trend);
        return $text;
    }

    /**
     * 获取指标文案
     *
     * @param $result
     * @param $trend
     * @return string
     */
    private function getMetricText($result, $trend)
    {
        // 判断哪些指标影响评分
        $maxField = '';
        $maxValue = 0;
        $trendText = '下降';
        $fields = ['smoothness_score', 'avg_memory_score', 'big_jank_score'];
        // 判断趋势
        if ($trend == '提升') {
            foreach ($fields as $field) {
                if (($result[0][$field] - $result[1][$field]) < 0) {
                    if (round($result[1][$field] - $result[0][$field], 2) > $maxValue) {
                        $maxField = $field;
                        $maxValue = round($result[1][$field] - $result[0][$field], 2);
                    }
                }
            }
            $field = 'fps_score';
            if (($result[0][$field] - $result[1][$field]) > 0) {
                if (round($result[0][$field] - $result[1][$field], 2) > $maxValue) {
                    $maxField = $field;
                    $maxValue = round($result[0][$field] - $result[1][$field], 2);
                    $trendText = '上升';
                }
            }
        } else {
            foreach ($fields as $field) {
                if (($result[1][$field] - $result[0][$field]) < 0) {
                    if (round($result[0][$field] - $result[1][$field], 2) > $maxValue) {
                        $maxField = $field;
                        $maxValue = round($result[0][$field] - $result[1][$field], 2);
                        $trendText = '上升';
                    }
                }
            }
            $field = 'fps_score';
            if (($result[1][$field] - $result[0][$field]) > 0) {
                if (round($result[1][$field] - $result[0][$field], 2) > $maxValue) {
                    $maxField = $field;
                    $maxValue = round($result[1][$field] - $result[0][$field], 2);
                }
            }
        }
        // 判断是否为空
        if (empty($maxField)) {
            return '';
        }
        // 获取字段名称
        $filed = ['fps_score' => 'FPS帧率', 'smoothness_score' => '卡顿率', 'avg_memory_score' => '内存均值', 'big_jank_score' => 'BigJank卡顿'][$maxField];
        $unit = ['fps_score' => '帧', 'smoothness_score' => '%', 'avg_memory_score' => 'MB', 'big_jank_score' => '次/10分钟'][$maxField];
        // 拼接文案
        $text = "其中{$filed}{$trendText}变化明显(由{$result[1][$maxField]}{$unit}{$trendText}到{$result[0][$maxField]}{$unit})";
        // 判断上升还是下降
        if ($trend == '提升') {
            $text .= "，请继续保持当前的优化措施。";
        } else {
            $text .= "，请尽快定位性能问题，并采取相应措施进行优化。";
        }
        return $text;
    }

    /**
     * 检查是否需要报警
     *
     * @return bool
     */
    protected function checkWarning(): bool
    {
        return true;
    }

    /**
     * 获取接收者的信息
     *
     * @return string
     */
    protected function getPersonMessage(): string
    {
        $time = $this->isDay ? '每日' : '每周';
        $params = '?range=["' . $this->startTime . '","' . $this->endTime . '"]&type=warning_push';
        return "线上性能 {$time}汇总 [{$this->apmWarning['name']}][{$this->date}]\n" . $this->monitorData['text'] . "\n统计时间：{$this->startTime}~{$this->endTime}\n<a href='{$this->getUrl($params)}'>【查看详情】</a>";
    }

    /**
     * 获取接收群的信息
     *
     * @return string
     */
    protected function getGroupMessage(): string
    {
        $time = $this->isDay ? '每日' : '每周';
        $params = '?range=["' . $this->startTime . '","' . $this->endTime . '"]&type=warning_push';
        return "**线上性能 {$time}汇总 [{$this->apmWarning['name']}][{$this->date}]**\n" . $this->monitorData['text'] . "\n统计时间：{$this->startTime}~{$this->endTime}\n[【查看详情】]({$this->getUrl($params)})";
    }

    /**
     * 获取接收手机的信息
     *
     * @return string
     */
    protected function getPhoneMessage(): string
    {
        $time = $this->isDay ? '每日' : '每周';
        return "线上性能 {$time}汇总 [{$this->apmWarning['name']}][{$this->date}]\n" . $this->monitorData['text'] . "\n统计时间：{$this->startTime}~{$this->endTime}";
    }
}
