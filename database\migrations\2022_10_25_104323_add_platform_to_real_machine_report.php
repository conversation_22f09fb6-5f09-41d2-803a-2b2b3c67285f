<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPlatformToRealMachineReport extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('real_machine_report', function (Blueprint $table) {
            //
            $table->string('device_model',128)->default('')->comment('机型设备');
            $table->unsignedTinyInteger('platform')->default(1)->comment('平台（1为PC、2为安卓、3为苹果）');
            $table->float('avg_cpu')->default(0)->comment('CPU耗时均值');
            $table->float('max_mem')->default(0)->comment('设备内存峰值');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('real_machine_report', function (Blueprint $table) {
            //
            $table->dropColumn('device_model');
            $table->dropColumn('platform');
            $table->dropColumn('avg_cpu');
            $table->dropColumn('max_mem');
        });
    }
}
