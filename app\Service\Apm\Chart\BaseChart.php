<?php

/**
 * apm图表
 * @desc apm图表
 * <AUTHOR> <EMAIL>
 * @date 2024/08/14
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\BaseBuilder;
use App\Model\Apm\StarRocks\PerformanceStatData;

abstract class BaseChart
{
    const CLASS_NAME = [
        'cpuInfo' => CpuInfoChart::class, // CPU模块
        'networkInfo' => NetworkInfoChart::class, // 网络模块
        'gameInfo' => GameInfoChart::class, // 游戏数据模块
        'memoryInfo' => MemoryInfoChart::class, // 内存模块
        'batteryInfo' => BatteryInfoChart::class, // 电池模块
        'tagsInfo' => TagsInfoChart::class, // 标签模块
        'frameTimeInfo' => FrameTimeInfoChart::class, // 帧率模块
        'fpsInfo' => FpsInfoChart::class, // FPS模块
        'tempInfo' => TempInfoChart::class, // 温度模块
        'imageInfo' => ImageInfoChart::class, // 图片模块
        'gpuInfo' => GpuInfoChart::class, // gpu模块
        'ioInfo' => IoInfoChart::class, // io模块
        'handleInfo' => HandleInfoChart::class, // handle模块
    ];

    /**
     * 类型
     *
     * @var string
     */
    protected $type;

    /**
     * 报告ID
     *
     * @var int
     */
    protected $reportId;

    /**
     * 标签
     *
     * @var array
     */
    protected $tags;

    /**
     * 查询到的数据
     *
     * @var array
     */
    protected $result = [];

    /**
     * 构造函数
     */
    public function __construct($type, $reportId, $tags)
    {
        $this->type = $type;
        $this->reportId = $reportId;
        $this->tags = $tags;
    }

    /**
     * 获取Builder
     *
     * @return BaseBuilder
     */
    abstract protected function getBuilder();

    /**
     * 处理数据
     *
     * @return array
     */
    abstract protected function handleData(): array;

    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        // 获取报告的时长
        $reportDuration = PerformanceStatData::query()
            ->select(['duration'])
            ->where('session_id', $this->reportId)
            ->limit(1)
            ->firstFromSR();
        $builder = $this->getBuilder()->limit($reportDuration['duration'] ?? 500)->orderBy('perf_data_ts')->orderBy('game_info_ts');
        $this->result = $builder->getFromSR();
        return $this->handleData();
    }

    /**
     * 创建对应报表
     *
     * @param $type
     * @param $reportId
     * @param $tags
     * @return BaseChart
     */
    public static function createChart($type, $reportId, $tags): BaseChart
    {
        $className = static::CLASS_NAME[$type];
        return new $className($type, $reportId, $tags);
    }
}
