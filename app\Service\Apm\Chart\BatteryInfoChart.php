<?php

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class BatteryInfoChart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'battery_level',
                'battery_power',
                'thermal_state',
                'tags_info',
                'app_state',
                'perf_data_ts',
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['batteryInfo'] = [ //按照时间戳分组
                'BElectricity' => $item['battery_level'],   //电量
                'BPower' => $item['battery_power'], //功率
                'thermalState' => $item['thermal_state'],   //电池温度状态
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }
        return array_values($list);
    }
}
