<?php

namespace App\Http\Middleware\Perf;

use Closure;
use Exception;
use Illuminate\Http\Request;

class PerfVerifySignV2
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if (empty($request->input('timestamp')) || !$request->hasHeader('signature')) {
                throw new \RuntimeException('缺少必传参数', 2000);
            }
            //验签
            $this->verifySign($request);
        } catch (Exception $exception) {
            return response()->json(['data' => '', 'message' => $exception->getMessage(), 'code' => $exception->getCode()]);
        }
        return $next($request);
    }

    /**
     * 验证签名
     * @param Request $request
     * @throws Exception
     */
    private function verifySign(Request $request): void
    {
        $data = $request->all();
        $timestamp = $data['timestamp'];
        $params = [
            'upload_id' => $data['upload_id'] ?? '',
            'serial' => $data['serial'] ?? '',
            'package_name' => $data['package_name'] ?? '',
            'duration' => $data['duration'] ?? '',
        ];
        $payload = json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK | JSON_PRETTY_PRINT);
        $params['duration'] = intval($params['duration']);
        $payload2 = json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        $sign = sha1(implode(":", [$payload, env('API_SECRET'), $timestamp]));
        $sign2 = sha1(implode(":", [$payload2, env('API_SECRET'), $timestamp]));
        if ($sign != $request->header('signature') && $sign2 != $request->header('signature')) {
            throw new \RuntimeException('非法请求', 2002);
        }
    }
}
