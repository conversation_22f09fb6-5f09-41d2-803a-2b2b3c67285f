<?php

/**
 * 推送apm评分排名
 * @desc 推送apm评分排名
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/02/19
 */

namespace App\Console\Commands;

use App\Components\Helper\Curl;
use App\Model\Apm\ApmGlobalConfig;
use App\Model\Apm\StarRocks\MysqlApmDeviceList;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Model\Apm\StarRocks\PerformanceStatData;
use App\Model\Apm\StarRocks\StarRocksDB;
use App\Model\Apps;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PushApmScoreRankCommand extends Command
{
    /**
     * 生成图片接口签名
     *
     * @var string
     */
    const MSG_SIGN = '8c8d357b5e872bbacd45197626bd5759';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'push:apm:score:rank';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '推送apm评分排名脚本';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     *
     * @return void
     */
    public function handle()
    {
        // 打印日志
        Log::info('开始推送apm评分排名脚本：' . Carbon::now()->toDateTimeString());

        $apps = array_column(Apps::query()->get()->toArray(), null, 'id');

        $config = $this->getAppApmMinDurations($apps);

        $list = $this->getList($config, $apps);

        $msg = $this->makeMsg($list['android'], $list['ios']);

        $img = $this->makeMsgByHtml($msg);

        $this->msgSend($img);

        // 打印日志
        Log::info('推送apm评分排名脚本结束：' . Carbon::now()->toDateTimeString());
    }

    /**
     * 获取APP的apm最小过滤时间
     *
     * @return array
     */
    private function getAppApmMinDurations($apps)
    {
        //获取每个app过滤的时间
        $config = ApmGlobalConfig::query()->get();
        $configs = [];
        foreach ($config as $item) {
            $configs[$item['developer_app_id']] = $item['min_duration'];
        }
        foreach (array_keys($apps) as $id) {
            if (!isset($configs[$id])) {
                $configs[$id] = 0;
            }
        }
        return $configs;
    }

    /**
     * 获取列表数据
     *
     * @return array
     */
    public function getList($configs, $apps): array
    {
        // 获取表明
        $mysql_apm_report_list_table = MysqlApmReportList::TABLE_NAME;
        $mysql_apm_device_list_table = MysqlApmDeviceList::TABLE_NAME;
        $performance_stat_data_table = PerformanceStatData::TABLE_NAME;
        $performance_score_data_table = PerformanceScoreData::TABLE_NAME;
        // 获取时间
        $endTime = Carbon::now()->subDay(1)->endOfDay()->toDateTimeString();
        $startTime = Carbon::parse($endTime)->subDays(7)->startOfDay()->toDateTimeString();
        // 系统版本
        $osType = ['android' => 1, 'ios' => 2];
        // 结果集
        foreach ($osType as $key => $item) {
            // 获取要推送的APP
            $pushAppList = $this->getPushAppList($startTime, $endTime, $item);
            // 打印日志
            Log::info("要推送的平台：{$key}，要推送的APP：" . json_encode($pushAppList));
            // 获取全部数据
            $allSubSql = StarRocksDB::toSql(
                DB::table($mysql_apm_report_list_table)
                    ->selectRaw("{$mysql_apm_report_list_table}.developer_app_id, {$performance_score_data_table}.all_score, {$performance_stat_data_table}.duration, {$mysql_apm_device_list_table}.os_type")
                    ->join($performance_stat_data_table, "{$performance_stat_data_table}.session_id", '=', "{$mysql_apm_report_list_table}.id")
                    ->join($mysql_apm_device_list_table, function ($join) use ($mysql_apm_device_list_table, $mysql_apm_report_list_table) {
                        $join->on("{$mysql_apm_report_list_table}.developer_app_id", '=', "{$mysql_apm_device_list_table}.developer_app_id")
                            ->on("{$mysql_apm_report_list_table}.dev_str", '=', "{$mysql_apm_device_list_table}.dev_str");
                    })
                    ->join($performance_score_data_table, "{$performance_score_data_table}.session_id", '=', "{$mysql_apm_report_list_table}.id")
                    ->whereBetween("{$mysql_apm_report_list_table}.created_at", [$startTime, $endTime])
                    ->whereIn("{$mysql_apm_report_list_table}.developer_app_id", $pushAppList)
                    ->where("{$mysql_apm_device_list_table}.is_simulator", 0)
            );
            $list[$key] = StarRocksDB::query(
                DB::table(Db::raw("({$allSubSql}) as t"))
                    ->selectRaw('developer_app_id, sum(all_score) as score, count(*) as num')
                    ->where(function ($query) use ($configs, $pushAppList) {
                        foreach ($configs as $id => $duration) {
                            if (!in_array($id, $pushAppList)) {
                                continue;
                            }
                            $query = $query->orWhereRaw("(developer_app_id = {$id} and duration > {$duration})");
                        }
                    })
                    ->when($item, function ($query, $value) {
                        return $query->where("os_type", $value);
                    })
                    ->groupBy('developer_app_id')
            )->get();
            // 打印日志
            Log::info("要推送的平台：{$key}，查询到的数据个数：" . count($list[$key]));
        }
        //返回数据
        return [
            'android' => $this->handleData($list['android'], $apps),
            'ios' => $this->handleData($list['ios'], $apps),
        ];
    }

    /**
     * 获取要推送的APP，在当前时间内，设备数大于500的，才推送
     *
     * @param string $startTime
     * @param string $endTime
     * @param int $osType
     * @return array
     */
    private function getPushAppList($startTime, $endTime, $osType)
    {
        // 获取表名
        $mysql_apm_report_list_table = MysqlApmReportList::TABLE_NAME;
        $mysql_apm_device_list_table = MysqlApmDeviceList::TABLE_NAME;
        // 子SQL查询
        $subSql = StarRocksDB::toSql(
            DB::table($mysql_apm_report_list_table)
                ->selectRaw("{$mysql_apm_report_list_table}.developer_app_id, count(distinct {$mysql_apm_report_list_table}.dev_str) as device_count")
                ->join($mysql_apm_device_list_table, function ($join) use ($mysql_apm_device_list_table, $mysql_apm_report_list_table) {
                    $join->on("{$mysql_apm_report_list_table}.developer_app_id", '=', "{$mysql_apm_device_list_table}.developer_app_id")
                        ->on("{$mysql_apm_report_list_table}.dev_str", '=', "{$mysql_apm_device_list_table}.dev_str");
                })
                ->whereBetween("{$mysql_apm_report_list_table}.created_at", [$startTime, $endTime])
                ->where("{$mysql_apm_device_list_table}.os_type", $osType)
                ->where("{$mysql_apm_device_list_table}.is_simulator", 0)
                ->groupBy("{$mysql_apm_report_list_table}.developer_app_id")
        );
        $res = StarRocksDB::query(
            DB::table(Db::raw("({$subSql}) as t"))
                ->selectRaw('developer_app_id')
                ->where('device_count', '>', 500)
        )->get();
        // 判断是否有数据
        if (empty($res)) {
            return [0];
        }
        // 返回应用列表
        return array_column($res, 'developer_app_id');
    }

    /**
     * 处理数据
     *
     * @param array $list
     * @param array $apps
     * @return array
     */
    private function handleData($list, $apps)
    {
        $newList = [];
        foreach ($list as $item) {
            if (!isset($apps[$item['developer_app_id']])) continue;
            $newList[] = [
                'num' => $item['num'] ? floatval(bcdiv($item['score'], $item['num'], 2)) : 0,
                'app_id' => intval($item['developer_app_id']),
                'app_name' => $apps[$item['developer_app_id']]['app_name'],
            ];
        }
        //排序
        array_multisort(array_column($newList, 'num'), SORT_DESC, $newList);
        //返回数据
        return $newList;
    }

    /**
     * 生成消息
     *
     * @param array $androidList
     * @param array $iosList
     * @return string
     */
    private function makeMsg($androidList, $iosList)
    {
        // Android评分
        $html = '<div style="padding: 0 10px;height: 40px; line-height: 40px; text-align: center; font-size: 20px; font-weight: bold; color: #fff; background: #1890ff">
        Android性能排行 (最近7天)</div><table border="1" style="border-collapse: collapse;width: 100%;text-align: center;">';
        if (!empty($androidList)) {
            $html .= '<tr style="background: #f4e3d9"><th>项目</th><th>分数</th></tr>';
            foreach ($androidList as $item) {
                $html .= "<tr><td>{$item['app_name']}</td><td>{$item['num']}</td></tr>";
            }
        } else {
            $html .= '<div style="margin-top: 5px; text-align: center; font-weight: bold">无数据</div>';
        }
        $html .= '</table>';
        // IOS评分
        $html .= '<div style="margin-top: 30px;padding: 0 10px;height: 40px; line-height: 40px; text-align: center; font-size: 20px; font-weight: bold; color: #fff; background: #1890ff">
        IOS性能排行 (最近7天)</div><table border="1" style="border-collapse: collapse;width: 100%;text-align: center;">';
        if (!empty($iosList)) {
            $html .= '<tr style="background: #f4e3d9"><th>项目</th><th>分数</th></tr>';
            foreach ($iosList as $item) {
                $html .= "<tr><td>{$item['app_name']}</td><td>{$item['num']}</td></tr>";
            }
        } else {
            $html .= '<div style="margin-top: 5px; text-align: center; font-weight: bold">无数据</div>';
        }

        $html .= '</table>';
        // 备注
        $html .= '<div style="margin-top: 30px; font-weight: bold"><span style="color: red">备注：</span>近七天设备超过500台才进入榜单</div>';
        return $html;
    }


    /**
     * 调用接口生成图片并发送到机器人
     *
     * @param $html
     * @throws GuzzleException
     */
    public function makeMsgByHtml($html)
    {
        $time = time();
        $params = [
            'type' => 'stream',
            'time' => $time,
            'sign' => md5(self::MSG_SIGN . $time),
            'htmlStr' => $html,
        ];
        try {
            $res = Curl::json(
                'https://ad-asset.shiyuegame.com/api/manager/asset/publish/image',
                $params,
                [
                    'headers' => [
                        'Accept-Language' => 'zh-CN,zh;q=0.9',
                        'Origin' => 'https://ad-asset.shiyuegame.com',
                        'Referer' => 'https://ad-asset.shiyuegame.com/api/manager/asset/publish/image',
                        'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36',
                        'Content-Type' => 'application/json',
                    ]
                ]
            );
            return $res;
        } catch (\Exception $error) {
            Log::error('生成图片失败，原因为：' . $error->getMessage());
        }
        return "";
    }

    /**
     * 推送机器人-图片类型
     *
     * @param string $msg
     */
    public function msgSend(string $msg)
    {
        $params = [
            'msgtype' => 'image',
            'image' => [
                'base64' => base64_encode($msg),
                'md5' => md5($msg),
            ]
        ];
        try {
            $res = Curl::json(
                'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cffbb57a-3a02-407b-b320-fdaecafd20a2',
                $params
            );
            Log::info('企业微信机器人信息推送，结果：' . json_encode($res));
        } catch (\Exception $error) {
            Log::error('企业微信机器人信息推送加载失败，原因为：' . $error->getMessage());
        }
    }
}
