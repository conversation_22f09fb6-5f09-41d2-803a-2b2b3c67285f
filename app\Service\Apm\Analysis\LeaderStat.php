<?php

/**
 * 获取负责人标签统计
 * @desc 获取负责人标签统计
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/04/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Analysis;

use App\Model\Apm\StarRocks\PerformanceTagScoreData;
use App\Service\Apm\Performance\ApmTrait;

class LeaderStat
{
    use ApmTrait;

    /**
     * 参数
     *
     * @var array
     */
    protected $params = [];

    /**
     * 构造函数
     *
     * @param array $params 初始化参数
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取统计数据
     *
     * @return array
     */
    public function getStat()
    {
        $currentData = $this->getData();
        $previousData = $this->getPreviousPeriodData();

        return $this->calculatePeriodOnPeriod($currentData, $previousData);
    }

    /**
     * 获取数据
     *
     * @return array
     */
    private function getData()
    {
        $selectRaw = $this->buildSelectExpression();

        $data = PerformanceTagScoreData::query() //查询标签评分表
            ->selectRaw($selectRaw)
            ->join($this->performance_stat_data_table, "{$this->performance_tag_score_data_table}.session_id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->mysql_apm_report_list_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_tag_score_data_table}.session_id") //关联报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { //关联设备表
                $join->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->join($this->performance_tag_stat_data_table, function ($join) { //关联标签统计表
                $join->on("{$this->performance_tag_stat_data_table}.session_id", '=', "{$this->performance_tag_score_data_table}.session_id") //通过session_id关联
                    ->on("{$this->performance_tag_stat_data_table}.tag", '=', "{$this->performance_tag_score_data_table}.tag"); //通过标签关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->params['start_date'], $this->params['end_date']]) //过滤掉不在时间范围内的数据
            ->where("{$this->performance_tag_stat_data_table}.tag", 'like', '%|%') //过滤掉不是多负责人标签的数据
            ->groupBy("{$this->performance_tag_stat_data_table}.tag")
            ->getFromSR();

        return $this->processTagData($data);
    }

    /**
     * 获取上一周期数据
     *
     * @return array
     */
    private function getPreviousPeriodData()
    {
        // 保存当前参数
        $currentParams = $this->params;

        // 计算上一周期的时间范围
        $previousPeriod = $this->calculatePreviousPeriod();

        // 设置参数为上一周期
        $this->params['start_date'] = $previousPeriod['start_date'];
        $this->params['end_date'] = $previousPeriod['end_date'];

        // 获取上一周期数据
        $previousData = $this->getData();

        // 恢复当前参数
        $this->params = $currentParams;

        return $previousData;
    }

    /**
     * 计算上一周期的时间范围
     *
     * @return array 包含上一周期开始和结束时间
     */
    private function calculatePreviousPeriod()
    {
        $startDate = new \DateTime($this->params['start_date']);
        $endDate = new \DateTime($this->params['end_date']);

        // 计算当前周期的时间跨度
        $interval = $startDate->diff($endDate);

        // 计算上一周期的结束时间（等于当前周期的开始时间）
        $previousEndDate = clone $startDate;

        // 计算上一周期的开始时间（根据当前周期的时间跨度往前推）
        $previousStartDate = clone $startDate;
        $previousStartDate->sub($interval);

        return [
            'start_date' => $previousStartDate->format('Y-m-d H:i:s'),
            'end_date' => $previousEndDate->format('Y-m-d H:i:s')
        ];
    }

    /**
     * 计算环比数据
     *
     * @param array $currentData 当前周期数据
     * @param array $previousData 上一周期数据
     * @return array 包含环比的数据
     */
    private function calculatePeriodOnPeriod($currentData, $previousData)
    {
        // 将上一周期数据转换为以负责人名称为键的关联数组，方便查找
        $previousDataMap = [];
        foreach ($previousData as $item) {
            $previousDataMap[$item['name']] = $item;
        }

        // 为当前数据添加环比字段
        foreach ($currentData as &$current) {
            $leaderName = $current['name'];

            // 检查上一周期是否有该负责人的数据
            if (isset($previousDataMap[$leaderName])) {
                $previous = $previousDataMap[$leaderName];

                // 计算各项指标的环比
                $current['score_pop'] = $this->calculateRatio($current['score'], $previous['score']);
                $current['substandard_num_pop'] = $this->calculateRatio($current['substandard_num'], $previous['substandard_num']);
                $current['num_pop'] = $this->calculateRatio($current['num'], $previous['num']);
                $current['memory_pop'] = $this->calculateRatio($current['memory'], $previous['memory']);
                $current['max_memory_pop'] = $this->calculateRatio($current['max_memory'], $previous['max_memory']);
                $current['big_jank_pop'] = $this->calculateRatio($current['big_jank'], $previous['big_jank']);
                $current['fps_pop'] = $this->calculateRatio($current['fps'], $previous['fps']);
                $current['stutter_pop'] = $this->calculateRatio($current['stutter'], $previous['stutter']);
            } else {
                // 如果上一周期没有数据，则环比为0
                $current['score_pop'] = '0';
                $current['substandard_num_pop'] = '0';
                $current['num_pop'] = '0';
                $current['memory_pop'] = '0';
                $current['max_memory_pop'] = '0';
                $current['big_jank_pop'] = '0';
                $current['fps_pop'] = '0';
                $current['stutter_pop'] = '0';
            }
        }

        return $currentData;
    }

    /**
     * 计算两个值的比率变化
     *
     * @param float $current 当前值
     * @param float $previous 上一周期值
     * @return float|null 比率变化百分比，如果上一周期值为0则返回0
     */
    private function calculateRatio($current, $previous)
    {
        if ($previous == 0) {
            return '0';
        }

        $change = ($current - $previous) / $previous * 100;
        return bcadd(round($change, 2), 0, 2);
    }

    /**
     * 构建查询表达式
     * 
     * @return string 查询表达式
     */
    private function buildSelectExpression()
    {
        return <<<EXPRESSION
        performance_tag_stat_data.tag, 
        round(sum(performance_tag_score_data.all_score) / count(1), 2) as score,
        count(CASE WHEN performance_tag_score_data.all_score < 80 THEN 1 END) as substandard_num,
        count(mysql_apm_report_list.id) as num,
        round(sum(performance_tag_stat_data.sum_used_memory) / sum(performance_tag_stat_data.num) / 1024 / 1024, 2) as memory,
        round(sum(performance_tag_stat_data.max_used_memory) / count(performance_tag_stat_data.session_id) / 1024 / 1024, 2) as max_memory,
        round(sum(performance_tag_stat_data.big_jank_count_10) / count(performance_tag_stat_data.session_id), 2) as big_jank,
        round(sum(performance_tag_stat_data.sum_fps) / sum(performance_tag_stat_data.num), 2) as fps,
        round(round(sum(performance_tag_stat_data.sum_jank_time) / sum(performance_tag_stat_data.sum_frame_times_time), 4) * 100, 2) as stutter
        EXPRESSION;
    }

    /**
     * 处理标签数据
     * 
     * @param array $data 原始数据
     * @return array 处理后的数据
     */
    private function processTagData($data)
    {
        $groupedData = $this->groupDataByLeader($data);
        return $this->calculateGroupStats($groupedData);
    }

    /**
     * 按负责人分组数据
     * 
     * @param array $data 原始数据
     * @return array 分组后的数据
     */
    private function groupDataByLeader($data)
    {
        $result = [];

        foreach ($data as $item) {
            // 分割标签
            $tagParts = explode('|', $item['tag']);

            // 只取前两部分，忽略第三部分(窗口名称)
            if (count($tagParts) >= 2) {
                $leaders = array_slice($tagParts, 0, 2);

                // 为每个负责人添加这条数据
                foreach ($leaders as $leader) {
                    if (!isset($result[$leader])) {
                        $result[$leader] = [];
                    }
                    // 将数据添加到结果中
                    $result[$leader][$item['tag']] = $item;
                }
            }
        }

        return $result;
    }

    /**
     * 计算每组的统计数据
     * 
     * @param array $groupedData 分组后的数据
     * @return array 统计结果
     */
    private function calculateGroupStats($groupedData)
    {
        $stats = [];

        foreach ($groupedData as $leader => $items) {
            $count = count($items);

            if ($count == 0) {
                continue;
            }

            $stats[] = $this->calculateLeaderStats($leader, $items, $count);
        }

        // 按count字段从大到小排序
        usort($stats, function ($a, $b) {
            return $b['count'] - $a['count'];
        });

        return $stats;
    }

    /**
     * 计算单个负责人的统计数据
     * 
     * @param string $leader 负责人名称
     * @param array $items 数据项
     * @param int $count 数据项数量
     * @return array 统计结果
     */
    private function calculateLeaderStats($leader, $items, $count)
    {
        // 初始化统计变量
        $sums = $this->initSumsArray();

        // 累加各项数据
        foreach ($items as $item) {
            $this->addItemToSums($sums, $item);
        }

        // 计算均值和总和
        return [
            'name' => $leader,
            'count' => $count,
            'score' => bcadd(round($sums['score'] / $count, 2), 0, 2),
            'substandard_num' => $sums['substandard_num'],
            'num' => $sums['num'],
            'memory' => bcadd(round($sums['memory'] / $count, 2), 0, 2),
            'max_memory' => bcadd(round($sums['max_memory'] / $count, 2), 0, 2),
            'big_jank' => bcadd(round($sums['big_jank'] / $count, 2), 0, 2),
            'fps' => bcadd(round($sums['fps'] / $count, 2), 0, 2),
            'stutter' => bcadd(round($sums['stutter'] / $count, 2), 0, 2),
        ];
    }

    /**
     * 初始化统计数组
     * 
     * @return array 初始化的数组
     */
    private function initSumsArray()
    {
        return [
            'score' => 0,
            'substandard_num' => 0,
            'num' => 0,
            'memory' => 0,
            'max_memory' => 0,
            'big_jank' => 0,
            'fps' => 0,
            'stutter' => 0
        ];
    }

    /**
     * 将单项数据添加到汇总数组
     * 
     * @param array &$sums 汇总数组（引用传递）
     * @param array $item 单项数据
     */
    private function addItemToSums(&$sums, $item)
    {
        $sums['score'] += $item['score'];
        $sums['substandard_num'] += $item['substandard_num'];
        $sums['num'] += $item['num'];
        $sums['memory'] += $item['memory'];
        $sums['max_memory'] += $item['max_memory'];
        $sums['big_jank'] += $item['big_jank'];
        $sums['fps'] += $item['fps'];
        $sums['stutter'] += $item['stutter'];
    }
}
