<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAbReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('ab_report', function (Blueprint $table) {
            $table->bigIncrements('report_id')->comment('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->string('report_url')->default('')->comment('报表存储路径');
            $table->unsignedSmallInteger('all_ab_num')->default(0)->comment('总AB数');
            $table->unsignedSmallInteger('dependence_ab_num')->default(0)->comment('有外部依赖的AB数');
            $table->unsignedSmallInteger('redundancy_ab_num')->default(0)->comment('含冗余资源的AB数');
            $table->unsignedSmallInteger('all_resource_num')->default(0)->comment('总资源数');
            $table->unsignedSmallInteger('redundancy_resource_num')->default(0)->comment('冗余资源数');
            $table->string('remark', 255)->nullable()->comment('备注');
            $table->timestamps();
            $table->index('developer_app_id','idx_developer_app_id','BTREE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('ab_report');
    }
}
