<?php

/**
 * 线下性能检测项目管理模型类
 * @desc 线下性能检测项目管理模型类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/04/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\Perf;

use App\Model\BaseModel;
use App\Model\ModelTrait;

class Project extends BaseModel
{
    use ModelTrait;

    protected $table = 'perf_project';

    protected $primaryKey = 'id';

    protected $fillable = [
        'developer_app_id',
        'title',
        'package_name',
        'operator',
        'staff_id',
    ];

    protected $casts = [
        'package_name' => 'json',
    ];
}
