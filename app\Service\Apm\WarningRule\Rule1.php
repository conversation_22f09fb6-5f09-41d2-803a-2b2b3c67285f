<?php

namespace App\Service\Apm\WarningRule;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use Carbon\Carbon;

class Rule1 extends BaseRule
{
    public function getData(): float
    {
        //获取报告数量、设备数量、平均分、低于标准分数量
        $selectRaw = <<<COLUMNS
count({$this->performance_score_data_table}.session_id) as score_num,
sum(case when {$this->performance_score_data_table}.all_score < {$this->standardScore} then 1 else 0 end) as low_score_num
COLUMNS;

        $data = PerformanceScoreData::query()
            ->join($this->mysql_apm_report_list_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")  //连接报告表
            ->join($this->mysql_apm_device_list_table, function ($join) {    //连接设备表
                $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str");
            })
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id")   //连接性能统计表
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())  //过滤掉小于最小耗时的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [Carbon::parse($this->params['end_time'])->subDays()->toDateTimeString(), $this->params['end_time']]) //获取最近一天的报告数据（因为如果重跑评分，那么评分的时间会刷新，所以只取最近一天的报告）
            ->whereBetween("{$this->performance_score_data_table}.date", [$this->params['start_time'], $this->params['end_time']]) //过滤掉不在该评分时间范围内的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //只获取当前应用的数据
            ->where("{$this->mysql_apm_device_list_table}.is_simulator", 0) //过滤掉模拟器数据
            ->when(!empty($this->params['os_type']), function ($query) {  //过滤掉不是当前平台的数据
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(!empty($this->params['versions']), function ($query) {  //过滤掉不是当前版本的数据
                $query->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['versions']);
            })
            ->selectRaw($selectRaw)
            ->firstFromSR();

        // 如果没有数据，直接返回0
        if ($data['score_num'] == 0) {
            $this->result = 0;
        } else {
            // 计算低于指定分数的占比
            $this->result = bcmul(bcdiv($data['low_score_num'], $data['score_num'], 4), 100, 2);
        }
        // 返回结果
        return  $this->result;
    }

    public function getMessage(): string
    {
        //小时
        $hour = round((strtotime($this->params['end_time']) - strtotime($this->params['start_time'])) / 3600);
        $time = "最近{$hour}小时";
        //如果大于24小时，显示天数
        if ($hour >= 24) {
            $time = "最近" . round($hour / 24) . "天";
        }
        //告警阈值, 取整
        $value = round($this->params['value'], 2);
        //返回告警信息
        return "{$time}，性能评分不达标率是：{$this->result}%（告警阈值：大于{$value}%）";
    }
}
