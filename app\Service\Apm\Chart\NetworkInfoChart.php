<?php

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class NetworkInfoChart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'down_traffic',
                'up_traffic',
                'network_delay',
                'tags_info',
                'app_state',
                'perf_data_ts',
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $list[$item['perf_data_ts']]['networkInfo'] = [ //按照时间戳分组
                'downTraffic' => $item['down_traffic'], //下载流量
                'upTraffic' => $item['up_traffic'], //上传流量
//                'networkDelay' => $item['network_delay'],   //网络延迟
            ];
            //（新）网络延迟信息networkDelayInfo   沿用原来network_delay字段
            $list[$item['perf_data_ts']]['networkDelayInfo'] = [ //按照时间戳分组
                'networkLatency' => $item['network_delay'],   //网络延迟
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }

        return array_values($list);
    }
}
