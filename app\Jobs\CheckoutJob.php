<?php

namespace App\Jobs;

use App\Model\Checkout\Checkout;
use App\Service\SshService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use websocketService\WebsocketClient;

class CheckoutJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $checkoutId;
    public $timeout = 1800;

    /**
     * Create a new job instance.
     * @param int $checkoutId
     * @return void
     */
    public function __construct(int $checkoutId)
    {
        $this->checkoutId = $checkoutId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('begin');
        $checkout = Checkout::find($this->checkoutId);
        $sshService = new SshService(config('ssh.packer_host'), config('ssh.packer_port'), config('ssh.packer_user'),  config('ssh.packer_pwd'));
        //调用Python脚本
        $output = null;
        if ($checkout->platform_type == Checkout::PLATFORM_IOS) {
            $command = sprintf('inv package.ipa.check -p %s', $checkout->url);
        } else {
            $command = sprintf('inv package.check-apk.check-apk-url -a %s -c %s', $checkout->url, Checkout::PLATFORM_TYPE[$checkout->platform_type]);
        }
        $sshService->run("cd /Users/<USER>/awesome-tools;git pull;source venv/bin/activate;" . $command, $output);
        //转换为数组
        $outputArr = explode('%%%', $output);
        unset($output);
        //截取真正输出检测字符串
        $resultStr = $outputArr[1] ?? null;
        unset($outputArr);
        $result = json_decode($resultStr, true);
        $code = $result['code'] ?? 500;
        unset($result);
        //更新状态和检测报告
        $checkout->status = $code == 0 ? Checkout::STATUS_FINISHED : Checkout::STATUS_FAIL;
        $checkout->checkout_report = $resultStr;
        $checkout->save();
        //推送swoole
        $swooleClient = new WebsocketClient(config('app.env') == 'local');
        $swooleClient->text(json_encode(['type' => 2, 'checkout_id' => $checkout->checkout_id]));
        $swooleClient->close();
        \Log::info('end');
    }
}
