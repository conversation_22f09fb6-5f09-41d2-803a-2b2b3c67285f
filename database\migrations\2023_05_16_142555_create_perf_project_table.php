<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePerfProjectTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('perf_project', function (Blueprint $table) {
            $table->increments('id')->comment('ID');
            $table->string('title', 255)->default('')->comment('项目名称');
            $table->json('package_name')->comment('包名');
            $table->timestamps();

        });
        \DB::connection('tool')->statement("ALTER TABLE `perf_project` comment 'apm-线下性能项目表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('perf_project');
    }
}
