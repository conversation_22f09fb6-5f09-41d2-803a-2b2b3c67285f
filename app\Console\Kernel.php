<?php

/**
 * 定时任务
 * @desc 定时任务
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/02/19
 */

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //每小时执行一次
        $schedule->command('check:apm:warning')->hourly()->appendOutputTo(storage_path('logs/check_apm_warning.log'));
        // 每天10点执行一次
        $schedule->command('push:apm:score:rank')->dailyAt('10:00')->appendOutputTo(storage_path('logs/push_apm_score_rank.log'));
        $schedule->command("apm:leader:webhook 1")->withoutOverlapping()->dailyAt('10:00')->runInBackground();
        // 每天3点执行一次
        $schedule->command('delete:export:file')->dailyAt('03:00')->appendOutputTo(storage_path('logs/delete_export_file.log'));
        // $schedule->command('inspire')
        //          ->hourly();
        //只在正式服执行的脚本
        if (config('app.env') === 'production') {
            //每天10点，执行
            $schedule->command("monitor:data")->withoutOverlapping()->dailyAt('10:02')->runInBackground();
            // 每天4点执行
            $schedule->command("delete:pref:expired:data")->withoutOverlapping()->dailyAt('04:00')->runInBackground();
            // 每个小时执行
            $schedule->command("cahx:warning")->withoutOverlapping()->hourly()->runInBackground();
            // 检查同步脚本
            $schedule->command("check:apm:sync")->withoutOverlapping()->hourly()->runInBackground();
        }
        // 每周一十点执行
        $schedule->command("apm:leader:webhook 2")->withoutOverlapping()->weeklyOn(1, '10:00')->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
