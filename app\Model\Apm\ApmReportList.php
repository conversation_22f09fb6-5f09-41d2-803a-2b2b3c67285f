<?php

/**
 * 线上性能玩家数据列表
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ApmReportList extends Model
{
    use ModelTrait;

    public $connection = "apm";

    protected $table = 'apm_report_list';

    protected $primaryKey = 'id';

    protected $guarded = [];

    /**
     * 获取该设备信息
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo('App\Model\Apm\ApmDeviceList', 'dev_str', 'dev_str');
    }
}

