<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApmReportScoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('apm_report_score', function (Blueprint $table) {
            $table->bigInteger('report_id')->default(0)->comment('报告ID');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->string('dev_str')->default('')->comment('设备标识');
            $table->double('cpu', 10)->default(0)->comment('CPU分数');
            $table->double('memory', 10)->default(0)->comment('内存分数');
            $table->double('fps', 10)->default(0)->comment('FPS分数');
            $table->double('smoothness', 10)->default(0)->comment('流畅度分数');
            $table->timestamps();
            $table->primary('report_id');
            $table->index('developer_app_id');
            $table->index('created_at');
        });
        \DB::connection('apm')->statement("ALTER TABLE `apm_report_score` comment 'apm-报告评分表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('apm_report_score');
    }
}
