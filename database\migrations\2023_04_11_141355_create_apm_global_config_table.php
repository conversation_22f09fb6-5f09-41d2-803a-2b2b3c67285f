<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApmGlobalConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->create('apm_global_config', function (Blueprint $table) {
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->text('config')->comment('配置信息');
            $table->timestamps();

            $table->primary('developer_app_id');
        });
        \DB::connection('apm')->statement("ALTER TABLE `apm_global_config` comment 'apm-全局配置表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->dropIfExists('apm_global_config');
    }
}
