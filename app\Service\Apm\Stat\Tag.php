<?php

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceTagScoreData;

class Tag extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        return $this->getScore();
    }

    /**
     * 获取分数
     *
     * @return array
     */
    protected function getScore(): array
    {
        //获取标签、平均分
        $selectRaw = <<<COLUMNS
{$this->performance_tag_score_data_table}.tag as tag,
ROUND(sum({$this->performance_tag_score_data_table}.all_score) / count({$this->performance_tag_score_data_table}.session_id), 2) as score
COLUMNS;

        return $this->getCommonBuilder(new PerformanceTagScoreData)
            ->selectRaw($selectRaw)
            ->groupBy("{$this->performance_tag_score_data_table}.tag")  //按标签分组
            ->orderBy("score")  //按分数升序排列
            ->limit(5)  //取前5个
            ->getFromSR();
    }
}
