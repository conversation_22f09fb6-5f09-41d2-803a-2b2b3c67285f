<?php

/**
 * 性能详情
 * @desc 性能详情
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/08/12
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\ApmReportList;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\PerformanceExtendData;
use App\Service\ModelLevelService;
use App\Service\StarRocksService;
use Illuminate\Database\Query\JoinClause;

class ApmInfo
{
    use ApmTrait;

    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 报告信息
     *
     * @var ApmReportList
     */
    protected $report;

    /**
     * 数仓连接对象
     *
     * @var StarRocksService
     */
    protected $starRocks;

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
        $this->starRocks = new StarRocksService();
    }

    /**
     * 获取信息
     *
     * @return array
     */
    public function get(): array
    {
        //获取报告
        $this->getReport();
        //返回报告信息
        return $this->getInfo();
    }

    /**
     * 获取报告信息
     *
     * @return void
     */
    protected function getReport()
    {
        $res = MysqlApmReportList::query()
            ->selectRaw("{$this->mysql_apm_device_list_table}.*,{$this->mysql_apm_report_list_table}.*,{$this->performance_stat_data_table}.*,{$this->performance_score_data_table}.*")
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->join($this->performance_score_data_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->join($this->mysql_apm_device_list_table, function (JoinClause $join) {
                $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str");
            })
            ->where("{$this->mysql_apm_report_list_table}.id", $this->params['report_id'])
            ->firstFromSR();

        if (!empty($res)) {
            $this->report = $res;
        }
    }

    /**
     * 获取初始化信息
     *
     * @return array
     */
    protected function getInitInfo(): array
    {
        return [
            'avg_fps' => 0,
            'stutter' => 0,
            'jank_count' => 0,
            'big_jank_count' => 0,
            'max_used_memory' => 0,
            'avg_used_memory' => 0,
            'avg_temp' => 0,
            'avg_gpu_temp' => 0,
            'max_gpu_temp' => 0,
            'avg_cpu_usage' => 0,
            'dev_str' => '',
            'dev_brand' => '',
            'dev_model' => '',
            'game_version_code' => '',
            'app_version_name' => '',
            'is_simulator' => '',
            'channel' => '',
            'package_name' => '',
            'dev_cpu_count' => '',
            'dev_cpu_model' => '',
            'opengl_es_version' => '',
            'os_version_code' => '',
            'resolution_ratio' => '',
            'dev_memory' => '',
            'operator' => '',
            'created_at' => '',
            'duration' => 0,
            'architecture' => '',
            'dev_gpu_model' => '',
            'area' => '',
            'network_type' => 0,
            'user_list' => [],
            'sdk_ver' => '',
            'inner_version' => '',
            'quality' => '',
            'down_traffic' => 0,
            'up_traffic' => 0,
            'network_delay' => 0,
            'fps_module' => [
                'max_fps_memory' => 0,
                'min_fps_memory' => 0,
                'avg_fps' => 0,
                'jank_count' => 0,
                'big_jank_count' => 0,
                'low_ratio' => 0,
                'stutter' => 0,
            ],
            'memory_module' => [
                'avg_used_memory' => 0,
                'max_used_memory' => 0,
            ],
            'cpu_module' => [
                'avg_used_cpu' => 0,
                'max_used_cpu' => 0,
                'avg_total_used_cpu' => 0,
                'max_total_used_cpu' => 0,
            ],
            'battery_module' => [
                'max_electricity' => 0,
                'avg_electricity' => 0,
                'max_power_dissipation' => 0,
                'avg_power_dissipation' => 0,
            ],
            'temp_module' => [
                'max_cpu_temp' => 0,
                'avg_cpu_temp' => 0,
                'max_battery_temp' => 0,
                'avg_battery_temp' => 0,
            ],
            'extend_data' => [],
            'dev_gpu_ram_size' => '',
            'dev_gpu_api' => '',
            'avg_lua_memory' => '0',
            'avg_mono_used_size' => '0',
            'fps_power' => '0',
        ];
    }

    /**
     * 获取扩展信息
     *
     * @return array|mixed
     */
    private function getExtendData()
    {
        $result = PerformanceExtendData::query()
            ->select('extend_data')
            ->where("session_id", $this->params['report_id'])
            ->firstFromSR();
        if ($result) {
            return json_decode($result['extend_data'], true);
        }
        return [];
    }

    /**
     * 获取信息
     *
     * @return array
     */
    protected function getInfo(): array
    {
        $data = $this->getInitInfo();
        if (empty($this->report)) {
            return $data;
        }
        //处理数据
        $data['disk_type'] = $this->report['disk_type'] ?? '';
        $data['disk_model'] = $this->report['disk_model'] ?? '';
        $data['dev_str'] = $this->report['dev_str'];
        $data['dev_brand'] = $this->report['dev_brand'];
        $data['dev_model'] = ApmModelMap::getValue($this->report['dev_model']);
        $data['game_version_code'] = $this->report['game_version_code'];
        $data['app_version_name'] = $this->report['app_version_name'];
        $data['is_simulator'] = $this->report['is_simulator'];
        $data['channel'] = $this->report['channel'];
        $data['package_name'] = $this->report['package_name'];
        $data['dev_cpu_count'] = $this->report['dev_cpu_count'];
        $data['dev_cpu_model'] = $this->report['dev_cpu_model'];
        $data['opengl_es_version'] = $this->report['opengl_es_version'];
        $data['os_version_code'] = $this->report['os_version_code'];
        $data['resolution_ratio'] = $this->report['screen_resolution'];
        $data['dev_memory'] = $this->report['app_total_memory'];
        $data['operator'] = $this->report['ip_operator'];
        $data['created_at'] = $this->report['created_at'];
        $data['duration'] = $this->report['duration'];
        $data['architecture'] = $this->report['abi'];
        $data['dev_gpu_model'] = $this->report['dev_gpu_model'];
        $data['network_type'] = $this->report['network_type'];
        $data['dev_gpu_ram_size'] = $this->report['dev_gpu_ram_size'];
        $data['dev_gpu_api'] = $this->report['dev_gpu_api'];
        $data['area'] = "{$this->report['ip_country']}-{$this->report['ip_province']}-{$this->report['ip_city']}";
        $num = $this->report['num'] == 0 ? 1 : $this->report['num'];
        $data['avg_fps'] = bcdiv(sprintf('%f', $this->report['sum_fps']), $num, 2);
        $data['stutter'] = $this->report['sum_frame_times_time'] == 0 ? 0 : bcmul(bcdiv(sprintf('%f', $this->report['sum_jank_time']), sprintf('%f', $this->report['sum_frame_times_time']), 4), 100, 2);
        $data['jank_count'] = bcadd(sprintf('%f', $this->report['jank_count_10']), 0, 2);
        $data['big_jank_count'] = bcadd(sprintf('%f', $this->report['big_jank_count_10']), 0, 2);
        $data['down_traffic'] = bcadd(sprintf('%f', $this->report['down_traffic_10']), 0, 2);
        $data['up_traffic'] = bcadd(sprintf('%f', $this->report['up_traffic_10']), 0, 2);
        $data['network_delay'] = bcdiv(sprintf('%f', $this->report['sum_network_delay']), $num, 2);
        $data['max_used_memory'] = bcadd(sprintf('%f', $this->report['max_used_memory']), 0, 2);
        $data['avg_used_memory'] = bcdiv(sprintf('%f', $this->report['sum_used_memory']), $num, 2);
        $data['avg_lua_memory'] = bcdiv(sprintf('%f', $this->report['sum_lua_memory']), $num, 2);
        $data['avg_mono_used_size'] = bcdiv(sprintf('%f', $this->report['sum_mono_used_size']), $num, 2);
        if ($this->report['sum_fps'] > 0) {
            $data['fps_power'] = bcadd(round($this->report['sum_battery_power'] / $this->report['sum_fps'], 2), 0, 2);
        }
        $data['avg_temp'] = bcdiv(sprintf('%f', $this->report['sum_battery_temp']), $num, 2);
        $data['avg_gpu_temp'] = min(bcdiv(sprintf('%f', $this->report['sum_gpu_temp']), $num, 2), '100');
        $data['max_gpu_temp'] = min($this->report['max_gpu_temp'], '100');
        $data['avg_power'] = bcdiv(sprintf('%f', $this->report['sum_battery_power']), $num, 2);
        $data['avg_power_hour'] = bcadd(sprintf('%f', $this->report['battery_power_hour']), 0, 2);
        $data['avg_cpu_usage'] = bcdiv(sprintf('%f', $this->report['sum_cpu_usage']), $num, 2);
        $data['avg_total_cpu_usage'] = bcdiv(sprintf('%f', $this->report['sum_cpu_total_usage']), $num, 2);
        $allScore = $this->report['all_score'];
        $data['all_score'] = $allScore == null ? 0 : $allScore; //总评分
        $levelService = ModelLevelService::getInstance();
        if (empty($this->report['device_tier'])) {
            $data['model_level'] = $levelService->getLevelText($levelService->getLevel($this->report['dev_model'], $data['dev_cpu_model'], $data['is_simulator'])); //机型等级
        } else {
            $data['model_level'] = $levelService->getLevelText($this->report['device_tier']); //机型等级
        }
        $data['user_list'] = json_decode($this->report['user_id'], true); //用户列表
        $data['sdk_ver'] = $this->report['sdk_ver'] ?? ''; //sdk版本
        $data['inner_version'] = $this->report['inner_version'] ?? ''; //资源版本
        $data['quality'] = $this->report['quality'] ?? ''; //画质
        $data['score'] = $allScore == null ? null : [
            'max_memory' => $this->report['memory'] ?? 0, //内存峰值评分
            'fps' => $this->report['fps'] ?? 0, //fps评分
            'smoothness' => $this->report['smoothness'] ?? 0, //流畅度
            'avg_memory' => $this->report['avg_memory'] ?? 0, //内存均值评分
            'big_jank' => $this->report['big_jank'] ?? 0, //大卡顿评分
        ];
        //fps模块
        $data['fps_module'] = [
            'max_fps_memory' => bcadd(sprintf('%f', $this->report['max_fps']), 0, 2),
            'min_fps_memory' => bcadd(sprintf('%f', $this->report['min_fps']), 0, 2),
            'avg_fps' => $data['avg_fps'],
            'jank_count' => $data['jank_count'],
            'big_jank_count' => $data['big_jank_count'],
            'low_ratio' => bcmul(bcdiv(sprintf('%f', $this->report['sum_min_fps_25']), $num, 4), 100, 2),
            'low_ratio_55' => bcmul(bcdiv(sprintf('%f', $this->report['sum_min_fps_55']), $num, 4), 100, 2),
            'stutter' => $data['stutter'],
        ];
        //内存模块
        $data['memory_module'] = [
            'avg_used_memory' => $data['avg_used_memory'],
            'max_used_memory' => $data['max_used_memory'],
            'avg_virtual_memory' => bcdiv(sprintf('%f', $this->report['sum_virtual_memory']), $num, 2),
            'max_virtual_memory' => bcadd(sprintf('%f', $this->report['max_virtual_memory']), 0, 2),
        ];
        //cpu模块
        $data['cpu_module'] = [
            'avg_used_cpu' => $data['avg_cpu_usage'],
            'max_used_cpu' => bcadd(sprintf('%f', $this->report['max_cpu_usage']), 0, 2),
            'avg_total_cpu_usage' => $data['avg_total_cpu_usage'],
            'max_total_used_cpu' => bcadd(sprintf('%f', $this->report['max_cpu_total_usage']), 0, 2),
        ];
        //电量模块
        $data['battery_module'] = [
            'max_electricity' => bcadd(sprintf('%f', $this->report['max_battery_current']), 0, 2),
            'avg_electricity' => bcdiv(sprintf('%f', $this->report['sum_battery_current']), $num, 2),
            'max_power_dissipation' => bcadd(sprintf('%f', $this->report['max_battery_power']), 0, 2),
            'avg_power_dissipation' => bcdiv(sprintf('%f', $this->report['sum_battery_power']), $num, 2),
        ];
        //温度模块
        $data['temp_module'] = [
            'max_cpu_temp' => bcadd(sprintf('%f', $this->report['max_cpu_temp']), 0, 2),
            'avg_cpu_temp' => bcdiv(sprintf('%f', $this->report['sum_cpu_temp']), $num, 2),
            'max_battery_temp' => bcadd(sprintf('%f', $this->report['max_battery_temp']), 0, 2),
            'avg_battery_temp' => bcdiv(sprintf('%f', $this->report['sum_battery_temp']), $num, 2),
        ];
        // 拓展数据
        $data['extend_data'] = $this->getExtendData();
        //返回
        return $data;
    }
}
