<?php
/**
 * service.php
 *
 * User: Dican
 * Date: 2022/7/14
 * Email: <<EMAIL>>
 */


use websocketService\WebsocketServer;

try {
    require __DIR__ . '/../vendor/autoload.php';
    $cfg = require __DIR__ . '/config.php';
    $server = new WebsocketServer($cfg['debug']);
    $server->serverStart();
} catch (\Exception $exception) {
    echo '工具平台webSocket服务器的启动失败,原因:' . $exception->getMessage();
}
