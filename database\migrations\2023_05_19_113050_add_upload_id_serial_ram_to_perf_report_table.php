<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUploadIdSerialRamToPerfReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       Schema::connection('tool')->table('perf_report', function (Blueprint $table) {
            $table->string('upload_id', 255)->default('')->comment('上传ID');
            $table->string('serial', 255)->default('')->comment('设备的唯一标识');
            $table->string('ram', 255)->default('')->comment('设备的最大物理内存RAM大小');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
       Schema::connection('tool')->table('perf_report', function (Blueprint $table) {
            $table->dropColumn('upload_id');
            $table->dropColumn('serial');
            $table->dropColumn('ram');
        });
    }
}
