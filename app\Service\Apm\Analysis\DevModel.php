<?php

/**
 * 机型和品牌分析
 * @desc 机型和品牌分析
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/08/01
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Analysis;

use App\Model\Apm\ApmDeviceList;
use App\Model\Apm\StarRocks\BaseBuilder;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apm\StarRocks\StarRocksDB;
use App\Service\Apm\Performance\ApmBase;
use App\Service\Apm\Performance\ApmModelMap;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DevModel extends BaseAnalysis
{
    /**
     * 获取通用的构造器
     *
     * @return BaseBuilder|Builder|mixed
     */
    protected function getCommonBuilder()
    {
        return MysqlApmReportList::query() //查询报告表
            ->join($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->performance_score_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_score_data_table}.session_id") //关联报告评分表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) //过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['game_version_code']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            });
    }

    /**
     * 品牌概况，根据传入的时间筛选排版数据，区分达标和不达标，返回列表
     *
     * @return array
     */
    public function brandSummary(): array
    {
        // 获取数据
        $list = $this->getCommonBuilder()
            ->selectRaw("{$this->mysql_apm_device_list_table}.dev_brand, round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score, count({$this->mysql_apm_report_list_table}.id) as num") //查询品牌、平均分、数量
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_brand") //按品牌分组
            ->orderBy('num', 'desc') //按报告数排序
            ->getFromSR();
        // 根据分数划分达标和不达标
        $substandard = $standard = [];
        foreach ($list as $item) {
            if ($item['score'] < ApmBase::STANDARD_SCORE) {
                $substandard[] = $item;
            } else {
                $standard[] = $item;
            }
        }
        // 返回数据
        return [
            'substandard' => $substandard,
            'standard' => $standard,
            'standard_score' => ApmBase::STANDARD_SCORE,
        ];
    }

    /**
     * 型号概况，根据传入的时间筛选型号数据，区分达标和不达标，返回列表
     *
     * @return array
     */
    public function modelSummary(): array
    {
        // 获取数据
        $list = $this->handleListData($this->getCommonBuilder()
            ->selectRaw("{$this->mysql_apm_device_list_table}.dev_model, round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score, count({$this->mysql_apm_report_list_table}.id) as num") //查询型号、平均分、数量
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model") //按型号分组
            ->orderBy('num', 'desc') //按报告数排序
            ->getFromSR());
        // 根据分数划分达标和不达标
        $substandard = $standard = [];
        foreach ($list as $item) {
            if ($item['score'] < ApmBase::STANDARD_SCORE) {
                $substandard[] = $item;
            } else {
                $standard[] = $item;
            }
        }
        // 返回数据
        return [
            'substandard' => $substandard,
            'standard' => $standard,
            'standard_score' => ApmBase::STANDARD_SCORE,
        ];
    }

    /**
     * 根据传入的时间筛选型号数据，型号前10个，根据报告数排序
     *
     * @return array
     */
    public function top(): array
    {
        return $this->handleListData($this->getCommonBuilder()
            ->selectRaw("{$this->mysql_apm_device_list_table}.dev_model, round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score, count({$this->mysql_apm_report_list_table}.id) as num") //查询型号、平均分、数量
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model") //按型号分组
            ->orderBy('num', 'desc') //按报告数倒序
            ->limit(10) //取前10个
            ->getFromSR());
    }

    /**
     * 获取机型数据列表，评分、fps、内存、卡顿率、cpu的列表
     *
     * @return array
     */
    public function list(): array
    {
        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_device_list_table}.dev_model,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
count({$this->mysql_apm_report_list_table}.id) as num,
round(sum({$this->performance_stat_data_table}.sum_cpu_usage) / sum({$this->performance_stat_data_table}.num), 2) as cpu,
round(sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num), 2) as memory,
round(sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_score_data_table}.session_id), 2) as max_memory,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_score_data_table}.session_id), 2) as big_jank,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter
EXPRESSION;

        $subSql = StarRocksDB::toSql(DB::table($this->mysql_apm_report_list_table) //查询报告列表
            ->join($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->performance_score_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_score_data_table}.session_id") //关联报告评分表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备列表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) ///过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['game_version_code']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model") //按型号分组
            ->select("{$this->mysql_apm_device_list_table}.dev_model")); //查询型号

        //查询总数
        $totalRes = StarRocksDB::query(DB::table(DB::raw("({$subSql}) as t"))->selectRaw('count(*) as total'))->first();

        $builder = $this->getCommonBuilder()
            ->selectRaw($selectRaw)
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model"); //按型号分组
        // 判断筛选条件是否为空
        if (!empty($this->params['filter_top'])) {
            // 判断筛选条件
            if ($this->params['filter_top'] == 'top') {
                $builder->orderBy('score', 'desc');
            } else {
                $builder->orderBy('score', 'asc');
            }
            $builder->limit(10);
        } else {
            $builder->offset($this->getPageNum())->limit($this->perPage)->orderBy($this->sortField, $this->sortType);
        }
        // 返回处理后的数据
        return [
            'total' => $totalRes['total'] ?? 0,
            'list' => $this->handleListData($builder->getFromSR()),
        ];
    }

    /**
     * 获取机型数据列表，评分、fps、内存、卡顿率、cpu的列表
     *
     * @return string
     * @throws \Exception
     */
    public function export(): string
    {
        $selectRaw = <<<EXPRESSION
any_value({$this->mysql_apm_device_list_table}.dev_brand) as dev_brand,
{$this->mysql_apm_device_list_table}.dev_model,
any_value({$this->mysql_apm_device_list_table}.device_tier) as device_tier,
any_value({$this->mysql_apm_device_list_table}.os_type) as os_type,
any_value({$this->mysql_apm_device_list_table}.is_simulator) as is_simulator,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
count(distinct {$this->mysql_apm_device_list_table}.dev_str) as num,
round(sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num), 2) as memory,
round(sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_score_data_table}.session_id), 2) as max_memory,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_score_data_table}.session_id), 2) as big_jank,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter
EXPRESSION;

        $list = MysqlApmReportList::query()
            ->join($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id") //关联报告统计表
            ->join($this->performance_score_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_score_data_table}.session_id") //关联报告评分表
            ->join($this->mysql_apm_device_list_table, function ($json) { //关联设备列表
                $json->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str") //通过设备标识关联
                    ->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id"); //通过效能后台ID关联
            })
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) ///过滤掉不满足最小时长的数据
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) //过滤掉不是当前效能后台ID的数据
            ->where("memory", '>', 0) //只获取内存值大于0的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) //过滤掉不在时间范围内的数据
            ->when(isset($this->params['os_type']), function ($json) { //如果有传平台值，过滤掉不是当前平台的数据
                $json->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(!empty($this->params['game_version_code']), function ($query) { //如果有传版本值，过滤掉不在版本范围内的数据
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model") //按型号分组
            ->selectRaw($selectRaw)
            ->orderByDesc('num')
            ->getFromSR(); //查询型号
        //返回
        return config('app.url') . '/' . $this->handleExportData($list);
    }

    /**
     * 处理导出数据
     *
     * @param array $data
     * @return string
     * @throws \Exception
     */
    private function handleExportData(array $data): string
    {
        if (empty($data)) {
            throw new \Exception('没有数据可以导出');
        }

        $filename = 'export/model_top_list_' . Str::random() . '.csv';
        $filepath = public_path($filename); // 将文件保存到 public 目录

        $file = fopen($filepath, 'w');

        fputcsv($file, ['品牌', '机型', '平台', '是否模拟器', '机型档位', '设备总数', '总设备占比', '评分', 'FPS均值（帧/秒）', 'Stutter卡顿率（%）', 'BigJank（10min/次）', '内存峰值（MB）', '内存均值（MB）']); // 写入标题

        $tier = [1 => '高档', 2 => '中档', 3 => '低档'];
        $devSum = array_sum(array_column($data, 'num'));
        foreach ($data as $row) {
            fputcsv($file, [
                $row['dev_brand'], // 品牌
                ApmModelMap::getValue($row['dev_model']), // 机型
                ApmDeviceList::PLATFORM[$row['os_type']] ?? '未知', // 平台
                $row['is_simulator'] == 1 ? '模拟器' : '真机', // 是否模拟器
                $tier[$row['device_tier']] ?? '中档', // 机型档位
                $row['num'], // 设备数量
                number_format(round($row['num'] / $devSum, 6) * 100, 2) . '%', // 总设备占比
                number_format($row['score'], 2), // 评分
                number_format($row['fps'], 2), // FPS
                number_format($row['stutter'], 2), // Stutter
                number_format($row['big_jank'], 2), // BigJank
                number_format(round($row['max_memory'] / (1024 * 1024), 6), 2), // 内存峰值
                number_format(round($row['memory'] / (1024 * 1024), 6), 2), // 内存均值
            ]); // 写入每一行数据到 CSV 文件
        }

        fclose($file);

        return $filename;
    }
}
