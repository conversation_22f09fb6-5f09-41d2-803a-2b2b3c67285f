<?php

/**
 * 线下性能检测报告标签控制器
 * @desc 线下性能检测报告标签控制器
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2024/12/17
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Perf;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Model\Perf\Report;
use App\Model\Perf\ReportTag;
use App\Service\PerfomanceScrore\ReCalculatePerformScore;
use App\Service\PerfReportStatService;
use Illuminate\Http\JsonResponse;

class PerfReportTagController extends Controller
{
    /**
     * 获取标签列表
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15282
     * @return JsonResponse
     */
    public function get()
    {
        $input = request()->all();
        $validator = \Validator::make($input, [
            'report_id' => 'required|integer'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            $tag = ReportTag::query()->where('report_id', $input['report_id'])->first();
            return $this->response(StatusCode::C_SUCCESS, $tag ? $tag['tag'] : []);
        } catch (\Exception $e) {
            \Log::error('通用性能检测获取标签列表接口报错-reportId:' . $input['report_id'] . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 编辑标签
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15280
     * @return JsonResponse
     */
    public function edit()
    {
        $input = request()->all();
        $validator = \Validator::make($input, [
            'report_id' => 'required|integer',
            'tag' => 'required|array',
            'tag.*.name' => 'required|string',
            'tag.*.start' => 'required|integer',
            'tag.*.end' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            ReportTag::query()->updateOrCreate(
                [
                    'report_id' => $input['report_id']
                ],
                [
                    'tag' => $input['tag']
                ]
            );
            return $this->response(StatusCode::C_SUCCESS);
        } catch (\Exception $e) {
            \Log::error('通用性能检测编辑标签接口报错-reportId:' . $input['report_id'] . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 统计标签
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15283
     * @return JsonResponse
     */
    public function stat()
    {
        $input = request()->all();
        $validator = \Validator::make($input, [
            'report_id' => 'required|integer'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        try {
            $report = Report::query()->where('report_id', $input['report_id'])->firstOrFail();
            $tag = ReportTag::query()->where('report_id', $input['report_id'])->first();
            //获取文件内容
            $fileData = file_get_contents(str_replace('storage', storage_path('app/public'), $report['report_url']));
            //json转为数据
            $fileData = json_decode($fileData, true);
            //获取每个标签的统计和评分
            $tags = $tag ? $tag['tag'] : [];
            //遍历数据
            foreach ($tags as &$item) {
                $item['Statistics'] = (new PerfReportStatService())->analyze(array_slice($fileData['Data'], $item['start'], $item['end'] - $item['start'] + 1), $report['system']);
                $item['ScoreV2'] = (new ReCalculatePerformScore())->setLevel($fileData['ScoreDetail']['tier'] ?? 2)
                    ->setRam($report['ram'])
                    ->setReportData(array_slice($fileData['Data'], $item['start'], $item['end'] - $item['start'] + 1))
                    ->setPssMemoryPeak(floatval($item['Statistics']['pssPeak']) / (1024 * 1024))
                    ->setAvg10BigJank(floatval($item['Statistics']['avg10BigJank']))
                    ->getScore();
            }
            //返回
            return $this->response(StatusCode::C_SUCCESS, $tags);
        } catch (\Exception $e) {
            \Log::error('通用性能检测获取标签列表接口报错-reportId:' . $input['report_id'] . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
