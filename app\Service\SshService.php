<?php
/**
 * SshService.php
 *
 * User: Dican
 * Date: 2022/8/18
 * Email: <<EMAIL>>
 */

namespace App\Service;


class SshService
{
    protected $ssh;

    public function __construct($host, $port, $user, $pwd)
    {
        //判断是否成功安装ssh2扩展
        if(!function_exists("ssh2_connect")){
            exit('SSH扩展没有安装或者没有安装成功');
        }
        //建立ssh2连接
        $ssh2 = ssh2_connect($host, $port);
        if (!$ssh2) {
            exit('连接服务器失败');
        }
        //连接成功后进行密码验证，没验证无法进行其他操作。
        if (!ssh2_auth_password($ssh2, $user, $pwd)) {
            exit('密码验证不通过');
        }
        $this->ssh = $ssh2;
    }

    /**
     * 执行调用命令
     * @param string $cmd 命令行
     * @param null $out 输入
     * @param null $err 错误报告
     * @return bool
     */
    public function run(string $cmd, &$out = null, &$err = null): bool
    {
        $result = false;
        $out = '';
        $err = '';
        $sshout = ssh2_exec($this->ssh, $cmd);
        if ($sshout) {
            $ssherr = ssh2_fetch_stream($sshout, SSH2_STREAM_STDERR);
            if ($ssherr) {
                # we cannot use stream_select() with SSH2 streams
                # so use non-blocking stream_get_contents() and usleep()
                if (stream_set_blocking($sshout, false) and
                    stream_set_blocking($ssherr, false)
                ) {
                    $result = true;
                    # loop until end of output on both stdout and stderr
                    $wait = 0;
                    while (!feof($sshout) or !feof($ssherr)) {
                        # sleep only after not reading any data
                        if ($wait) usleep($wait);
                        $wait = 50000; # 1/20 second
                        if (!feof($sshout)) {
                            $one = stream_get_contents($sshout);
                            if ($one === false) {
                                $result = false;
                                break;
                            }
                            if ($one != '') {
                                $out .= $one;
                                $wait = 0;
                            }
                        }
                        if (!feof($ssherr)) {
                            $one = stream_get_contents($ssherr);
                            if ($one === false) {
                                $result = false;
                                break;
                            }
                            if ($one != '') {
                                $err .= $one;
                                $wait = 0;
                            }
                        }
                    }
                }
                # we need to wait for end of command
                stream_set_blocking($sshout, true);
                stream_set_blocking($ssherr, true);
                # these will not get any output
                stream_get_contents($sshout);
                stream_get_contents($ssherr);
                fclose($ssherr);
            }
            fclose($sshout);
        }
        return $result;
    }
}
