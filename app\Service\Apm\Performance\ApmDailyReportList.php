<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;

class ApmDailyReportList extends ApmBase
{
    /**
     * 获取具体设备某一天的数据报告列表
     */
    public function getList(): array
    {
        // 通用查询条件
        $query = MysqlApmReportList::query()
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("$this->mysql_apm_device_list_table.developer_app_id", '=', "$this->mysql_apm_report_list_table.developer_app_id")
                    ->on("$this->mysql_apm_device_list_table.dev_str", '=', "$this->mysql_apm_report_list_table.dev_str");
            })
            ->join($this->performance_stat_data_table, "$this->mysql_apm_report_list_table.id", '=', "$this->performance_stat_data_table.session_id", 'left')
            ->join($this->performance_score_data_table, "$this->mysql_apm_report_list_table.id", '=', "$this->performance_score_data_table.session_id", 'left')
            ->where("$this->mysql_apm_report_list_table.developer_app_id", $this->params['developer_app_id'])
            ->where("$this->performance_stat_data_table.duration", '>', $this->getMinDuration())
            ->where("$this->mysql_apm_report_list_table.dev_str", $this->params['dev_str'])
            ->whereRaw("DATE($this->mysql_apm_report_list_table.created_at) = '{$this->params['date']}'");

        // 获取记录总数
        $totalRes = (clone $query)->selectRaw('count(*) as total')->firstFromSR();
        // 转为数字
        $total = intval($totalRes['total']);

        //没有记录返回空数组
        if (empty($total)) {
            return [
                'list' => [],
                'total' => 0,
            ];
        }

        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_report_list_table}.id AS report_id,
{$this->mysql_apm_report_list_table}.dev_str AS dev_str,
{$this->performance_stat_data_table}.duration AS duration,
{$this->mysql_apm_device_list_table}.dev_model AS dev_model,
{$this->mysql_apm_device_list_table}.dev_brand AS dev_brand,
{$this->mysql_apm_report_list_table}.app_version_name AS game_version_code,
{$this->performance_score_data_table}.all_score AS all_score,
{$this->mysql_apm_report_list_table}.created_at AS created_at,
{$this->mysql_apm_device_list_table}.os_type AS os_type,
{$this->performance_stat_data_table}.sum_fps / {$this->performance_stat_data_table}.num AS avg_fps,
{$this->performance_stat_data_table}.sum_used_memory / {$this->performance_stat_data_table}.num AS avg_used_memory,
{$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time AS stutter
EXPRESSION;

        // 获取记录列表
        $this->reportList = (clone $query)->selectRaw($selectRaw)
            ->orderBy($this->sortField, $this->sortType)
            ->offset($this->getPageNum())
            ->limit($this->perPage)
            ->getFromSR();

        //处理数据
        $this->handleListData();

        //返回数据
        return [
            'total' => $total,
            'list' => $this->reportList,
        ];
    }
}
