<?php

/**
 * API控制器类
 * @desc API控制器类
 * <AUTHOR> <EMAIL>
 * @date 2025/02/25
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Http\Validation\Summary\HomeValidation;
use App\Model\Apm\ApmGlobalConfig;
use App\Model\Apps;
use App\Service\Apm\Performance\ApmHomeScore;
use App\Service\Apm\Performance\ApmHomeStutter;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{
    /**
     * 获取APP的apm最小过滤时间
     *
     * @return array
     */
    private function getAppApmMinDurations($apps)
    {
        //获取每个app过滤的时间
        $config = ApmGlobalConfig::query()->get();
        $configs = [];
        foreach ($config as $item) {
            $configs[$item['developer_app_id']] = $item['min_duration'];
        }
        foreach (array_keys($apps) as $id) {
            if (!isset($configs[$id])) {
                $configs[$id] = 0;
            }
        }
        return $configs;
    }

    /**
     * 获取app列表
     *
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse
     */
    private function getApps(Request $request)
    {
        $list = Apps::query()->where("status", 1)->get()->toArray();
        $format = [];
        foreach ($list as $value) {
            $format[$value['id']] = [
                'id' => $value['id'],
                'app_name' => $value['app_name'],
                'app_icon' => $value['app_icon'],
            ];
        }
        return $format;
    }

    /**
     * 线上性能首页评分数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5543
     * @return JsonResponse
     */
    public function apmScore(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->osType()
            ->homeType()
            ->validate();

        try {
            $apps = $this->getApps($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeScore($params))->getList($configs, $apps);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data['all'] ?? []);
        } catch (Exception $e) {
            Log::error('获取线上性能首页评分数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 线上性能首页卡顿数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5543
     * @return JsonResponse
     */
    public function apmStutter(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->osType()
            ->homeType()
            ->validate();

        try {
            $apps = $this->getApps($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeStutter($params))->getList($configs, $apps);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data['all'] ?? []);
        } catch (Exception $e) {
            Log::error('获取线上性能首页卡顿数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
