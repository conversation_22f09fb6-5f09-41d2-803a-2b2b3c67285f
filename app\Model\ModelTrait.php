<?php
/**
 * ModelTrait.php
 *
 * User: Dican
 * Date: 2022/8/16
 * Email: <<EMAIL>>
 */

namespace App\Model;

use App\Components\ApiResponse\ErrorHelper;
use App\Exceptions\InfoException;

/**
 * 模型层操作trait
 * 封装增删查改统一操作，减少每次写重复的代码
 * Trait ModelTrait
 * @package App\Model
 */
trait ModelTrait
{
    /**
     * 新增、编辑
     * model需定义validateRule、uniqueKey字段
     * @example
        public $_validateRule = [
            'username' => 'required',
            'alias' => 'required',
            'password' => 'required',
        ];
        public $uniqueKey = ['username'];
     * @param array $params
     * @param false $validate 是否验证表单数据
     * @param false $checkExists 是否检验唯一值
     * @throws InfoException
     */
    public function store($params = [], $validate = false, $checkExists = false)
    {
        empty($params) && $params = \request()->all();
        $validate && ErrorHelper::validate($params, $this->validateRule);

        $checkExists && $this->checkExists($params);

        foreach ($params as $key => $value) {
            in_array($key, $this->getFillable()) && $this->$key = $value;
        }
        !$this->save() && ErrorHelper::callException(1007);
    }

    /**
     * 检查是否已存在相同数据
     * model需定义uniqueKey字段
     * @example public $uniqueKey = ['username'];代表用户名唯一
     * @param array $params
     * @throws InfoException
     */
    private function checkExists(array $params)
    {
        //组装唯一值判断条件
        $unique = [];
        foreach ($this->uniqueKey as $value) {
            $unique[$value] = $params[$value] ?? '';
        }

        (new static())->where($unique)->exists() && ErrorHelper::callException(1012);
    }
}
