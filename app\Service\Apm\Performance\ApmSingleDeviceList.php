<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\StarRocksDB;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ApmSingleDeviceList extends ApmBase
{
    /**
     * 获取子查询SQL
     *
     * @return string
     */
    private function getSubSql(): string
    {
        return StarRocksDB::toSql(DB::table($this->mysql_apm_report_list_table)
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->leftJoin($this->performance_stat_data_table, "{$this->mysql_apm_report_list_table}.id", '=', "{$this->performance_stat_data_table}.session_id")
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->when(isset($this->params['dev_str']), function (Builder $query) { // 判断是否传入设备ID
                return $query->where("{$this->mysql_apm_report_list_table}.dev_str", $this->params['dev_str']);
            })
            ->when(isset($this->params['os_type']), function (Builder $query) { // 判断是否传入平台
                return $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['tag']), function (Builder $query) { // 判断是否传入标签
                return $query->whereRaw("array_contains({$this->performance_stat_data_table}.tags_info, '" . str_replace('/', '\\\/', $this->params['tag']) . "')");
            })
            ->when(isset($this->params['game_version_code']), function (Builder $query) { // 判断是否传入游戏版本号
                return $query->whereIn("{$this->mysql_apm_report_list_table}.app_version_name", explode(',', $this->params['game_version_code']));
            })
            ->when(isset($this->params['uid']), function (Builder $query) { // 判断是否传入用户标识
                return $query->whereRaw("array_contains({$this->performance_stat_data_table}.user_id, '" . $this->params['uid'] . "')");
            })
            ->when(isset($this->params['dev_brand']), function (Builder $query) { // 判断是否传入设备品牌
                return $query->whereIn("{$this->mysql_apm_device_list_table}.dev_brand", explode(',', $this->params['dev_brand']));
            })
            ->when(isset($this->params['dev_model']), function (Builder $query) { // 判断是否传入设备型号
                return $query->where("{$this->mysql_apm_device_list_table}.dev_model", ApmModelMap::getReverseValue($this->params['dev_model']));
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->groupBy("{$this->mysql_apm_report_list_table}.developer_app_id", "{$this->mysql_apm_report_list_table}.dev_str")
            ->selectRaw("{$this->mysql_apm_report_list_table}.developer_app_id,{$this->mysql_apm_report_list_table}.dev_str,max({$this->mysql_apm_report_list_table}.id) as id"));
    }

    /**
     * 获取主要查询对象
     *
     * @param $subSql
     * @return Builder
     */
    private function getMainQuery($subSql): Builder
    {
        return DB::table(DB::raw("({$subSql}) as t"))
            ->join($this->mysql_apm_report_list_table, 't.id', '=', "{$this->mysql_apm_report_list_table}.id")
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->leftJoin($this->performance_stat_data_table, 't.id', '=', "{$this->performance_stat_data_table}.session_id")
            ->leftJoin($this->performance_score_data_table, 't.id', '=', "{$this->performance_score_data_table}.session_id");
    }

    /**
     * 获取列表
     *
     * @return array
     */
    public function getList(): array
    {
        // 通用查询条件
        $subSql = $this->getSubSql();

        $mainQuery = $this->getMainQuery($subSql);

        // 获取记录总数
        $totalRes = StarRocksDB::query((clone $mainQuery)->selectRaw('count(*) as total'))->first();
        // 转为数字
        $total = (int)$totalRes['total'];

        //没有记录返回空数组
        if (empty($total)) {
            return [
                'list' => [],
                'total' => 0,
            ];
        }

        //获取数据库数据
        $this->getDbList($mainQuery);

        //处理数据
        $this->handleListData();

        // 返回数据
        return [
            'total' => $total,
            'list' => $this->reportList,
        ];
    }

    /**
     * 获取数据库数据
     *
     * @param $mainQuery
     * @return void
     */
    private function getDbList($mainQuery): void
    {
        //查询列表，需要查询的字段
        $selectRaw = <<<EXPRESSION
t.id AS report_id,
t.dev_str AS dev_str,
{$this->performance_stat_data_table}.duration AS duration,
{$this->mysql_apm_device_list_table}.dev_brand AS dev_brand,
{$this->mysql_apm_device_list_table}.dev_model AS dev_model,
{$this->mysql_apm_device_list_table}.device_tier AS device_tier,
{$this->mysql_apm_report_list_table}.app_version_name AS game_version_code,
{$this->performance_score_data_table}.all_score AS all_score,
{$this->mysql_apm_report_list_table}.created_at AS created_at,
{$this->mysql_apm_device_list_table}.os_type AS os_type,
{$this->performance_stat_data_table}.sum_fps / {$this->performance_stat_data_table}.num AS avg_fps,
{$this->performance_stat_data_table}.sum_used_memory / {$this->performance_stat_data_table}.num AS avg_used_memory,
{$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time AS stutter
EXPRESSION;

        $this->reportList = StarRocksDB::query((clone $mainQuery)
                ->selectRaw($selectRaw)
                ->orderBy($this->sortField, $this->sortType)
                ->limit($this->perPage)
                ->offset($this->getPageNum())
        )->get(); // 获取记录列表
    }
}
