<?php

namespace App\Service\Apm\Performance;

use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

abstract class ApmBase
{
    use ApmTrait;

    const STANDARD_SCORE = 80;

    /**
     * 分页大小
     *
     * @var int
     */
    protected $perPage = 10;

    /**
     * 排序字段
     *
     * @var string
     */
    protected $sortField = 'created_at';

    /**
     * 排序类型
     *
     * @var string
     */
    protected $sortType = 'desc';

    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 开始时间
     *
     * @var string
     */
    protected $startTime;

    /**
     * 结束时间
     *
     * @var string
     */
    protected $endTime;

    /**
     * 环比开始时间
     *
     * @var string
     */
    protected $proportionStartTime;

    /**
     * 环比结束时间
     *
     * @var string
     */
    protected $proportionEndTime;

    /**
     * 报告列表
     *
     * @var array
     */
    protected $reportList;

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
        isset($params['limit']) && $this->perPage = $params['limit'];
        isset($params['sort_field']) && $this->sortField = $params['sort_field'];
        isset($params['sort_type']) && $this->sortType = $params['sort_type'];
        if (isset($params['type'])) {
            $dateType = Str::lower($params['type']);
            switch ($dateType) {
                case 'today':
                    $this->startTime = Carbon::now()->startOfDay()->toDateTimeString();
                    $this->endTime = Carbon::now()->endOfDay()->toDateTimeString();
                    break;
                case 'latest7':
                    $this->startTime = Carbon::now()->subDays(6)->startOfDay()->toDateTimeString();
                    $this->endTime = Carbon::now()->endOfDay()->toDateTimeString();
                    break;
                case 'latest30':
                    $this->startTime = Carbon::now()->subDays(29)->startOfDay()->toDateTimeString();
                    $this->endTime = Carbon::now()->endOfDay()->toDateTimeString();
                    break;
                default:
                    $this->startTime = isset($this->params['start_time']) ? Carbon::parse($this->params['start_time'])->startOfDay()->toDateTimeString() : Carbon::now()->startOfDay()->toDateTimeString();
                    $this->endTime = isset($this->params['end_time']) ? Carbon::parse($this->params['end_time'])->endOfDay()->toDateTimeString() : Carbon::now()->endOfDay()->toDateTimeString();
                    break;
            }
        } else {
            $this->startTime = isset($this->params['start_time']) ? Carbon::parse($this->params['start_time'])->startOfDay()->toDateTimeString() : Carbon::now()->startOfDay()->toDateTimeString();
            $this->endTime = isset($this->params['end_time']) ? Carbon::parse($this->params['end_time'])->endOfDay()->toDateTimeString() : Carbon::now()->endOfDay()->toDateTimeString();
        }
        // 环比的结束时间就是 $this->startTime 的前一天
        $this->proportionEndTime = Carbon::parse($this->startTime)->subDay()->endOfDay()->toDateTimeString();
        // 要计算 $this->startTime 和 $this->endTime 相差的天数
        $proportionDays = Carbon::parse($this->startTime)->diffInDays(Carbon::parse($this->endTime));
        // 环比开始时间就是 $this->startTime 的前 $proportionDays 天
        $this->proportionStartTime = Carbon::parse($this->proportionEndTime)->subDays($proportionDays)->startOfDay()->toDateTimeString();
        // 判断相差天数，大于等于28，小于等于30
        if ($proportionDays >= 28 && $proportionDays <= 30) {
            $this->proportionStartTime = Carbon::parse($this->proportionEndTime)->startOfMonth()->toDateTimeString();
        }
    }

    /**
     * 获取页码
     *
     * @return float|int
     */
    protected function getPageNum()
    {
        // 计算页码，默认显示第一页
        if (isset($this->params['page'])) {
            $pageNum = ($this->params['page'] - 1) * $this->perPage;
        } else {
            $pageNum = 0;
        }
        return $pageNum;
    }

    /**
     * 处理列表数据
     *
     * @return void
     */
    protected function handleListData()
    {
        //判断是否有数据
        if (empty($this->reportList)) {
            return;
        }

        foreach ($this->reportList as $key => $item) {
            $this->reportList[$key]['dev_model'] = ApmModelMap::getValue($item['dev_model']);
            $this->reportList[$key]['avg_fps'] = bcadd(sprintf('%f', $item['avg_fps']), 0, 2) ?? 0; // fps均值
            $this->reportList[$key]['avg_used_memory'] = bcadd(sprintf('%f', $item['avg_used_memory']), 0, 2) ?? 0; // 使用内存均值
            $this->reportList[$key]['stutter'] = bcmul(sprintf('%f', $item['stutter']), 100, 2) . '%' ?? '0%'; // 卡帧率
        }
    }
}
