<?php

namespace App\Service\PerfomanceScrore;

/**
 * 流畅度评分类
 */
class SmoothnessPerformanceScore extends PerformanceScoreService implements PerformanceScore
{
    protected $level;
    protected $jankTimes;
    protected $frameTimes;

    public function __construct($level, $jankTimes, $frameTimes)
    {
        $this->level = $level;
        $this->jankTimes = $jankTimes;
        $this->frameTimes = $frameTimes;
    }

    /**
     * 以Android的2档机型为例
     * 卡顿率等于2%，分数30*0.7=21
     * 卡顿率等于8%，分数0分
     * 卡顿率等于0.5%，分数30分
     *
     * @return int
     */
    public function getPerformanceScore() : int
    {
        if (empty($this->frameTimes)) {
            return 0;
        }
        // 计算卡顿率
        $stutter = bcmul(bcdiv($this->jankTimes, $this->frameTimes, 4), 100, 2);
        // 卡顿率占比
        $proportion = self::LEVEL_TO_PROPORTION[$this->level];
        // 计算分数
        $score = 21;
        if ($stutter == $proportion) {
            return $score;
        } elseif ($stutter < $proportion) {
            $diff = bcsub($proportion, $stutter, 2);
            // 加分
            $score = bcadd(bcmul(bcmul($diff, 100, 2), 0.06, 4), $score, 2);
            // 最高30分
            return min($score, 30);
        } else {
            $diff = bcsub($stutter, $proportion, 2);
            // 扣分
            $score = bcsub($score, bcmul(bcmul($diff, 100, 2), 0.035, 4), 2);
            // 最低0分
            return max($score, 0);
        }
    }
}
