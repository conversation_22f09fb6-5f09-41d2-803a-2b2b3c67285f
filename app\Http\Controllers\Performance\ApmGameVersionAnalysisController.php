<?php

/**
 * 游戏版本分析控制器
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\AnalysisValidation;
use App\Service\Apm\Analysis\GameVersion;
use Exception;
use Illuminate\Http\JsonResponse;

class ApmGameVersionAnalysisController extends Controller
{
    /**
     * 通用处理逻辑
     *
     * @param $func
     * @param $message
     * @return JsonResponse
     */
    protected function commonHandle($func, $message): JsonResponse
    {
        $params = AnalysisValidation::build()
            ->developerAppId()->startTime()->endTime()->osType()
            ->isSimulator()->deviceTier()->gameVersionCode()->innerVersion()
            ->quality()->filterTop()
            ->validate();

        try {
            //获取数据
            $data = call_user_func([new GameVersion($params), $func]);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error("{$message},原因:" . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取概览信息
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3861
     * @return JsonResponse
     */
    public function summary(): JsonResponse
    {
        return $this->commonHandle('summary', '获取版本分析概览接口报错');
    }

    /**
     * 获取top排行榜
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3863
     * @return JsonResponse
     */
    public function top(): JsonResponse
    {
        return $this->commonHandle('top', '获取版本分析top排行榜接口报错');
    }

    /**
     * 获取数据列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3864
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        $params = AnalysisValidation::build()
            ->developerAppId()->startTime()->endTime()->sortField()
            ->sortType()->osType()->isSimulator()->deviceTier()
            ->gameVersionCode()->innerVersion()->quality()->filterTop()
            ->validate();

        try {
            //获取数据
            $data = (new GameVersion($params))->list();
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            \Log::error("获取版本分析数据列表接口报错,原因:" . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
