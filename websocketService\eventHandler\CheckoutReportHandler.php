<?php
/**
 * CheckoutReportHandler.php
 *
 * User: Dican
 * Date: 2022/8/17
 * Email: <<EMAIL>>
 */

namespace websocketService\eventHandler;


class CheckoutReportHandler extends socketEventBase implements eventInterface
{
    private $push = [];
    public static $type = 2;

    public function returnMessage(): array
    {
        $fds = $this->redis->hGetAll(CheckoutHandler::getRedisKey());
        foreach ($fds as $fd => $checkoutIds) {
            if (!in_array($this->message['checkout_id'], explode(',', $checkoutIds))) continue;
            $this->push[] = ['fd' => $fd, 'message' => ['type' => CheckoutHandler::$type, 'data' => CheckoutHandler::getCheckout($checkoutIds, $this->mysql)]];
        }
        return $this->push;
    }
}
