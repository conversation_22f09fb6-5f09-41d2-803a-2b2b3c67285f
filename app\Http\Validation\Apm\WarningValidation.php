<?php

/**
 * 线上性能预警校验类
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;
use App\Model\Apm\ApmDeviceList;
use App\Model\Apm\ApmWarning;
use Illuminate\Validation\Rule;

/**
 * @method static WarningValidation build()
 */
class WarningValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): WarningValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 状态校验
     *
     * @return $this
     */
    public function status(): WarningValidation
    {
        $this->rules['status'] = [
            'required',
            'integer',
            Rule::in([ApmWarning::START, ApmWarning::CLOSE]),
        ];
        return $this;
    }

    /**
     * 页码校验
     *
     * @return $this
     */
    public function page(): WarningValidation
    {
        $this->rules['page'] = 'integer|min:1';
        return $this;
    }

    /**
     * 每页条数校验
     *
     * @return $this
     */
    public function perPage(): WarningValidation
    {
        $this->rules['per_page'] = 'integer|min:10|max:100';
        return $this;
    }

    /**
     * 平台校验
     *
     * @return $this
     */
    public function osType(): WarningValidation
    {
        $this->rules['os_type'] = [
            'required',
            'integer',
            Rule::in([0, ApmDeviceList::ANDROID, ApmDeviceList::IOS, ApmDeviceList::PC, ApmDeviceList::MINI, ApmDeviceList::HARMONY]),
        ];
        return $this;
    }

    /**
     * 名称校验
     *
     * @return $this
     */
    public function name(): WarningValidation
    {
        $this->rules['name'] = [
            'required',
            'string',
            Rule::unique('apm.warning')->where(function ($query) {
                $request = request();
                return $query->where('developer_app_id', $request->input('developer_app_id'))
                    ->when($request->route('warning_id'), function ($query) use ($request) {
                        $query->where('warning_id', '<>', $request->route('warning_id'));
                    });
            }),
        ];
        return $this;
    }

    /**
     * app版本（默认全选）校验
     *
     * @return $this
     */
    public function appVersion(): WarningValidation
    {
        $this->rules['app_version'] = [
            'array',
        ];
        $this->rules['app_version.*'] = [
            'required',
            'string',
        ];
        return $this;
    }

    /**
     * 接收人员（填写工号）校验
     *
     * @return $this
     */
    public function receivingPerson(): WarningValidation
    {
        $this->rules['receiving_person'] = [
            'array',
        ];
        $this->rules['receiving_person.*'] = [
            'required',
            'string',
        ];
        return $this;
    }

    /**
     *    接收群校验
     *
     * @return $this
     */
    public function receivingGroup(): WarningValidation
    {
        $this->rules['receiving_group'] = [
            'array',
        ];
        $this->rules['receiving_group.*'] = [
            'required',
            'string',
        ];
        return $this;
    }

    /**
     *    接收手机号校验
     *
     * @return $this
     */
    public function receivingPhone(): WarningValidation
    {
        $this->rules['receiving_phone'] = [
            'array',
        ];
        return $this;
    }

    /**
     *    预警触发条件校验
     *
     * @return $this
     */
    public function warningRule(): WarningValidation
    {
        $this->rules['warning_rule'] = [
            'required_if:monitor_type,' . ApmWarning::ERROR_MONITOR,
            'array',
        ];
        $this->rules['warning_rule.*.schedule_time'] = [
            'required_if:monitor_type,' . ApmWarning::ERROR_MONITOR,
            'integer',
            'min:1',
        ];
        $this->rules['warning_rule.*.index'] = [
            'required_if:monitor_type,' . ApmWarning::ERROR_MONITOR,
            'integer',
            'in:1',
        ];
        $this->rules['warning_rule.*.operator'] = [
            'required_if:monitor_type,' . ApmWarning::ERROR_MONITOR,
            'string',
            'in:>,<',
        ];
        $this->rules['warning_rule.*.value'] = [
            'required_if:monitor_type,' . ApmWarning::ERROR_MONITOR,
            'integer',
            'min:1',
        ];
        return $this;
    }

    /**
     *    监控类型类型校验
     *
     * @return $this
     */
    public function monitorType(): WarningValidation
    {
        $this->rules['monitor_type'] = [
            'required',
            'integer',
            Rule::in([ApmWarning::REPORT_MONITOR, ApmWarning::ERROR_MONITOR]),
        ];
        return $this;
    }

    /**
     *    执行周期
     *
     * @return $this
     */
    public function scheduleTime(): WarningValidation
    {
        $this->rules['schedule_time'] = [
            'required',
            'integer',
        ];
        return $this;
    }
}
