<?php

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Components\Helper\Curl;
use App\Http\Controllers\Controller;
use App\Http\Validation\Summary\HomeValidation;
use App\Model\Apm\ApmGlobalConfig;
use App\Model\Apm\StarRocks\MysqlApmReportList;
use App\Model\Apps;
use App\Model\RealMachine\RealMachineReport;
use App\Model\Report;
use App\Model\Special\Report as SpecialReport;
use App\Service\Apm\Performance\ApmHomeDevice;
use App\Service\Apm\Performance\ApmHomeNetDelay;
use App\Service\Apm\Performance\ApmHomeScore;
use App\Service\Apm\Performance\ApmHomeScoreList;
use App\Service\Apm\Performance\ApmHomeStutter;
use App\Service\Apm\Performance\ApmHomeSummary;
use App\Service\Apm\Stat\BasicInfo;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HomeSummaryController extends Controller
{
    /**
     * AB性能检测首页概览统计数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5520
     * @return JsonResponse
     */
    public function abSummary(): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->developerAppId()
            ->startDate()
            ->endDate()
            ->validate();

        try {
            $report = Report::query()
                ->selectRaw('sum(all_ab_num) as all_ab_num, sum(redundancy_ab_num) as redundancy_ab_num, sum(redundancy_resource_size) as redundancy_resource_size')
                ->where('developer_app_id', $params['developer_app_id'])
                ->whereBetween('created_at', [Carbon::parse($params['start_date'])->startOfDay()->toDateTimeString(), Carbon::parse($params['end_date'])->endOfDay()->toDateTimeString()])
                ->first();
            // 判断是否使用
            $count = Report::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->count();
            //返回的数据
            $data = [
                'is_use' => $count > 0,
                'ab_file_rate' => $report['all_ab_num'] ? round(bcdiv($report['redundancy_ab_num'], $report['all_ab_num'], 4) * 100, 2) : 0, //AB文件冗余率
                'ab_file_size' => round(bcdiv($report['redundancy_resource_size'], 1024, 2), 2), //AB文件冗余大小
            ];

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取AB性能检测首页概览统计数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 真人真机首页概览统计数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5525
     * @return JsonResponse
     */
    public function rmSummary(): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->developerAppId()
            ->startDate()
            ->endDate()
            ->validate();

        try {
            $report = RealMachineReport::query()
                ->selectRaw('count(report_id) as num, sum(score) as score')
                ->where('developer_app_id', $params['developer_app_id'])
                ->whereBetween('created_at', [Carbon::parse($params['start_date'])->startOfDay()->toDateTimeString(), Carbon::parse($params['end_date'])->endOfDay()->toDateTimeString()])
                ->first();

            $middleScore = [];
            //如果有数据，获取中位数
            if ($report['num']) {
                $middleScore = RealMachineReport::query()
                    ->selectRaw('score')
                    ->where('developer_app_id', $params['developer_app_id'])
                    ->whereBetween('created_at', [Carbon::parse($params['start_date'])->startOfDay()->toDateTimeString(), Carbon::parse($params['end_date'])->endOfDay()->toDateTimeString()])
                    ->offset(floor($report['num'] / 2))
                    ->limit(1)
                    ->orderBy('score')
                    ->first();
            }

            // 判断是否使用
            $count = RealMachineReport::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->count();
            //返回的数据
            $data = [
                'is_use' => $count > 0,
                'report_num' => intval($report['num']), //检测数量
                'avg_score' => $report['num'] ? round(bcdiv($report['score'], $report['num'], 2), 2) : 0, //平均分
                'middle_score' => $middleScore['score'] ?? 0, //中文分
            ];

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取真人真机首页概览统计报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 特效检测首页概览统计数据
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/14354
     * @return JsonResponse
     */
    public function srSummary()
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->developerAppId()
            ->startDate()
            ->endDate()
            ->validate();

        try {
            $report = SpecialReport::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->whereBetween('created_at', [Carbon::parse($params['start_date'])->startOfDay()->toDateTimeString(), Carbon::parse($params['end_date'])->endOfDay()->toDateTimeString()])
                ->count();
            $count = SpecialReport::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->count();
            //返回的数据
            $data = [
                'is_use' => $count > 0, // 是否接入
                'use_num' => $report, // 使用次数
            ];
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取特效检测首页概览统计数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 线上性能首页概览统计数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5526
     * @return JsonResponse
     */
    public function apmSummary(): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->developerAppId()
            ->startDate()
            ->endDate()
            ->validate();

        try {
            $params['start_time'] = $params['start_date'];
            $params['end_time'] = $params['end_date'];
            $basicData = (new BasicInfo($params))->getData();

            //获取列表数据
            $list = (new ApmHomeSummary($params))->getList();

            $count = MysqlApmReportList::query()->selectRaw('count(*) as num')->where('developer_app_id', $params['developer_app_id'])->firstFromSR();

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, [
                'list' => $list,
                'compliance_rate' => $basicData['score_num'] ? round(bcdiv($basicData['score_num'] - $basicData['low_score_num'], $basicData['score_num'], 4) * 100, 2) : 0,
                'report_num' => intval($basicData['score_num']),
                'average_memory' => $basicData['average_memory'],
                'stutter' => $basicData['stutter'],
                'is_use' => ($count['num'] ?? 0) > 0,
            ]);
        } catch (Exception $e) {
            Log::error('获取线上性能首页概览统计报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 线上性能首页设备列表数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5532
     * @return JsonResponse
     */
    public function apmDevice(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->startDate()
            ->endDate()
            ->validate();

        try {
            $params['start_time'] = $params['start_date'];
            $params['end_time'] = $params['end_date'];

            $apps = $this->pullAllAppList($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeDevice($params))->getList($configs);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取线上性能首页设备列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION, [], json_encode([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]));
        }
    }

    /**
     * 线上性能首页评分数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5543
     * @return JsonResponse
     */
    public function apmScore(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->startDate()
            ->endDate()
            ->osType()
            ->homeTypeNull()
            ->validate();

        try {
            $params['start_time'] = $params['start_date'];
            $params['end_time'] = $params['end_date'];

            $apps = $this->getApps($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeScore($params))->getList($configs, $apps);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取线上性能首页评分数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 线上性能首页卡顿数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5543
     * @return JsonResponse
     */
    public function apmStutter(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->startDate()
            ->endDate()
            ->osType()
            ->homeTypeNull()
            ->validate();

        try {
            $params['start_time'] = $params['start_date'];
            $params['end_time'] = $params['end_date'];

            $apps = $this->getApps($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeStutter($params))->getList($configs, $apps);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取线上性能首页卡顿数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 线上性能首页网络延迟数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5543
     * @return JsonResponse
     */
    public function apmNetDelay(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->startDate()
            ->endDate()
            ->osType()
            ->homeTypeNull()
            ->validate();

        try {
            $params['start_time'] = $params['start_date'];
            $params['end_time'] = $params['end_date'];

            $apps = $this->getApps($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeNetDelay($params))->getList($configs, $apps);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取线上性能首页网络延迟数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 线上性能首页评分列表数据
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=5554
     * @return JsonResponse
     */
    public function apmScoreList(Request $request): JsonResponse
    {
        //请求参数校验
        $params = HomeValidation::build()
            ->type()
            ->validate();

        try {
            $apps = $this->pullAllAppList($request);
            if (!is_array($apps)) {
                return $apps;
            }

            //获取每个app过滤的时间
            $configs = $this->getAppApmMinDurations($apps);

            $data = (new ApmHomeScoreList($params))->getList($configs, $apps, $params['type']);

            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取线上性能首页评分列表数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取app列表
     *
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse
     */
    private function getApps(Request $request)
    {
        $list = Apps::query()->where("status", 1)->get()->toArray();
        $format = [];
        foreach ($list as $value) {
            $format[$value['id']] = [
                'id' => $value['id'],
                'app_name' => $value['app_name'],
                'app_icon' => $value['app_icon'],
            ];
        }
        return $format;
        // //获取APP列表
        // // $url = 'https://test-developer-manager.shiyue.com/app/list';
        // $url = 'https://test-developer-manager.shiyue.com/authV2/getAllAppList'; // 效能后台权限优化2.0排行榜需要获取所有app数据（不再根据用户权限获取）
        // if (env('APP_ENV') == 'production') {
        //     // $url = 'https://developer-manager.shiyue.com/app/list';
        //     $url = 'https://developer-manager.shiyue.com/authV2/getAllAppList'; // 效能后台权限优化2.0排行榜需要获取所有app数据（不再根据用户权限获取）
        // }
        // $cookie = $request->header('Cookie');
        // $apps = Curl::get($url, ['page' => 1, 'per_page' => 10000], [CURLOPT_HTTPHEADER => ["Cookie: $cookie"]]);
        // //判断是否有数据
        // if (!$apps) {
        //     return $this->response(StatusCode::C_SYS_EXCAPTION);
        // }
        // $apps = json_decode($apps, true);
        // if ($apps['code'] != 0) {
        //     return response()->json($apps);
        // }
        // // return array_column($apps['data']['list'], null, 'id'); // /authV2/getAllAppList接口没有list下标
        // return array_column($apps['data'], null, 'id');
    }

    /**
     * @param Request $request
     * @return array|JsonResponse
     * author : <EMAIL>
     * datetime: 2023/07/13 14:54
     * memo : 獲取[全部的]APP列表
     */
    private function pullAllAppList(Request $request)
    {
        $envi = env('APP_ENV') == 'production' ? '' : 'test-';
        $api = "https://{$envi}developer-manager.shiyue.com/pullAllAppList";
        $appList = Curl::get($api);
        if (!$appList) {
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
        $appList = json_decode($appList, true);
        if ($appList['code'] != 0) {
            return response()->json($appList);
        }
        return array_column($appList['data'], null, 'id');
    }

    /**
     * 获取APP的apm最小过滤时间
     *
     * @return array
     */
    private function getAppApmMinDurations($apps)
    {
        //获取每个app过滤的时间
        $config = ApmGlobalConfig::query()->get();
        $configs = [];
        foreach ($config as $item) {
            $configs[$item['developer_app_id']] = $item['min_duration'];
        }
        foreach (array_keys($apps) as $id) {
            if (!isset($configs[$id])) {
                $configs[$id] = 0;
            }
        }
        return $configs;
    }
}
