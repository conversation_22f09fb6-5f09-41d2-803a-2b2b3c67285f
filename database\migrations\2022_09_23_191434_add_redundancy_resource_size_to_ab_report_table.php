<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRedundancyResourceSizeToAbReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('ab_report', function (Blueprint $table) {
            //
            $table->unsignedSmallInteger('redundancy_resource_size')->default(0)
                ->comment('冗余资源占用大小');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('ab_report', function (Blueprint $table) {
            //
            $table->dropColumn('redundancy_resource_size');
        });
    }
}
