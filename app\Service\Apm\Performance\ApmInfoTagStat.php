<?php

/**
 * 性能详情标签统计
 * @desc 性能标签统计
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/02/07
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\PerformanceTagStatData;

class ApmInfoTagStat
{
    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * 获取数据信息
     *
     * @return array
     */
    public function getData()
    {
        $selectRaw = <<<SELECT
tag,
ROUND(sum_fps / num, 2) AS avg_fps,
ROUND((sum_jank_time / sum_frame_times_time) * 100, 2) AS stutter,
ROUND(big_jank_count_10, 2) AS big_jank_count_10,
ROUND(max_used_memory / (1024 * 1024), 2) AS max_used_memory,
ROUND((sum_used_memory / num) / (1024 * 1024), 2) AS avg_used_memory,
ROUND(sum_battery_temp / num, 2) AS avg_temp,
ROUND(sum_battery_power / num, 2) AS avg_power,
ROUND(battery_power_hour, 2) AS avg_power_hour,
ROUND(down_traffic_10 / 1024, 2) AS down_traffic_10,
ROUND(up_traffic_10 / 1024, 2) AS up_traffic_10,
ROUND(sum_network_delay / num, 2) AS network_delay
SELECT;
        return PerformanceTagStatData::query()
            ->selectRaw($selectRaw)
            ->where('session_id', $this->params['report_id'])
            ->getFromSR();
    }
}
