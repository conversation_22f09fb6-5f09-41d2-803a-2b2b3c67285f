<?php
/**
 * 签名类
 * @desc 签名类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/03/15
 * @todo 这里是后续需要跟进的功能说明
 */
namespace App\Http\Middleware\Perf;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PerfVerifySign
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if (empty($request->input('timestamp')) || !$request->hasHeader('signature')) {
                throw new \RuntimeException('缺少必传参数', 2000);
            }
            //验签
            $this->verifySign($request);
        } catch (Exception $exception) {
            return response()->json(['data' => '', 'message' => $exception->getMessage(), 'code' => $exception->getCode()]);
        }
        return $next($request);
    }

    /**
     * 验证签名
     * @param Request $request
     * @throws Exception
     */
    private function verifySign(Request $request): void
    {
        $data = $request->all();
        $timestamp = $data['timestamp'];
        unset($data['report'], $data['timestamp'], $data['app_icon'], $data['extra']);
        $payload = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK | JSON_PRETTY_PRINT);
        $sign = sha1(implode(":", [$payload, env('API_SECRET'), $timestamp]));
        if ($sign != $request->header('signature')) {
            throw new \RuntimeException('非法请求', 2002);
        }
    }
}
