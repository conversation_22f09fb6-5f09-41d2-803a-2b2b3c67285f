<?php

/**
 * 监控配置变化服务
 * @desc 监控配置变化服务
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/09
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service;

use Illuminate\Support\Facades\Auth;

class MonitorConfigChangeService
{
    /**
     * 路径
     *
     * @var string
     *
     */
    const PATH = '/data/www/developer/monitor-config-change';

    /**
     * 推送机器人地址
     *
     * @var string
     *
     */
    private $webHook;

    /**
     * 变更前数据
     *
     * @var array
     *
     */
    private $oldData;

    /**
     * 变更标题
     *
     * @var string
     *
     */
    private $changeTitle;

    /**
     * 构造函数
     *
     * @param array $oldData
     * @param string $changeTitle
     */
    public function __construct($oldData, $changeTitle)
    {
        $this->webHook = file_get_contents(static::PATH . '/webhook.conf');
        $this->oldData = $oldData;
        $this->changeTitle = $changeTitle;
    }

    /**
     * 监控配置变化
     *
     * @return void
     */
    public function monitor($newData)
    {
        // 组装msg
        $msg = "配置变更提醒：{$this->changeTitle}\n\n";
        // 判断是否有旧数据
        if ($this->oldData) {
            $msg .= "变更前配置：" . json_encode($this->oldData, JSON_UNESCAPED_UNICODE) . "\n\n";
        }
        $msg .= "变更后配置：" . json_encode($newData, JSON_UNESCAPED_UNICODE) . "\n\n";
        $msg .= "变更操作者：" . Auth::user()->alias . "\n\n";
        $msg .= "变更时间：" . date('Y-m-d H:i:s', time());
        // 推送到机器人
        $this->curlJson(['msgtype' => 'text', 'text' => ['content' => $msg]]);
        // 写入日志文件
        $this->saveLog($msg);
    }

    /**
     * 保存数据到日志文件
     *
     * @param array $content
     * @return void
     */
    private function saveLog(string $content)
    {
        file_put_contents(static::PATH . '/monitor_config_change.log', date('Y-m-d H:i:s') . ' ' . $content . PHP_EOL, FILE_APPEND);
    }

    /**
     * 发起curl的json请求
     *
     * @param array $data
     * @return mixed
     */
    private function curlJson($data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, str_replace("\n", "", $this->webHook));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}
