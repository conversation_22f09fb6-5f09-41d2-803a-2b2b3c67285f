<?php

namespace App\Model\Special;

use App\Model\BaseModel;
use App\Model\ModelTrait;

class ThresholdConfig extends BaseModel
{
    use ModelTrait;

    protected $table = 'special_threshold_config';
    protected $primaryKey = 'threshold_config_id';

    protected $fillable = [
        'developer_app_id', 'name', 'config', 'is_current_config'
    ];

    public $validateRule = [
        'config' => 'required|array',
        'is_current_config' => 'required|int'
    ];

    //是否为当前配置
    public const CURRENT = 1;
    public const NOT_CURRENT = 0;
    public const TYPE = [
        self::NOT_CURRENT,
        self::CURRENT,
    ];

    protected $casts = [
        'config' => 'array',
    ];

    /**
     * 默认配置数据
     *
     * @var string
     */
    public const DEFAULT_CONFIG = [
        "rules" => [
            [
                "type" => 1,
                "desc" => "贴图内存",
                "thresholdValues" => [
                    "val" => 10
                ]
            ],
            [
                "type" => 2,
                "desc" => "最高粒子数",
                "thresholdValues" => [
                    "val" => 50
                ]
            ],
            [
                "type" => 3,
                "desc" => "平均每像素overDraw率",
                "thresholdValues" => [
                    "val" => 4
                ]
            ],
            [
                "type" => 4,
                "desc" => "特效实际填充像素点平均值",
                "thresholdValues" => [
                    "val" => 26214
                ]
            ],
        ]
    ];

    /**
     * 创建默认配置
     *
     * @param int $developerAppId
     * @return void
     */
    public function createDefaultConfig(int $developerAppId)
    {
        //1、获取数据库中配置的数量
        $count = $this->newQuery()->where('developer_app_id', $developerAppId)->count($this->primaryKey);
        //2、判断数据库中是否存在配置，有则不做处理
        if ($count > 0) return;
        //3、创建默认配置
        $this->name = '服务器默认配置';
        $this->developer_app_id = $developerAppId;
        $this->is_current_config = self::CURRENT;
        $this->config = self::DEFAULT_CONFIG;
        $this->save();
    }
}
