<?php

/**
 * 线下性能检测项目管理增加操作者和工号字段的迁移文件
 * @desc 线下性能检测项目管理增加操作者和工号字段的迁移文件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/04/16
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOperatorToPerfProjectTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('perf_project', function (Blueprint $table) {
            $table->string('operator')->default('')->comment('操作者');
            $table->string('staff_id')->default('')->comment('工号');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('perf_project', function (Blueprint $table) {
            $table->dropColumn('operator');
            $table->dropColumn('staff_id');
        });
    }
}
