<?php

/**
 * 全局配置
 * @desc 全局配置
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class ApmGlobalConfig extends Model
{
    /**
     * 默认值
     *
     * @var string
     */
    public const DEFAULT_VALUE = '{"max_duration":7200,"min_duration":0,"exception_fps_val":50,"exception_fps_jitter":8,"exception_pps_val":60,"exception_mark_val":20,"exception_mark_status":0,"exception_cold_cd":10,"exception_fps_jitter_num":2,"exception_big_jank":3,"exception_low_fps_cd":5,"exception_low_fps_num":3}';

    use ModelTrait;

    public $connection = "apm";

    protected $table = 'apm_global_config';

    protected $primaryKey = 'developer_app_id';

    protected $guarded = [];

    /**
     * 获取最小采集时间，单位秒
     *
     * @return float|int
     */
    public function getMinDurationAttribute()
    {
        $config = json_decode($this->config, true) ?? [];
        return $config['min_duration'] ?? 0;
    }

    /**
     * 获取异常标记次数阈值
     *
     * @return float|int
     */
    public function getExceptionMarkValAttribute()
    {
        $config = json_decode($this->config, true) ?? [];
        return $config['exception_mark_val'] ?? 0;
    }

    /**
     * 获取开关缓存KEY
     *
     * @param $developerAppId
     * @return string
     */
    private static function getCacheKey($developerAppId): string
    {
        return 'apm_global_config:' . $developerAppId;
    }

    /**
     * 刷新缓存
     *
     * @param $developerAppId
     * @param $config
     * @return void
     */
    public static function flushCache($developerAppId, $config)
    {
        $cacheKey = self::getCacheKey($developerAppId);
        Redis::connection('apm')->set($cacheKey, $config);
    }
}
