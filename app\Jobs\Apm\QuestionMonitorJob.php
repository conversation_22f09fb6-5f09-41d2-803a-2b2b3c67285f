<?php

namespace App\Jobs\Apm;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use ReflectionClass;

class QuestionMonitorJob extends BaseMonitorJob
{
    /**
     * 统计规则信息
     *
     * @var array
     */
    protected $rules = [];

    protected function checkMonitor(): bool
    {
        //获取当前时间的时间戳
        $currentTime = time();
        //1、判断是否有上次时间的时间戳，有的话判断当前时间是
        if (!empty($this->apmWarning['last_warning_time'])) {
            //循环判断规则
            foreach ($this->apmWarning['warning_rule'] as $rule) {
                //获取最后执行时间加上规则间隔时间的时间戳
                $time = Carbon::parse($this->apmWarning['last_warning_time'])->addSeconds($rule['schedule_time'])->timestamp;
                //当前时间小于最后执行时间(要减5分钟，防止时间更新时间的时候比较晚，差几分钟不满足条件)加上规则间隔时间，不执行
                if ($currentTime < ($time - 300)) return false;
            }
        }
        //2、可以执行
        return true;
    }

    protected function getMonitorData()
    {
        try {
            foreach ($this->apmWarning['warning_rule'] as $rule) {
                // 获取类
                $reflectionClass = new ReflectionClass('App\Service\Apm\WarningRule\Rule' . $rule['index']);
                //获取开始时间和结束时间
                $startTime = Carbon::now()->subSeconds($rule['schedule_time'])->second(0)->toDateTimeString();
                $endTime = Carbon::now()->second(0)->toDateTimeString();
                // 创建 ReflectionClass 对象
                $obj = $reflectionClass->newInstance(
                    $this->apmWarning['developer_app_id'],
                    $this->apmWarning['os_type'],
                    $startTime,
                    $endTime,
                    $this->apmWarning['app_version'],
                    $rule['value']
                );
                // 调用 getData 方法
                $this->monitorData[$rule['index']] = $reflectionClass->getMethod('getData')->invoke($obj);
                //保存规则信息
                $this->rules[] = [
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'msg' => $reflectionClass->getMethod('getMessage')->invoke($obj),
                ];
            }
        } catch (\ReflectionException $e) {
            Log::error("获取问题监控数据失败：" . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
        }
    }

    protected function checkWarning(): bool
    {
        if (empty($this->apmWarning['warning_rule'])) return false;
        //匹配数量
        $matchNum = 0;
        //循环判断规则
        foreach ($this->apmWarning['warning_rule'] as $rule) {
            //判断是否有该指标的数据，没有的话跳过
            if (!isset($this->monitorData[$rule['index']])) continue;
            //匹配规则，执行对应计算
            switch ($rule['operator']) {
                case '>':
                    if ($this->monitorData[$rule['index']] > $rule['value']) $matchNum++;
                    break;
                case '<':
                    if ($this->monitorData[$rule['index']] < $rule['value']) $matchNum++;
                    break;
                case '=':
                    if ($this->monitorData[$rule['index']] == $rule['value']) $matchNum++;
                    break;
            }
        }
        //匹配数量等于规则数量，返回true
        if ($matchNum == count($this->apmWarning['warning_rule'])) return true;
        //匹配数量不等于规则数量，返回false
        return false;
    }

    protected function getPersonMessage(): string
    {
        return "线上性能 监控告警 [{$this->apmWarning['name']}][{$this->date}]\nApp名称：{$this->apmWarning['app']['app_name']}\n平台：{$this->platform}\n版本：{$this->version}\n{$this->getRuleMsg("\n")}\n<a href='{$this->getUrl()}'>【查看详情】</a>";
    }

    protected function getGroupMessage(): string
    {
        return "**线上性能 监控告警 [{$this->apmWarning['name']}][{$this->date}]**\nApp名称：{$this->apmWarning['app']['app_name']}\n平台：{$this->platform}\n版本：{$this->version}\n{$this->getRuleMsg("\n")}\n[【查看详情】]({$this->getUrl()})";
    }

    protected function getPhoneMessage(): string
    {
        return "线上性能 监控告警 [{$this->apmWarning['name']}][{$this->date}] App名称：{$this->apmWarning['app']['app_name']} 平台：{$this->platform} 版本：{$this->version} {$this->getRuleMsg(" ")}";
    }

    /**
     * 获取规则信息
     *
     * @param $separator
     * @return string
     */
    protected function getRuleMsg($separator): string
    {
        $msg = [];
        foreach ($this->rules as $rule) {
            $msg[] = "统计时间：{$rule['start_time']}~{$rule['end_time']}{$separator}告警规则：{$rule['msg']}";
        }
        return implode($separator, $msg);
    }
}
