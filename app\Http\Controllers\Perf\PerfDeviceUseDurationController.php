<?php
/**
 * 设备使用时长控制器
 * @desc 设备使用时长控制器
 * <AUTHOR> <EMAIL>
 * @date 2024/01/12
 */

namespace App\Http\Controllers\Perf;

use App\Http\Validation\Perf\DeviceUseDurationValidation;
use App\Model\Perf\DeviceUseDuration;
use Exception;
use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PerfDeviceUseDurationController extends Controller
{
    /**
     * 设备使用时长上报
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recordDeviceUserDuration(Request $request)
    {
        $params = DeviceUseDurationValidation::build()
            ->deviceCode()
            ->duration()
            ->validate();

        try {
            // 获取当前日期
            $date = date('Y-m-d');
            $deviceUseDuration = DeviceUseDuration::query()
                ->where('device_code', $params['device_code'])
                ->where('date', $date)
                ->first();
            if ($deviceUseDuration) {
                $deviceUseDuration->duration += $params['duration'];
            } else {
                $deviceUseDuration = new DeviceUseDuration([
                    'device_code' => $params['device_code'],
                    'duration' => $params['duration'],
                    'date' => $date
                ]);
            }
            $deviceUseDuration->save();
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            Log::error('获取收藏夹列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
