<?php
/**
 * 设备使用时长校验类
 * @desc 设备使用时长校验类
 * <AUTHOR> <EMAIL>
 * @date 2024/01/12
 */

namespace App\Http\Validation\Perf;


use App\Http\Validation\BaseValidation;

/**
 * @method static DeviceUseDurationValidation build()
 */
class DeviceUseDurationValidation extends BaseValidation
{
    /**
     * 设备码
     * @return $this
     */
    public function deviceCode()
    {
        $this->rules['device_code'] = 'required|string';
        return $this;
    }

    /**
     * 使用时长
     * @return $this
     */
    public function duration()
    {
        $this->rules['duration'] = 'required|int';
        return $this;
    }
}
