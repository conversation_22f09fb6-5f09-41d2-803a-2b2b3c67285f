<?php

/**
 * 获取首页筛选数据
 * @desc 获取首页筛选数据
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/07/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;

class ApmHomeSearch extends ApmBase
{
    /**
     * 获取首页筛选数据
     *
     * @return array
     */
    public function getSearch(): array
    {
        $builder = MysqlApmReportList::query()
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->join($this->performance_tag_stat_data_table, "{$this->performance_tag_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->when($this->params['os_type'] ?? null, function ($query) {
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['device_tier'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when($this->params['game_version_code'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when(isset($this->params['is_simulator']), function ($query) {
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when($this->params['quality'] ?? null, function ($query) {
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->when($this->params['director'] ?? null, function ($query) {
                return $query->whereIn("{$this->performance_tag_stat_data_table}.tag", explode(',', $this->params['director']));
            });
        //返回数据
        return [
            'game_version_code' => array_column((clone $builder)
                ->selectRaw("{$this->mysql_apm_report_list_table}.app_version_name as game_version_code")
                ->whereRaw("null_or_empty({$this->mysql_apm_report_list_table}.app_version_name) = 0")
                ->where("{$this->mysql_apm_report_list_table}.app_version_name", '!=', '')
                ->groupBy("{$this->mysql_apm_report_list_table}.app_version_name")
                ->getFromSR(), 'game_version_code'),
            'inner_version' => array_column((clone $builder)
                ->selectRaw("{$this->mysql_apm_report_list_table}.inner_version")
                ->whereRaw("null_or_empty({$this->mysql_apm_report_list_table}.inner_version) = 0")
                ->where("{$this->mysql_apm_report_list_table}.inner_version", '!=', '')
                ->groupBy("{$this->mysql_apm_report_list_table}.inner_version")
                ->getFromSR(), 'inner_version'),
            'quality' => array_column((clone $builder)
                ->selectRaw("{$this->mysql_apm_report_list_table}.quality")
                ->whereRaw("null_or_empty({$this->mysql_apm_report_list_table}.quality) = 0")
                ->where("{$this->mysql_apm_report_list_table}.quality", '!=', '')
                ->groupBy("{$this->mysql_apm_report_list_table}.quality")
                ->getFromSR(), 'quality'),
            'director' => $this->getDirectorList(clone $builder),
        ];
    }

    /**
     * 获取负责人列表
     *
     * @param Builder $builder
     * @return array
     */
    private function getDirectorList($builder)
    {
        $list = $builder->selectRaw("{$this->performance_tag_stat_data_table}.tag")
            ->whereRaw("null_or_empty({$this->performance_tag_stat_data_table}.tag) = 0")
            ->where("{$this->performance_tag_stat_data_table}.tag", '!=', '')
            ->groupBy("{$this->performance_tag_stat_data_table}.tag")
            ->getFromSR();
        $result = [];
        foreach ($list as $item) {
            $tags = explode('|', $item['tag']);
            if (count($tags) <= 1) {
                continue;
            }
            $count = count($tags) - 1;
            for ($i = 0; $i < $count; $i++) {
                $result[$tags[$i]][] = $item['tag'];
            }
        }
        // 处理 result 数据
        foreach ($result as $key => $item) {
            $result[$key] = implode(',', $item);
        }
        // 返回
        return $result;
    }
}
