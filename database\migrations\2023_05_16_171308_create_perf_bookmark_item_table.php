<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePerfBookMarkItemTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('perf_bookmark_item', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->unsignedInteger('bookmark_id')->default(0)->comment('收藏夹ID');
            $table->unsignedBigInteger('report_id')->default(0)->comment('报告ID');

            $table->index('bookmark_id');
            $table->index('report_id');
        });
        \DB::connection('tool')->statement("ALTER TABLE `perf_bookmark_item` comment 'apm-线下性能收藏夹子项表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('perf_bookmark_item');
    }
}
