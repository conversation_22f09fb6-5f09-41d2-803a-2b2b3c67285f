<?php

namespace App\Http\Controllers;

use App\Components\ApiResponse\StatusCode;
use App\Model\Special\Report;
use App\Service\SpecialReportService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SpecialReportController extends Controller
{
    private $report;
    private $reportService;

    public function __construct(Report $report, SpecialReportService $reportService)
    {
        $this->report = $report;
        $this->reportService = $reportService;
    }

    /**
     * 检测新增接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3209
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $input = $request->all();

        $validator = \Validator::make($input, [
            'report' => 'required|file'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        $reportFormat = "json";
        if ($input['report']->getClientOriginalExtension() === $reportFormat) {
            $input['report_url'] = $this->reportService->storeReport($input['report'], $reportFormat);
            try {
                \DB::connection('tool')->beginTransaction();
                $this->report->store($input, true);
                \DB::connection('tool')->commit();
                return $this->response();
            } catch (Exception $e) {
                \Log::error('特效检测新增接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                    $e->getLine());
                \DB::connection('tool')->rollBack();
                return $this->response(StatusCode::C_SYS_EXCAPTION);
            }
        } else {
            return $this->response(StatusCode::C_PARAM_ERROR);
        }
    }

    /**
     * 检测列表接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3220
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        try {
            $page = $input['page'] ?? 1;
            $perPage = $input['per_page'] ?? 15;
            $start_created_at = null;
            $end_created_at = null;
            if (isset($input['start_created_at'], $input['end_created_at'])) {
                $start_created_at = $input['start_created_at']." 00:00:00";
                $end_created_at = $input['end_created_at']." 23:59:59";
            }
            [$list, $total] = $this->reportService->getReportList($input['developer_app_id'], $perPage, $page,
                $start_created_at, $end_created_at);
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            \Log::error('特效检测列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }

    }

    /**
     * 获取报表地址
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3221
     * @param $id
     * @return JsonResponse
     */
    public function getReport($id): JsonResponse
    {
        try {
            $content = $this->report->findOrFail($id, ['developer_app_id', 'report_url', 'created_at']);
            //解析json文件，并把返回的数据合并
            $currentConfig = $this->reportService->getCurrentConfig($content['developer_app_id']);
            $content = array_merge($content->toArray(), $this->reportService->parseJsonFile($content['report_url'], $currentConfig));
             //移除项目ID字段
            unset($content['developer_app_id']);
            //返回
            return $this->response(StatusCode::C_SUCCESS, $content);
        } catch (Exception $e) {
            \Log::error('获取特效检测报告详情接口报错-reportId:' . $id . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 报告更新接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3222
     * @param Request $request
     * @param $reportId
     * @return JsonResponse
     */
    public function updateReport(Request $request, $reportId): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'remark' => 'present|max:255'
        ]);
        if ($validator->fails()) {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }
        try {
            \DB::connection('tool')->beginTransaction();
            $this->report->where('report_id', $reportId)->update(['remark' => $input['remark']]);
            \DB::connection('tool')->commit();
            return $this->response();
        } catch (Exception $e) {
            \Log::error('更新特效报告接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            \DB::connection('tool')->rollBack();
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 报告删除接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3284
     * @param $reportId
     * @return JsonResponse
     */
    public function delete($reportId): JsonResponse
    {
        try {
            $report = $this->report::query()->where('report_id', $reportId)->first();
            if (empty($report)) {
                return $this->response(StatusCode::C_SYS_EXCAPTION);
            }
            $isSuccess = $this->reportService->deleteReport($report->report_url);
            if (!$isSuccess) {
                return $this->response(StatusCode::C_SYS_EXCAPTION);
            }
            $report->delete();
            return $this->response();
        } catch (Exception $e) {
            \Log::error('特效检测删除接口报错-reportId:' . $reportId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
