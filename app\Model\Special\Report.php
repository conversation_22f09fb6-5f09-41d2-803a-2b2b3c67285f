<?php

namespace App\Model\Special;

use App\Model\BaseModel;
use App\Model\ModelTrait;

class Report extends BaseModel
{
    use ModelTrait;

    protected $table = 'special_report';
    protected $primaryKey = 'report_id';

    protected $fillable = [
        'developer_app_id',
        'report_url',
        'remark'
    ];
    public $validateRule = [
        'developer_app_id' => 'required|int',
        'report_url' =>'required|string',
        'remark' =>'string|max:255'
    ];
}
