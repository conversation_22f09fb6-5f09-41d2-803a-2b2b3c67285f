<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
class CreatePerfReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('perf_report', function (Blueprint $table) {
            $table->bigIncrements('report_id')->comment('id');
            $table->string('report_url')->default('')->comment('报表存储路径');
            $table->string('app_name', 255)->default('')->comment('APP名称');
            $table->string('app_icon', 255)->default('')->comment('APP图标');
            $table->string('title', 255)->default('')->comment('名称');
            $table->string('creator', 50)->default('')->comment('创建者');
            $table->string('device', 50)->default('')->comment('设备');
            $table->string('device_model', 50)->default('')->comment('设备型号');
            $table->string('cpu', 50)->default('')->comment('中央处理器');
            $table->string('gpu', 50)->default('')->comment('图形处理器');
            $table->string('system', 20)->default('')->comment('系统类型');
            $table->string('os_version', 20)->default('')->comment('系统版本');
            $table->string('app_version', 20)->default('')->comment('软件版本');
            $table->string('package_name', 50)->default('')->comment('包名');
            $table->unsignedInteger('duration')->default(0)->comment('时长');
            $table->string('remark', 255)->nullable()->comment('备注');
            $table->text('extra')->nullable()->comment('额外信息');
            $table->timestamps();

            $table->index('title');
            $table->index('app_name');
            $table->index('device_model');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('perf_report');
    }
}
