<?php

/**
 * 线下性能检测报告标签控制器
 * @desc 线下性能检测报告标签控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/12/17
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Service\PerfomanceScrore;

class ReCalculatePerformScore
{
    /**
     * 报告数据
     *
     * @var array
     */
    private $reportData;

    /**
     * 登记
     *
     * @var int
     */
    private $level;

    /**
     * ram
     *
     * @var string
     */
    private $ram;

    /**
     * 最大内存峰值
     *
     * @var string
     */
    private $pssMemoryPeak;

    /**
     * 平均10分钟大跳数
     *
     * @var string
     */
    private $avg10BigJank;

    /**
     * 设置报告数据
     *
     * @param $reportData
     * @return $this
     */
    public function setReportData($reportData)
    {
        $this->reportData = $reportData;
        return $this;
    }

    /**
     * 设置等级
     *
     * @param $level
     * @return $this
     */
    public function setLevel($level)
    {
        $this->level = $level;
        return $this;
    }

    /**
     * 设置ram内存
     *
     * @param $ram
     * @return $this
     */
    public function setRam($ram)
    {
        $ramMb = $ram;
        if (empty($ramMb) || $ramMb == 'unknown') {
            $ramMb = '4GB';
        }
        $isMatch = preg_match("/^([0-9]+([.]+[0-9]+)?)?\s?GB$/i", $ramMb, $match);
        if (!$isMatch || floatval($match[1]) == 0) {
            $match[1] = 4;
        }
        $this->ram = floatval($match[1]) * 1024;
        return $this;
    }

    /**
     * 设置pss内存峰值
     *
     * @param $pssMemoryPeak
     * @return $this
     */
    public function setPssMemoryPeak($pssMemoryPeak)
    {
        $this->pssMemoryPeak = $pssMemoryPeak;
        return $this;
    }

    /**
     * 设置平均10帧大跳数
     *
     * @param $avg10BigJank
     * @return $this
     */

    public function setAvg10BigJank($avg10BigJank)
    {
        $this->avg10BigJank = $avg10BigJank;
        return $this;
    }

    /**
     * 获取分数
     *
     * @return array
     */
    public function getScore(): array
    {
        $jankCount = $totalFrameCount = 0;
        foreach ($this->reportData as $dataForSecond) {
            $jankCount += $dataForSecond['fpsInfo']['Jank'];
            $jankCount += $dataForSecond['fpsInfo']['BigJank'];
            $totalFrameCount += count($dataForSecond['fpsInfo']['FrameTimes']);
        }
        $smoothnessScore = (new SmoothnessPerformanceScore($this->level, $jankCount, $totalFrameCount))->getPerformanceScore();
        $maxMemoryScore = (new MaxMemoryPercentPerformanceScore($this->level, $this->pssMemoryPeak, $this->ram))->getPerformanceScore();
        $bigJankScore = (new BigJankPerformanceScore($this->level, $this->avg10BigJank))->getPerformanceScore();
        //返回
        return [
            'smoothness_score' => $smoothnessScore,
            'peak_memory_score' => $maxMemoryScore,
            'big_jank_score' => $bigJankScore,
        ];
    }
}
