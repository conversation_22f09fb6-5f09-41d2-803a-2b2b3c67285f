<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSpecialReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('special_report', function (Blueprint $table) {
            $table->bigIncrements('report_id')->comment('id');
            $table->unsignedMediumInteger('developer_app_id')->default(0)->comment('研发效能APP项目id');
            $table->string('report_url')->default('')->comment('报表存储路径');
            $table->string('remark', 255)->nullable()->comment('备注');
            $table->timestamps();
            $table->index('developer_app_id','idx_developer_app_id','BTREE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('special_report');
    }
}
