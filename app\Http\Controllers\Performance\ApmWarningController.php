<?php

/**
 * 预警管理
 * @desc 预警管理
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/07/22
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\WarningValidation;
use App\Model\Apm\ApmWarning;
use App\Model\Apm\ApmWarningRule;
use App\Service\Apm\Analysis\WarningData;
use App\Service\Apm\Analysis\WarningWeekData;
use App\Service\MonitorConfigChangeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApmWarningController extends Controller
{
    /**
     * 每页显示条数
     *
     * @var int
     */
    const PER_PAGE = 15;

    /**
     * 预警新增、编辑接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3935
     * @param null $warningId
     * @return JsonResponse
     * @throws Exception
     */
    public function store($warningId = null): JsonResponse
    {
        //校验参数
        $params = WarningValidation::build()
            ->developerAppId()->name()->monitorType()->osType()->appVersion()
            ->receivingPerson()->receivingGroup()->receivingPhone()->warningRule()
            ->scheduleTime()->validate();
        $db = DB::connection('tool');
        try {
            $service = null;
            $db->beginTransaction();
            if ($warningId) {
                // 如果 warningId 存在则是编辑
                $warning = ApmWarning::query()->where('warning_id', $warningId)->first();
                // 判断数据库中是否存在该预警
                if (empty($warning)) {
                    return $this->response(StatusCode::C_PARAM_ERROR);
                }
                // 创建监控服务类
                $service = new MonitorConfigChangeService($warning->toArray(), "PerfMate修改预警配置");
            } else {
                $warning = new ApmWarning();
                //状态 默认为开启状态
                $warning['status'] = ApmWarning::START;
                // 创建监控服务类
                $service = new MonitorConfigChangeService(null, "PerfMate创建预警配置");
            }
            $warning['developer_app_id'] = $params['developer_app_id']; //效能后台APPID
            $warning['os_type'] = $params['os_type']; //平台
            $warning['name'] = $params['name']; //预警名称
            $warning['monitor_type'] = $params['monitor_type']; //监控类型
            //如果是大盘监控，需要删除异常预警触发条件
            if ($params['monitor_type'] == ApmWarning::REPORT_MONITOR && $warningId) {
                ApmWarningRule::query()->where('warning_id', $warningId)->delete();
            }
            $warning['receiving_group'] = $params['receiving_group'] ?? []; //接收群地址数组
            $warning['app_version'] = $params['app_version'] ?? []; //APP版本
            $warning['receiving_person'] = $params['receiving_person'] ?? []; //接收人数组
            $warning['receiving_phone'] = $params['receiving_phone'] ?? []; // 接收人手机号数组
            $warning['schedule_time'] = $params['schedule_time'] ?? 86400; // 执行周期
            //保存数据
            $warning->save();
            //存储异常预警触发条件
            (new ApmWarningRule())->batchStore($warning->warning_id, $params['warning_rule'] ?? []);
            //提交事务
            $db->commit();
            // 监控
            $service->monitor($warning->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            Log::error('保存预警接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            $db->rollBack();
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 预警列表接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3936
     * @return JsonResponse
     */
    public function list(): JsonResponse
    {
        $params = WarningValidation::build()->developerAppId()->page()->perPage()->validate();
        try {
            $list = ApmWarning::with('warningRule')->orderBy('created_at', 'desc')
                ->where('developer_app_id', $params['developer_app_id'])
                ->paginate($params['per_page'] ?? self::PER_PAGE, ['warning_id', 'last_warning_time', 'created_at', 'name',
                    'os_type', 'app_version', 'status', 'receiving_person', 'trigger_count', 'monitor_type', 'schedule_time']);
            return $this->response(StatusCode::C_SUCCESS, ['list' => $list->items(), 'total' => $list->total()]);
        } catch (Exception $e) {
            Log::error('预警列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }

    }

    /**
     * 获取预警详情
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3937
     * @param $warningId
     * @return JsonResponse
     */
    public function getContent($warningId): JsonResponse
    {
        try {
            $content = ApmWarning::with('warningRule')->findOrFail($warningId, ['warning_id', 'name', 'os_type',
                'app_version', 'receiving_person', 'receiving_group', 'monitor_type', 'receiving_phone', 'schedule_time']);
            return $this->response(StatusCode::C_SUCCESS, $content);
        } catch (Exception $e) {
            Log::error('获取预警详情接口报错-warningId:' . $warningId . ',原因:' . $e->getMessage() .
                ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除预警接口
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3940
     * @param $warningId
     * @return JsonResponse
     * @throws Exception
     */
    public function delete($warningId): JsonResponse
    {
        //获取数据库连接
        $db = DB::connection('tool');
        try {
            // 创建监控服务类
            $warning = ApmWarning::query()->where('warning_id', $warningId)->first();
            $service = new MonitorConfigChangeService($warning ? $warning->toArray() : null, "PerfMate删除预警配置");
            //开启事务
            $db->beginTransaction();
            //删除预警
            ApmWarning::query()->where('warning_id', $warningId)->delete();
            //删除异常预警触发条件
            ApmWarningRule::query()->where('warning_id', $warningId)->delete();
            //提交事务
            $db->commit();
            // 监控
            $warning && $service->monitor([]);
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            //回滚事务
            $db->rollBack();
            Log::error('删除预警接口报错-warningId:' . $warningId . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 修改预警状态
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3938
     * @param null $warning_id
     * @return JsonResponse
     */
    public function updateStatus($warning_id = null): JsonResponse
    {
        try {
            // 获取预警信息
            $warning = ApmWarning::query()->find($warning_id);
            // 判断数据库中是否存在该预警
            if (empty($warning)) {
                return $this->response(StatusCode::C_PARAM_ERROR);
            }
            // 创建监控服务类
            $service = new MonitorConfigChangeService($warning->toArray(), "PerfMate修改预警状态");
            // 判断预警状态，取反修改
            if ($warning['status'] === ApmWarning::CLOSE) {
                $warning['status'] = ApmWarning::START;
            } else {
                $warning['status'] = ApmWarning::CLOSE;
            }
            $warning->save();
            // 监控
            $service->monitor($warning->toArray());
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            Log::error('修改预警状态接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 获取预警数据
     *
     * @return array
     */
    public function getWarningData(): array
    {
        $params = request()->all();

        // 验证key
        if (empty($params['key']) || $params['key'] != 'f4sMFzXv5RSE7xGWZ8okije9BAVchlUbIudqwNm60YtrK1nCay2OJ3QTLPpD') {
            return [];
        }

        return (new WarningData($params))->getData();
    }

    /**
     * 获取预警周数据
     *
     * @return JsonResponse
     */
    public function getWarningWeekData()
    {
        $params = request()->all();

        // 验证key
        if (empty($params['key']) || $params['key'] != 'yHBYEucdk3729v5KZASmCOixrDIGfRT60hM1gLaNVoQs8zUXje4pqFltnwPb') {
            return $this->response(StatusCode::C_PARAM_INVAILD);
        }

        return $this->response(StatusCode::C_SUCCESS, (new WarningWeekData($params))->getData());
    }
}
