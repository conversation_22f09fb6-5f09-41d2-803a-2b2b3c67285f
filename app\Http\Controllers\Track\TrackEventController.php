<?php

/**
 * 引擎组埋点
 * @desc 引擎组埋点
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025/03/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Track;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Track\TrackEventValidation;
use App\Model\Apps;
use App\Model\MobileDevice\TrackEvent;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class TrackEventController extends Controller
{

    /**
     * 统计
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15706
     * @return JsonResponse
     */
    public function stat(): JsonResponse
    {
        //请求参数校验
        $params = TrackEventValidation::build()
            ->developerAppId()
            ->label()
            ->startDate()
            ->endDate()
            ->validate();

        try {
            // 环比的结束时间就是$params['start_date'] 的前一天
            $proportionEndTime = Carbon::parse($params['start_date'])->subDay()->endOfDay()->toDateTimeString();
            // 要计算 $params['start_date'] 和 $params['end_date'] 相差的天数
            $proportionDays = Carbon::parse($params['start_date'])->diffInDays(Carbon::parse($params['end_date']));
            // 环比开始时间就是 $params['start_date'] 的前 $proportionDays 天
            $proportionStartTime = Carbon::parse($proportionEndTime)->subDays($proportionDays)->startOfDay()->toDateTimeString();
            $dates = [
                'current' => [
                    $params['start_date'],
                    $params['end_date'],
                ],
                'proportion' => [
                    $proportionStartTime,
                    $proportionEndTime,
                ]
            ];
            $result = [];
            foreach ($dates as $key => $date) {
                $result[$key] = TrackEvent::query()
                    ->selectRaw('name, count(1) as count')
                    ->where('developer_app_id', $params['developer_app_id'])
                    ->where('label', $params['label'])
                    ->whereBetween('created_at', [$date[0], $date[1]])
                    ->groupBy('name')
                    ->orderBy('count', 'desc')
                    ->get();
            }

            $data = [];
            $proportionData = array_column(collect($result['proportion'])->toArray(), null, 'name');
            foreach ($result['current'] as $item) {
                $proportion = $proportionData[$item['name']]['count'] ?? 0;
                $proportionVal = $proportion == 0 ? bcmul($item['count'], 100, 2) : bcadd(round(bcdiv(bcsub($item['count'], $proportion, 6), $proportion, 6) * 100, 2), 0, 2);
                $data[] = [
                    'name' => $item['name'],
                    'num' => $item['count'],
                    'proportion' => $proportionVal,
                ];
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $data);
        } catch (Exception $e) {
            Log::error('获取统计数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 全部数据
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/15707
     * @return JsonResponse
     */
    public function all(): JsonResponse
    {
        //请求参数校验
        $params = TrackEventValidation::build()
            ->startDate()
            ->endDate()
            ->appName()
            ->validate();

        try {
            $apps = Apps::query()->where('status', 1)
                ->when(!empty($params['app_name']), function ($query) use ($params) {
                    $query->where('app_name', 'like', '%' . $params['app_name'] . '%');
                })->get()->toArray();
            // 环比的结束时间就是$params['start_date'] 的前一天
            $proportionEndTime = Carbon::parse($params['start_date'])->subDay()->endOfDay()->toDateTimeString();
            // 要计算 $params['start_date'] 和 $params['end_date'] 相差的天数
            $proportionDays = Carbon::parse($params['start_date'])->diffInDays(Carbon::parse($params['end_date']));
            // 环比开始时间就是 $params['start_date'] 的前 $proportionDays 天
            $proportionStartTime = Carbon::parse($proportionEndTime)->subDays($proportionDays)->startOfDay()->toDateTimeString();
            $dates = [
                'current' => [
                    $params['start_date'],
                    $params['end_date'],
                ],
                'proportion' => [
                    $proportionStartTime,
                    $proportionEndTime,
                ]
            ];
            $result = [];
            foreach ($dates as $key => $date) {
                $result[$key] = TrackEvent::query()
                    ->selectRaw('developer_app_id, label, count(1) as count')
                    ->whereIn('developer_app_id', array_column($apps, 'id'))
                    ->whereBetween('created_at', [$date[0], $date[1]])
                    ->groupBy('developer_app_id', 'label')
                    ->orderBy('count', 'desc')
                    ->get();
            }
            $data = [];
            $proportionData = [];
            foreach ($result['proportion'] as $item) {
                $proportionData[$item['developer_app_id']][$item['label']] = $item['count'];
            }
            foreach ($result['current'] as $item) {
                $proportion = $proportionData[$item['developer_app_id']][$item['label']] ?? 0;
                $proportionVal = $proportion == 0 ? bcmul($item['count'], 100, 2) : bcadd(round(bcdiv(bcsub($item['count'], $proportion, 6), $proportion, 6) * 100, 2), 0, 2);
                $data[$item['developer_app_id']][$item['label']] = [
                    'num' => $item['count'],
                    'proportion' => $proportionVal,
                ];
            }
            $newData = [];
            $idApps = array_column($apps, null, 'id');
            foreach ($data as $id => $item) {
                $proportion1 = $proportionData[$id]['美术工具箱'] ?? 0;
                $proportion2 = $proportionData[$id]['优化提效工具'] ?? 0;
                $newData[] = [
                    'app_id' => $id,
                    'app_name' => $idApps[$id]['app_name'],
                    'app_icon' => $idApps[$id]['app_icon'],
                    'data' => [
                        "美术工具箱" => $item['美术工具箱'] ?? [
                            'num' => 0,
                            'proportion' => bcmul(-$proportion1, 100, 2),
                        ],
                        "优化提效工具" => $item['优化提效工具'] ?? [
                            'num' => 0,
                            'proportion' => bcmul(-$proportion2, 100, 2),
                        ],
                    ]
                ];
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, $newData);
        } catch (Exception $e) {
            Log::error('获取全部数据报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
