<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeviceTierToApmDeviceListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('apm')->table('apm_device_list', function (Blueprint $table) {
            $table->unsignedTinyInteger('device_tier')->default(0)->comment('设备等级，0:未知，1：高，2：中，3：低');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('apm')->table('apm_device_list', function (Blueprint $table) {
            $table->dropColumn('device_tier');
        });
    }
}
