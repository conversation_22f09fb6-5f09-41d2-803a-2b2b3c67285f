<?php
/**
 * MysqlService.php
 *
 * User: Dican
 * Date: 2022/7/15
 * Email: <<EMAIL>>
 */

namespace websocketService\dbService;


class MysqlService
{
    /**
     * @var \mysqli
     */
    private static $mysql = null;

    private function __construct()
    {
    }

    /**取得实例
     * @param $cfg
     * @return \mysqli
     */
    public static function getInstance($cfg)
    {
        if (self::$mysql == null) {
            self::$mysql = self::connect($cfg);
        }
        return self::$mysql;
    }

    public static function connect($cfg)
    {
        $cfg = $cfg['connections'];
        $connection = mysqli_connect($cfg['host'], $cfg['username'], $cfg['password'], $cfg['database']);//连接到数据库
        mysqli_query($connection, "set names 'utf8'");//编码转化
        return $connection;
    }

    /**
     * 关闭连接
     */
    public static function close()
    {
        if (self::$mysql != null) {
            self::$mysql->close();
        }
    }
}
