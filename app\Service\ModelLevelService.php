<?php

/**
 * 机型挡位服务类
 */

namespace App\Service;

use App\Model\MobileDevice\DeviceConfig;
use App\Model\MobileDevice\DeviceName;

class ModelLevelService
{
    /**
     * 默认挡位
     */
    const DEFAULT_LEVEL = 2;

    /**
     * 获取实例
     *
     * @return ModelLevelService
     */
    public static function getInstance(): ModelLevelService
    {
        return new static();
    }

    /**
     * 获取机型挡位
     *
     * @param string $deviceModel 设备型号
     * @param string $hardwareModel cpu型号
     * @param $isSimulator
     * @return string
     */
    public function getLevel(string $deviceModel, string $hardwareModel, $isSimulator = DeviceConfig::NO_SIMULATOR): string
    {
        //先用cup型号来查评分 如果没有值再用机型去查
        $hardwareModelName = $this->getSimpleSocModel($hardwareModel);
        //默认值
        $level = null;
        // 根据需求，若传入模拟器字段，则默认为查询型号为MuMu的信息
        if ($isSimulator == DeviceConfig::IS_SIMULATOR) {
            $deviceModel = 'Mumu';
            //若为模拟器，则需要先通过型号查询
            $level = $this->getByDeviceModel($deviceModel);
        }
        //如果没有值用cpu型号去查
        if (empty($level)) {
            $level = $this->getByHardwareName($hardwareModelName);
        }
        // 如果没有值再用机型去查
        if (empty($level)) {
            $level = $this->getByDeviceModel($deviceModel);
        }
        // 如果还是没有值，返回默认值
        if (empty($level)) {
            return self::DEFAULT_LEVEL;
        }
        //返回值
        return $level;
    }

    /**
     * 处理芯片数据
     *
     * @param $soc_str
     * @return string
     */
    protected function getSimpleSocModel($soc_str): string
    {
        /**
         * 输入一个线上获取的soc名称，返回用于查分匹配的简化字符串
         */
        $result = urldecode($soc_str);
        $result = strtolower(str_replace('-', '_', $result));
        if (strpos($result, '(flattened') !== false) {
            $result = str_replace('(flattened device tree)', '', $result);
            $result = str_replace('(flattened device tre', '', $result); // 兼容bmcx
        }
        // Qualcomm
        if (strpos($result, 'qualcomm') !== false || strpos($result, 'snapdragon') !== false) {
            $version_name = '';
            preg_match_all('/qualcomm technologies, inc \d+/', $result, $matches);
            if (count($matches[0]) > 0) {
                preg_match_all('/\d+/', $matches[0][0], $version_matches);
                $version_name = $version_matches[0][0];
            }
            preg_match_all('/snapdragon.?\d+/', $result, $matches);
            if (count($matches[0]) > 0) {
                preg_match_all('/\d+/', $matches[0][0], $version_matches);
                $version_name = $version_matches[0][0];
            }
            if (strlen($version_name) > 0) {
                $result = 'qcom_' . $version_name;
            }
            if (strpos($result, 'qualcomm technologies, inc') !== false) {
                $result = $this->cleanString($result, '/^.*qualcomm technologies, inc/');
                $result = str_replace('.', '', $result);
            } elseif (strpos($result, 'qualcomm') !== false) {
                $result = $this->cleanString($result, '/qualcomm/');
            }
            if (preg_match('/ aie$/', $result)) {
                $result = str_replace(' aie', '', $result);
            }
            if (preg_match('/ 5g$/', $result)) {
                $result = str_replace(' 5g', '', $result);
            }
            if (preg_match('/_plus$/', $result)) {
                $result = str_replace('_plus', 'p', $result);
            }
            if (preg_match('/plus$/', $result)) {
                $result = str_replace('plus', 'p', $result);
            }
            if (preg_match('/^(?=[^\d\s]*$)[^\s]*$/', $result)) {
                $result = 'qcom_' . $result;
            }

        } elseif (strpos($result, 'exynos') !== false) {
            // EXYNOS
            $version_name = '';
            preg_match_all('/\d+$/', $result, $matches);
            if (count($matches[0]) > 0) {
                $version_name = $matches[0][0];
            }
            $result = "exynos$version_name";
        } elseif (strpos($result, 'mt') === 0
            || strpos($result, 'based on mt') !== false
            || strpos($result, 'dimensity') === 0
            || strpos($result, 'helio') === 0) {
            // MediaTek
            $result = str_replace('(eng)', '', $result);
            if (strpos($result, 'dimensity') !== false) {
                $result = str_replace(' ', '_', $result);
            }
            $result = str_replace('helio ', '', $result);
            $result = str_replace('helio_', '', $result);
            if (strpos($result, 'based on') !== false) {
                $result = explode(' ', $result);
                $result = end($result);
            }

        } elseif (strpos($result, 'hisilicon') !== false
            || strpos($result, 'kirin') !== false
            || strpos($result, 'hikay') !== false) {
            // Hisilicon
            $result = str_replace('vendor ', '', $result);
            $result = str_replace('hisilicon ', '', $result);
            $result = str_replace(' ', '', $result);
            if (strpos($result, 'hikay') !== false) {
                $result = str_replace('hikay', 'kirin', $result);
                if (substr($result, -2) == 'q4') {
                    $result = str_replace('q4', '', $result);
                }
            }
            preg_match_all('/\d+/', $result, $matches);
            if (count($matches[0]) == 0) {
                $result = "hi_$result";
            } // Unisoc
            elseif (strpos($result, 'unisoc') !== false ||
                strpos($result, 'spreadtrum') !== false) {
                $result = str_replace('_unisoc', '', $result);
                $result = str_replace('unisoc', '', $result);
                if (strpos($result, 'spreadtrum') !== false) {
                    $result = str_replace('spreadtrum', '', $result);
                    $result = str_replace('board', '', $result);
                }
                $result = trim($result);
            } // Rockchip
            elseif (strpos($result, 'rockchip') !== false) {
                preg_match_all('/rk\d+/', $result, $matches);
                if (count($matches[0]) > 0) {
                    $result = $matches[0][0];
                }
            }
        }
        return trim($result);
    }

    /**
     * 将字符串中正则匹配到的子串删除
     *
     * @param $content
     * @param null $regex
     * @return string
     */
    protected function cleanString($content, $regex = null): string
    {
        // log_utils.info(f'clean_string input {content}')
        $result = $content;
        if ($regex && strlen($regex) > 0) {
            preg_match_all($regex, $content, $matches);
            foreach ($matches[0] as $sub_str) {
                $result = str_replace($sub_str, '', $result);
            }
        }
        // log_utils.info(f'clean_string output {result}')
        return trim($result);
    }

    /**
     * 通过设备CPU内部型号查询挡位
     *
     * @param $hardwareModelName
     * @return int
     */
    protected function getByHardwareName($hardwareModelName): int
    {
        $data = DeviceConfig::query()->where('hardware_model_names', $hardwareModelName)->first();
        if ($data) {
            return $this->convertLevel($data['tier_three'] ?? null);
        }
        return 0;
    }

    /**
     * 通过机型型号查询
     *
     * @param $deviceModel
     * @return int
     */
    protected function getByDeviceModel($deviceModel): int
    {
        $data = DeviceName::query()
            ->where('device_model', $deviceModel)
            ->with(['deviceConfig'])
            ->first();
        if ($data) {
            return $this->convertLevel($data['device_config']['tier_three'] ?? null);
        }
        return 0;
    }

    /**
     * 转换档位
     *
     * @param $level
     * @return int
     */
    protected function convertLevel($level): int
    {
        // 判断是否为空
        if (empty($level)) {
            return self::DEFAULT_LEVEL;
        }

        // 获取档位
        return intval($level);
    }

    /**
     * 获取等级名称
     *
     * @param $level
     * @return string
     */
    public function getLevelText($level): string
    {
        $text = [
            1 => '高档位',
            2 => '中档位',
            3 => '低档位',
        ];

        return $text[$level] ?? '中档位';
    }
}
