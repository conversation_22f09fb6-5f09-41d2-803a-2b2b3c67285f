<?php

namespace App\Service\PerfomanceScrore;

/**
 * 内存使用率评分类
 */
class MaxMemoryPercentPerformanceScore extends PerformanceScoreService implements PerformanceScore
{
    protected $level;
    protected $useMemory;
    protected $totalMemory;

    public function __construct(int $level, $useMemory, $totalMemory)
    {
        $this->level = $level;
        $this->useMemory = $useMemory;
        $this->totalMemory = $totalMemory;
    }

    /**
     * 获取设备总内存，按比例计算分数
     *    峰值等于60%，40*0.7=28
     *    峰值等于30%，40满分
     *    峰值等于90%，0分
     */
    public function getPerformanceScore(): int
    {
        // 获取内存峰值
        $maxMemory = bcmul(bcdiv($this->useMemory, $this->totalMemory, 2), 100, 2);
        // 内存消耗指标
        $memory = self::LEVEL_TO_MEMORY[$this->level];
        // 计算分数
        $score = 28;
        if ($maxMemory == $memory) {
            return $score;
        } elseif ($maxMemory < $memory) {
            $diff = bcsub($memory, $maxMemory, 2);
            // 加分
            $score = bcadd(bcmul(bcmul($diff, 100, 2), 0.004, 4), $score, 2);
            // 最高40分
            return min($score, 40);
        } else {
            $diff = bcsub($maxMemory, $memory, 2);
            // 扣分
            $score = bcsub($score, bcmul(bcmul($diff, 100, 2), 0.0094, 4), 2);
            // 最低0分
            return max($score, 0);
        }
    }
}
