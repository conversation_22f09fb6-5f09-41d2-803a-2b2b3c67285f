<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCheckoutTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('checkout', function (Blueprint $table) {
            $table->bigIncrements('checkout_id');
            $table->unsignedTinyInteger('platform_type')->default(0)->comment('平台类型;1为iOS,2为安卓国内,3为安卓谷歌,4为安卓通用');
            $table->string('name', 64)->comment('文件名');
            $table->string('title', 64)->comment('标题');
            $table->unsignedTinyInteger('type')->default(0)->comment('类型;1为包,2为json');
            $table->string('url', 255)->comment('下载地址');
            $table->unsignedInteger('size')->comment('大小');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态;1为已就绪,2为进行中,3为检测完成,4为检测失败');
            $table->unsignedInteger('estimated_time')->default(0)->comment('预计完成时间');
            $table->mediumText('checkout_report')->nullable()->comment('检测报告');
            $table->timestamps();
            $table->index(['platform_type', 'type']);
            $table->index(['platform_type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('checkout');
    }
}
