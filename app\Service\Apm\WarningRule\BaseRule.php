<?php

namespace App\Service\Apm\WarningRule;

use App\Service\Apm\Performance\ApmTrait;

abstract class BaseRule
{
    use ApmTrait;

    /**
     * 参数
     *
     * @var array
     */
    protected $params;

    /**
     * 结果
     *
     * @var float
     */
    protected $result;

    /**
     * 构造函数
     *
     * @param $developerAppId
     * @param $osType
     * @param $startTime
     * @param $endTime
     * @param $versions
     * @param $value
     */
    public function __construct($developerAppId, $osType, $startTime, $endTime, $versions, $value)
    {
        $this->params = [
            'developer_app_id' => $developerAppId,
            'os_type' => $osType,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'versions' => $versions,
            'value' => $value,
        ];
    }

    /**
     * 获取数据
     *
     * @return float
     */
    public abstract function getData(): float;

    /**
     * 获取报警信息
     *
     * @return string
     */
    public abstract function getMessage(): string;
}
