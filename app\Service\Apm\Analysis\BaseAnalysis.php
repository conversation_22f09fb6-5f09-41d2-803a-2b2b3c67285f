<?php

/**
 * 分析基类
 */

namespace App\Service\Apm\Analysis;

use App\Service\Apm\Performance\ApmTrait;
use Illuminate\Support\Carbon;

abstract class BaseAnalysis
{
    use ApmTrait;

    /**
     * 请求参数
     *
     * @var array
     */
    protected $params;

    /**
     * 开始时间
     *
     * @var string
     */
    protected $startTime;

    /**
     * 结束时间
     *
     * @var string
     */
    protected $endTime;

    /**
     * 排序字段
     *
     * @var string
     */
    protected $sortField = 'num';

    /**
     * 排序类型
     *
     * @var string
     */
    protected $sortType = 'desc';

    /**
     * 分页大小
     *
     * @var int
     */
    protected $perPage = 10;

    /**
     * 构造函数
     *
     * @param $params
     */
    public function __construct($params)
    {
        $this->params = $params;
        // 获取开始时间和结束时间，并对数据进行格式化
        $this->startTime = isset($this->params['start_time']) ? Carbon::parse($this->params['start_time'])->startOfDay()->toDateTimeString() : Carbon::now()->startOfDay()->toDateTimeString();
        $this->endTime = isset($this->params['end_time']) ? Carbon::parse($this->params['end_time'])->endOfDay()->toDateTimeString() : Carbon::now()->endOfDay()->toDateTimeString();
        // 获取排序字段、排序类型和分页大小
        isset($params['sort_field']) && $this->sortField = $params['sort_field'];
        isset($params['sort_type']) && $this->sortType = $params['sort_type'];
        isset($params['limit']) && $this->perPage = $params['limit'];
    }

    /**
     * 获取页码
     *
     * @return float|int
     */
    protected function getPageNum()
    {
        // 计算页码，默认显示第一页
        if (isset($this->params['page'])) {
            $pageNum = ($this->params['page'] - 1) * $this->perPage;
        } else {
            $pageNum = 0;
        }
        return $pageNum;
    }
}
