<?php

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceScoreData;
use App\Service\Apm\Performance\ApmModelMap;

class DeviceModel extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        $list = $this->getStatData();
        $list = $this->sortByScore($list);
        //判断长度是否大于20
        $length = count($list);
        $top = $this->handleData(array_slice($list, 0, 10));
        if ($length > 10) {
            $bottom = $this->handleData(array_slice($list, $length - 10, 10));
        } else {
            $bottom = $top;
        }
        return [
            'top' => $top,
            'bottom' => array_reverse($bottom),
        ];
    }

    /**
     * 获取统计数据
     *
     * @return array
     */
    protected function getStatData(): array
    {
        //获取品牌、型号、平均分
        $selectRaw = <<<COLUMNS
max({$this->mysql_apm_device_list_table}.dev_brand) as dev_brand,
{$this->mysql_apm_device_list_table}.dev_model,
ROUND(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score
COLUMNS;

        return $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw($selectRaw)
            ->groupBy("{$this->mysql_apm_device_list_table}.dev_model") //按型号分组
            ->getFromSR();
    }

    /**
     * 排序
     *
     * @param array $list
     * @param string $column
     * @return array
     */
    protected function sortByScore(array $list, string $column = 'score'): array
    {
        $sort = array_column($list, $column);
        array_multisort($sort, SORT_DESC, $list);
        return $list;
    }

    /**
     * 处理数据
     *
     * @param array $list
     * @return array
     */
    protected function handleData(array $list): array
    {
        $newList = [];
        foreach ($list as $item) {
            $newList[] = [
                'brand' => $item['dev_brand'],  //品牌
                'model' => ApmModelMap::getValue($item['dev_model']),   //型号
                'score' => $item['score'],  //平均分
            ];
        }
        return $newList;
    }
}
