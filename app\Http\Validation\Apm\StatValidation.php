<?php

/**
 * 线上性能大盘概览校验类
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;

/**
 * @method static StatValidation build()
 */
class StatValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): StatValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 开始时间校验
     *
     * @return $this
     */
    public function startTime(): StatValidation
    {
        $this->rules['start_time'] = 'required|date';
        return $this;
    }

    /**
     * 结束时间校验
     *
     * @return $this
     */
    public function endTime(): StatValidation
    {
        $this->rules['end_time'] = 'required|date';
        return $this;
    }

    /**
     * 游戏版本号校验
     *
     * @return $this
     */
    public function gameVersionCode(): StatValidation
    {
        $this->rules['game_version_code'] = 'nullable|string';
        return $this;
    }

    /**
     * 平台校验
     *
     * @return $this
     */
    public function osType(): StatValidation
    {
        $this->rules['os_type'] = 'string';
        return $this;
    }

    /**
     * 是否模拟器校验
     *
     * @return $this
     */
    public function isSimulator(): StatValidation
    {
        $this->rules['is_simulator'] = 'nullable|integer';
        return $this;
    }

    /**
     * 设备挡位校验
     *
     * @return $this
     */
    public function deviceTier(): StatValidation
    {
        $this->rules['device_tier'] = 'nullable|integer';
        return $this;
    }

    /**
     * 资源版本
     *
     * @return $this
     */
    public function innerVersion(): StatValidation
    {
        $this->rules['inner_version'] = 'nullable|string';
        return $this;
    }

    /**
     * 画质
     *
     * @return $this
     */
    public function quality(): StatValidation
    {
        $this->rules['quality'] = 'nullable|string';
        return $this;
    }
}
