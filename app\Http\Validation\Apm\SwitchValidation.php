<?php

/**
 * 线上性能开关校验类
 * @desc 线上性能开关校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation\Apm;

use App\Http\Validation\BaseValidation;
use App\Model\Apm\ApmDeviceList;
use App\Model\Apm\ApmSwitchConfig;
use Illuminate\Validation\Rule;

/**
 * @method static SwitchValidation build()
 */
class SwitchValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): SwitchValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 开关ID校验
     *
     * @return $this
     */
    public function switchId(): SwitchValidation
    {
        $this->rules['switch_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 状态校验
     *
     * @return $this
     */
    public function status(): SwitchValidation
    {
        $this->rules['status'] = [
            'required',
            'integer',
            Rule::in([ApmSwitchConfig::STATUS_ON, ApmSwitchConfig::STATUS_OFF]),
        ];
        return $this;
    }

    /**
     * 每页条数校验
     *
     * @return $this
     */
    public function limit(): SwitchValidation
    {
        $this->rules['limit'] = 'integer|min:10|max:100';
        return $this;
    }

    /**
     * 平台校验
     *
     * @return $this
     */
    public function osType(): SwitchValidation
    {
        $this->rules['os_type'] = [
            'required',
            'integer',
            Rule::in([ApmDeviceList::ANDROID, ApmDeviceList::IOS, ApmDeviceList::PC, ApmDeviceList::HARMONY, ApmDeviceList::MINI]),
        ];
        return $this;
    }

    /**
     * 设备数量校验
     *
     * @return $this
     */
    public function deviceNum(): SwitchValidation
    {
        $this->rules['device_num'] = 'nullable|integer|min:0';
        return $this;
    }

    /**
     * 开始时间校验
     *
     * @return $this
     */
    public function startTime(): SwitchValidation
    {
        $this->rules['start_time'] = 'nullable|date_format:H:i:s';
        return $this;
    }

    /**
     * 结束时间校验
     *
     * @return $this
     */
    public function endTime(): SwitchValidation
    {
        $this->rules['end_time'] = 'nullable|date_format:H:i:s';
        return $this;
    }

    /**
     * 包名校验
     *
     * @return $this
     */
    public function packageName(): SwitchValidation
    {
        $this->rules['package_name'] = 'nullable|string';
        return $this;
    }

    /**
     * 页码校验
     *
     * @return $this
     */
    public function page(): SwitchValidation
    {
        $this->rules['page'] = 'integer|min:1';
        return $this;
    }

    /**
     * 包名模糊匹配
     *
     * @return $this
     */
    public function enablePackBlur(): SwitchValidation
    {
        $this->rules['enable_pack_blur'] = 'nullable|integer';
        return $this;
    }
}
