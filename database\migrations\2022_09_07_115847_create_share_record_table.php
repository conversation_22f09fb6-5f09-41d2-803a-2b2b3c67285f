<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShareRecordTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->create('share_record', function (Blueprint $table) {
            $table->bigIncrements('record_id');
            $table->unsignedTinyInteger('type')->nullable(0)->comment('业务类型 1为检测工具');
            $table->unsignedBigInteger('info_id')->comment('业务id');
            $table->unsignedBigInteger('user_id')->comment('分享人id');
            $table->unsignedInteger('limit')->default(0)->comment('有效期 单位为分钟 0则永久');
            $table->timestamps();
            $table->index(['info_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->dropIfExists('share_record');
    }
}
