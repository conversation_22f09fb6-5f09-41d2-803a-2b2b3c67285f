<?php

namespace App\Components\ApiResponse;

use DomainException;
use Illuminate\Support\Facades\App;

/**
 * 状态码
 */
class StatusCode
{
    const C_SUCCESS = 0;
    //登录、权限公共模块状态码 1开头
    const C_PARAM_INVAILD = 1000;
    const C_PARAM_ERROR = 1001;
    const C_LOGIN_INVAILD = 1002;
    const C_LOGIN_ERROR = 1003;
    const C_NO_HTTPS = 1004;
    const C_SYS_EXCAPTION = 1005;
    const C_PERMISSION_ERROR = 1006;
    const C_STORE_ERROR = 1007;
    const C_LOGIN_NO_PASSWORD_NAME = 1008;
    const C_LOGIN_CAPTCHA_ERROR = 1009;
    const C_LOGIN_FILE_TYPE_ERROR = 1010;
    const C_ILLEGAL_OPERATE = 1011;
    const C_EXIST_DATA = 1012;

    //人员模块状态码 11开头
    const C_PASSWORD_ERROR = 1100;
    //app模块状态码 12开头
    const C_APP_NAME_REUSE = 1200;
    const C_APP_ID_REQUIRE = 1201;

    //推送模块 2开头

    //用户分群状态码 21开头
    const C_GROUP_NAME_REPETITION = 2100;
    //标签模块状态码 22开头
    const C_TAG_NAME_REPETITION = 2200;
    //集成配置模块状态码 23开头
    const C_PUSH_CONFIG_NAME_REPETITION = 2300;

    //云构建模块 3开头
    //流水线模块状态码 31开头
    const C_FLOW_NAME_REPETITION = 3100;
    const C_FLOW_DELETE_ERROR = 3101;
    //流水线凭据状态码 32开头
    const C_FLOW_CREDENTIAL_NAME_REPETITION = 3200;
    //环境变量模块状态码 33开头
    const C_FLOW_ENV_NAME_REPETITION = 3300;
    //流水线记录模块状态码 34开头

    //热更新后台 4开头
    //SDK版本模块状态码 41开头
    const C_PATCH_VERSION_REPETITION = 4100;

    //工具后台 5开头
    //阈值配置模块状态码 53开头
    const C_THRESHOLD_CONFIG_NAME_REPETITION = 5300;
    const C_THRESHOLD_CONFIG_UPDATE_ERROR = 5310;
    /**
     * 获取状态码对应错误提示
     * @param $code
     * @return bool
     */
    public static function getErrorMessage($code)
    {
        return self::$codeMessage[$code] ?? '--';
    }

    public static $codeMessage= [
        self::C_SUCCESS => 'success',
        //系统错误
        self::C_PARAM_INVAILD => '缺少必传参数',
        self::C_PARAM_ERROR => '提交数据格式错误',
        self::C_LOGIN_INVAILD => '账号未登录',
        self::C_LOGIN_ERROR => '登录失败，账号密码不匹配',
        self::C_SYS_EXCAPTION => '系统繁忙，请稍后重试',
        self::C_NO_HTTPS => '非https协议',
        self::C_PERMISSION_ERROR => '非法获取权限',
        self::C_STORE_ERROR => '保存失败',
        self::C_LOGIN_NO_PASSWORD_NAME => '账号密码参数必传',
        self::C_LOGIN_CAPTCHA_ERROR => '验证码验证失败',
        self::C_LOGIN_FILE_TYPE_ERROR => '上传文件的类型错误',
        self::C_ILLEGAL_OPERATE => '非法操作数据',
        self::C_EXIST_DATA => '已存在相应数据',

        self::C_PASSWORD_ERROR => '密码错误',
        self::C_APP_NAME_REUSE => 'app名字重复',
        self::C_APP_ID_REQUIRE => 'appId必传',

        self::C_THRESHOLD_CONFIG_UPDATE_ERROR => '阈值配置更新失败',

        self::C_GROUP_NAME_REPETITION => '群组名称已存在',
        self::C_TAG_NAME_REPETITION => '标签名称已存在',
        self::C_PUSH_CONFIG_NAME_REPETITION => '包名已存在',
        self::C_THRESHOLD_CONFIG_NAME_REPETITION => '阈值配置名称已存在',

        self::C_FLOW_NAME_REPETITION => '流水线名称已存在',
        self::C_FLOW_DELETE_ERROR => '任务构建中，请先终止任务',
        self::C_FLOW_CREDENTIAL_NAME_REPETITION => '凭据名称已存在',
        self::C_FLOW_ENV_NAME_REPETITION => '环境变量名称已存在',

        self::C_PATCH_VERSION_REPETITION => '版本号已存在',
    ];


}
