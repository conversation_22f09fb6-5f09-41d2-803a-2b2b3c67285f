<?php

/**
 * 通用控制器
 * @desc 通用控制器
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/11/13
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;


use App\Components\ApiResponse\StatusCode;
use App\Model\ShareRecord;
use App\Service\ShareService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class CommonController extends Controller
{
    /**
     * 获取分享签名
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2547
     * @param $infoId
     * @param Request $request
     * @return JsonResponse
     */
    public function share($infoId, Request $request): JsonResponse
    {
        try {
            $this->validate($request, ['type' => Rule::in(ShareRecord::TYPE), 'limit' => 'nullable|integer']);

            $record = new ShareRecord();
            $record->type = $request->input('type', ShareRecord::TYPE_CHECKOUT);
            $record->info_id = $infoId;
            $record->user_id = \Auth::id();
            $record->limit = $request->input('limit', 0);
            $record->save();
            return $this->response(0, ['record_id' => $record->record_id, 'sign' => $record->getSign()]);
        } catch (\Exception $e) {
            \Log::error('发起检测接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 分享报告
     *
     * @doc https://developer.shiyuegame.com/showdoc/web/#/129/12179
     * @param Request $request
     * @return JsonResponse
     */
    public function shareReport(Request $request): JsonResponse
    {
        try {
            $this->validate($request, [ShareService::SHARE_REFERER_KEY => 'required|string']);
            return $this->response(StatusCode::C_SUCCESS, ['share_token' => ShareService::create($request)->build()]);
        } catch (\Exception $e) {
            //判断是否是验证异常
            if ($e instanceof ValidationException) {
                return $this->response(StatusCode::C_PARAM_INVAILD, [], $e->getMessage());
            }
            \Log::error('分享接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
