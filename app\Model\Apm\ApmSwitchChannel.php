<?php

/**
 * 开关渠道
 */

namespace App\Model\Apm;

use App\Model\ModelTrait;
use Illuminate\Database\Eloquent\Model;

class ApmSwitchChannel extends Model
{
    use ModelTrait;

    public $connection = "apm";

    protected $table = 'apm_switch_channel';

    protected $primaryKey = 'id';

    protected $guarded = [];

    /**
     * 官方渠道
     *
     * @var string
     */
    const SHI_YUE_CHANNEL = 'shiyue';

    /**
     * 对应融合分包的APPID
     *
     * @var array
     */
    const RH_PACK_DISTRIBUTE_PROJECT_ID_MAP_TEST = [
        1 => 11,
        2 => 107,
        3 => 112,
        4 => 100,
        5 => 115,
        6 => 0,
        7 => 0,
        9 => 0,
        10 => 0,
        11 => 0,
        12 => 0,
        15 => 117,
        16 => 0,
        17 => 0,
        18 => 116,
    ];

    /**
     * 对应融合分包的APPID
     *
     * @var array
     */
    const RH_PACK_DISTRIBUTE_PROJECT_ID_MAP_PROD = [
        1 => 0,
        2 => 11,
        3 => 11,
        5 => 117,
        6 => 107,
        7 => 0,
        8 => 115,
        9 => 0,
        10 => 0,
        11 => 0,
        12 => 0,
        13 => 116,
        15 => 112,
        16 => 111,
        17 => 11,
    ];
}

