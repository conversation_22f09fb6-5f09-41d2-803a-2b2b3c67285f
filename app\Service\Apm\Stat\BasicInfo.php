<?php

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceScoreData;

class BasicInfo extends BaseStat
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData(): array
    {
        //获取报告数量、设备数量、平均分、低于标准分数量
        $selectRaw = <<<COLUMNS
count({$this->performance_score_data_table}.session_id) as score_num,
count(distinct {$this->mysql_apm_report_list_table}.dev_str) as dev_num,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as average_score,
sum(case when {$this->performance_score_data_table}.all_score < {$this->standardScore} then 1 else 0 end) as low_score_num,
round((sum({$this->performance_stat_data_table}.max_used_memory) / count({$this->performance_stat_data_table}.session_id)) / (1024 * 1024), 2) as average_memory,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter
COLUMNS;

        $res = $this->getCommonBuilder(new PerformanceScoreData)
            ->selectRaw($selectRaw)
            ->firstFromSR();
        //处理结果
        $result = $res ?? [
            'dev_num' => 0, //设备数量
            'score_num' => 0, //报告数量
            'average_score' => 0, //平均分
            'low_score_num' => 0, //低于标准分数量
            'average_memory' => 0, //内存峰值均值
            'stutter' => 0, //卡顿率
        ];
        //返回结果
        return $this->handleListData($result);
    }
}
