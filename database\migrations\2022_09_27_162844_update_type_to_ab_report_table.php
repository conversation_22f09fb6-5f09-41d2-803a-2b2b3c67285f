<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTypeToAbReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('ab_report', function (Blueprint $table) {
            //
            $table->unsignedInteger('redundancy_resource_size')->default(0)
                ->comment('冗余资源占用大小')->change();
            $table->unsignedInteger('all_ab_num')->default(0)
                ->comment('总AB数')->change();
            $table->unsignedInteger('dependence_ab_num')->default(0)
                ->comment('有外部依赖的AB数')->change();
            $table->unsignedInteger('redundancy_ab_num')->default(0)
                ->comment('含冗余资源的AB数')->change();
            $table->unsignedInteger('all_resource_num')->default(0)
                ->comment('总资源数')->change();
            $table->unsignedInteger('redundancy_resource_num')->default(0)
                ->comment('冗余资源数')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('ab_report', function (Blueprint $table) {
            //

        });
    }
}
