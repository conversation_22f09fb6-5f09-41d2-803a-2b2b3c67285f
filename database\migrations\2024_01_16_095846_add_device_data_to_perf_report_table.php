<?php

/**
 * 线下性能检测增加设备信息字段的迁移文件
 * @desc 线下性能检测增加设备信息字段的迁移文件
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeviceDataToPerfReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('perf_report', function (Blueprint $table) {
            $table->string('device_data', 2550)->default('')->comment('设备数据');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('perf_report', function (Blueprint $table) {
            $table->dropColumn('device_data');
        });
    }
}
