<?php

namespace App\Service\Apm\Chart;

use App\Model\Apm\StarRocks\PerformanceData;

class FrameTimeInfo<PERSON>hart extends BaseChart
{
    protected function getBuilder()
    {
        return PerformanceData::query()
            ->select([
                'frame_times',
                'game_info_ts',
                'tags_info',
                'app_state',
                'perf_data_ts',
            ])
            ->where('session_id', $this->reportId);
    }

    protected function handleData(): array
    {
        $list = [];
        foreach ($this->result as $item) {
            $frameTimes = json_decode($item['frame_times'], true) ?? [];    //帧率信息
            $list[$item['perf_data_ts']]['frameTimeInfo'][] = [ //按照时间戳分组
                'frameTimes' => $frameTimes,    //帧率信息
                'ts' => $item['game_info_ts'],  //游戏时间戳
            ];
            $list[$item['perf_data_ts']]['tagsInfo'] = json_decode($item['tags_info'], true) ?? []; //标签信息
            $list[$item['perf_data_ts']]['ts'] = $item['perf_data_ts']; //时间戳
            $list[$item['perf_data_ts']]['appState'] = $item['app_state'];  //app状态
        }

        return array_values($list);
    }
}
