<?php

/**
 * 功能说明:
 *  型号映射
 */

namespace App\Service\Apm\Performance;

class ApmModelMap
{
    const MAP = [
        "iPhone1,1" => "iPhone 2G (A1203)",
        "iPhone1,2" => "iPhone 3G (A1241/A1324)",
        "iPhone2,1" => "iPhone 3GS (A1303/A1325)",
        "iPhone3,1" => "iPhone 4 (A1332)",
        "iPhone3,2" => "iPhone 4 (A1332)",
        "iPhone3,3" => "iPhone 4 (A1349)",
        "iPhone4,1" => "iPhone 4S (A1387/A1431)",
        "iPhone5,1" => "iPhone 5 (A1428)",
        "iPhone5,2" => "iPhone 5 (A1429/A1442)",
        "iPhone5,3" => "iPhone 5C (A1456/A1532)",
        "iPhone5,4" => "iPhone 5C (A1507/A1516/A1526/A1529)",
        "iPhone6,1" => "iPhone 5S (A1453/A1533)",
        "iPhone6,2" => "iPhone 5S (A1457/A1518/A1528/A1530)",
        "iPhone7,1" => "iPhone 6 Plus (A1522/A1524)",
        "iPhone7,2" => "iPhone 6 (A1549/A1586)",
        "iPhone8,1" => "iPhone 6s",
        "iPhone8,2" => "iPhone 6s Plus",
        "iPhone8,4" => "iPhone SE (GSM)",
        "iPhone9,1" => "iPhone 7",
        "iPhone9,2" => "iPhone 7 Plus",
        "iPhone9,3" => "iPhone 7",
        "iPhone9,4" => "iPhone 7 Plus",
        "iPhone10,1" => "iPhone 8",
        "iPhone10,4" => "iPhone 8",
        "iPhone10,2" => "iPhone 8 Plus",
        "iPhone10,5" => "iPhone 8 Plus",
        "iPhone10,3" => "iPhone X Global",
        "iPhone10,6" => "iPhone X GSM",
        "iPhone11,2" => "iPhoneXs",
        "iPhone11,4" => "iPhoneXs Max",
        "iPhone11,6" => "iPhoneXs Max",
        "iPhone11,8" => "iPhone Xʀ",
        "iPhone12,1" => "iPhone 11",
        "iPhone12,3" => "iPhone 11 Pro",
        "iPhone12,5" => "iPhone 11 Pro Max",
        "iPhone12,8" => "iPhone SE 2nd Gen",
        "iPhone13,1" => "iPhone 12 Mini",
        "iPhone13,2" => "iPhone 12",
        "iPhone13,3" => "iPhone 12 Pro",
        "iPhone13,4" => "iPhone 12 Pro Max",
        "iPhone14,2" => "iPhone 13 Pro",
        "iPhone14,3" => "iPhone 13 Pro Max",
        "iPhone14,4" => "iPhone 13 Mini",
        "iPhone14,5" => "iPhone 13",
        "iPhone14,6" => "iPhone SE 3rd Gen",
        "iPhone14,7" => "iPhone 14",
        "iPhone14,8" => "iPhone 14 Plus",
        "iPhone15,2" => "iPhone 14 Pro",
        "iPhone15,3" => "iPhone 14 Pro Max",
        "iPhone15,4" => "iPhone 15",
        "iPhone15,5" => "iPhone 15 Plus",
        "iPhone16,1" => "iPhone 15 Pro",
        "iPhone16,2" => "iPhone 15 Pro Max",
        "iPhone17,1" => "iPhone 16 Pro",
        "iPhone17,2" => "iPhone 16 Pro Max",
        "iPhone17,3" => "iPhone 16",
        "iPhone17,4" => "iPhone 16 Plus",
        "iPod1,1" => "iPod Touch 1G (A1213)",
        "iPod2,1" => "iPod Touch 2G (A1288)",
        "iPod3,1" => "iPod Touch 3G (A1318)",
        "iPod4,1" => "iPod Touch 4G (A1367)",
        "iPod5,1" => "iPod Touch 5G (A1421/A1509)",
        "iPad1,1" => "iPad 1G (A1219/A1337)",
        "iPad2,1" => "iPad 2 (A1395)",
        "iPad2,2" => "iPad 2 (A1396)",
        "iPad2,3" => "iPad 2 (A1397)",
        "iPad2,4" => "iPad 2 (A1395+New Chip)",
        "iPad2,5" => "iPad Mini 1G (A1432)",
        "iPad2,6" => "iPad Mini 1G (A1454)",
        "iPad2,7" => "iPad Mini 1G (A1455)",
        "iPad3,1" => "iPad 3 (A1416)",
        "iPad3,2" => "iPad 3 (A1403)",
        "iPad3,3" => "iPad 3 (A1430)",
        "iPad3,4" => "iPad 4 (A1458)",
        "iPad3,5" => "iPad 4 (A1459)",
        "iPad3,6" => "iPad 4 (A1460)",
        "iPad4,1" => "iPad Air (A1474)",
        "iPad4,2" => "iPad Air (A1475)",
        "iPad4,3" => "iPad Air (A1476)",
        "iPad4,4" => "iPad Mini 2G (A1489)",
        "iPad4,5" => "iPad Mini 2G (A1490)",
        "iPad4,6" => "iPad Mini 2G (A1491)",
        "iPad4,7" => "iPad Mini 3",
        "iPad4,8" => "iPad Mini 3",
        "iPad4,9" => "iPad Mini 3",
        "iPad5,1" => "iPad Mini 4",
        "iPad5,2" => "iPad Mini 4",
        "iPad5,3" => "iPad Air 2",
        "iPad5,4" => "iPad Air 2",
        "iPad6,3" => "iPad Pro 9.7",
        "iPad6,4" => "iPad Pro 9.7",
        "iPad6,7" => "iPad Pro 12.9",
        "iPad6,8" => "iPad Pro 12.9",
        "iPad6,11" => "iPad 2017",
        "iPad6,12" => "iPad 2017",
        "iPad7,1" => "iPad Pro 2nd",
        "iPad7,2" => "iPad Pro 2nd",
        "iPad7,3" => "iPad Pro 10.5",
        "iPad7,4" => "iPad Pro 10.5",
        "iPad7,5" => "iPad 6th",
        "iPad7,6" => "iPad 6th",
        "iPad7,11" => "iPad 7",
        "iPad7,12" => "iPad 7",
        "iPad8,1" => "iPad Pro 3rd 11",
        "iPad8,2" => "iPad Pro 3rd 11",
        "iPad8,3" => "iPad Pro 3rd 11",
        "iPad8,4" => "iPad Pro 3rd 11",
        "iPad8,5" => "iPad Pro 3rd 12.9",
        "iPad8,6" => "iPad Pro 3rd 12.9",
        "iPad8,7" => "iPad Pro 3rd 12.9",
        "iPad8,8" => "iPad Pro 3rd 12.9",
        "iPad8,9" => "iPad Pro 4",
        "iPad8,10" => "iPad Pro 4",
        "iPad8,11" => "iPad Pro 4",
        "iPad8,12" => "iPad Pro 4",
        "iPad11,1" => "iPad Mini 2019",
        "iPad11,2" => "iPad Mini 2019",
        "iPad11,3" => "iPad Air 2019",
        "iPad11,4" => "iPad Air 2019",
        "iPad11,5" => "iPad Air 2019",
        "iPad11,6" => "iPad 8",
        "iPad11,7" => "iPad 8",
        "iPad12,1" => "iPad 9",
        "iPad12,2" => "iPad 9",
        "iPad14,1" => "iPad Mini 6",
        "iPad14,2" => "iPad Mini 6",
        "iPad13,1" => "iPad Air 4",
        "iPad13,2" => "iPad Air 4",
        "iPad13,4" => "iPad Pro 5",
        "iPad13,5" => "iPad Pro 5",
        "iPad13,6" => "iPad Pro 5",
        "iPad13,7" => "iPad Pro 5",
        "iPad13,8" => "iPad Pro 5",
        "iPad13,9" => "iPad Pro 5",
        "iPad13,10" => "iPad Pro 5",
        "iPad13,11" => "iPad Pro 5",
        "iPad13,16" => "iPad Air 5",
        "iPad13,17" => "iPad Air 5",
        "iPad13,18" => "iPad 10",
        "iPad13,19" => "iPad 10",
        "iPad14,3" => "iPad Pro 4",
        "iPad14,4" => "iPad Pro 4",
        "iPad14,5" => "iPad Pro 6",
        "iPad14,6" => "iPad Pro 6",
        "iPad14,8" => "iPad Air 6",
        "iPad14,9" => "iPad Air 6",
        "iPad14,10" => "iPad Air 7",
        "iPad14,11" => "iPad Air 7",
        "iPad16,3" => "iPad Pro 5",
        "iPad16,4" => "iPad Pro 5",
        "iPad16,5" => "iPad Pro 7",
        "iPad16,6" => "iPad Pro 7",
        "AppleTV2,1" => "Apple TV 2",
        "AppleTV3,1" => "Apple TV 3",
        "AppleTV3,2" => "Apple TV 3",
        "AppleTV5,3" => "Apple TV 4",
        "i386" => "iPhone Simulator",
        "x86_64" => "iPhone Simulator",
    ];

    /**
     * 获取设备型号
     *
     * @param $value
     * @return string
     */
    public static function getValue($value): string
    {
        return self::MAP[$value] ?? $value;
    }


    /**
     * 获取反转的设备型号
     *
     * @param $value
     * @return string
     */
    public static function getReverseValue($value): string
    {
        $map = array_flip(self::MAP);
        return $map[$value] ?? $value;
    }
}
