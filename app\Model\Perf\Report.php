<?php

/**
 * 线下性能检测报告模型
 * @desc 线下性能检测报告模型
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/01/16
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Model\Perf;

use App\Model\BaseModel;
use App\Model\ModelTrait;

class Report extends BaseModel
{
    use ModelTrait;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'perf_report';

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'report_id';

    /**
     * 可写入的字段
     *
     * @var array
     */
    protected $fillable = [
        'app_name',
        'app_icon',
        'report_url',
        'title',
        'creator',
        'device',
        'device_model',
        'cpu',
        'gpu',
        'system',
        'os_version',
        'app_version',
        'package_name',
        'duration',
        'remark',
        'extra',
        'upload_id',
        'serial',
        'ram',
        'device_data',
    ];

    /**
     * 验证规则
     *
     * @var array
     */
    public $validateRule = [
        'title'        => 'required|string|max:255',
        'app_name'     => 'required|string|max:255',
        'creator'      => 'string|max:255',
        'device'       => 'required|string|max:255',
        'device_model' => 'required|string|max:255',
        'system'       => 'required|string|in:android,ios,simulator,tablet,windows,macos,harmony',
        'cpu'          => 'required|string|max:255',
        'gpu'          => 'required|string|max:255',
        'os_version'   => 'required|string|max:255',
        'app_version'  => 'required|string|max:255',
        'package_name' => 'required|string|max:255',
        'duration'     => 'required|integer',
        'remark'       => 'string|max:255',
        'extra'        => 'string',
        'device_data'  => 'json',
    ];

    /**
     * 设备类型-安卓
     *
     * @var string
     */
    const ANDROID = 'android';

    /**
     * 设备类型-苹果
     *
     * @var string
     */
    const IOS = 'ios';
}
