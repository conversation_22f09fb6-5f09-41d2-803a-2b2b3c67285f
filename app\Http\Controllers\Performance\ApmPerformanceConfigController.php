<?php

/**
 * 性能全局配置
 * @desc 性能全局配置
 * <AUTHOR> chen<PERSON><PERSON><EMAIL>
 * @date 2023/11/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\ConfigValidation;
use App\Model\Apm\ApmGlobalConfig;
use App\Service\MonitorConfigChangeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApmPerformanceConfigController extends Controller
{
    /**
     * 获取配置
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3623
     * @param Request $request
     * @return JsonResponse
     */
    public function get(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ConfigValidation::build()
            ->developerAppId()
            ->validate();

        try {
            //获取数据
            $config = ApmGlobalConfig::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->value('config');
            // 判断是否为空
            if (empty($config)) {
                $config = ApmGlobalConfig::DEFAULT_VALUE;
            } else {
                // 不会空则先覆盖默认配置
                $config = json_decode($config, true);
                $config = array_merge(json_decode(ApmGlobalConfig::DEFAULT_VALUE, true), $config);
                $config = json_encode($config);
            }
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['config' => $config]);
        } catch (Exception $e) {
            \Log::error('获取配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 编辑配置
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3624
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request): JsonResponse
    {
        //请求参数校验
        $params = ConfigValidation::build()
            ->developerAppId()
            ->config()
            ->validate();

        try {
            // 创建监控服务类
            $config = ApmGlobalConfig::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->first();
            $service = new MonitorConfigChangeService($config ? $config->toArray() : ['developer_app_id' => $params['developer_app_id'], 'config' => ApmGlobalConfig::DEFAULT_VALUE], "PerfMate修改阈值配置");
            // 更新或者创建
            $config = ApmGlobalConfig::query()
                ->updateOrCreate(
                    ['developer_app_id' => $params['developer_app_id']],
                    ['config' => $params['config']]
                );
            //刷新缓存
            ApmGlobalConfig::flushCache($params['developer_app_id'], $params['config']);
            // 监控
            $service->monitor($config->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('编辑配置接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
