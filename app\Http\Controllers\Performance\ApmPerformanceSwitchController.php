<?php

/**
 * 性能开关
 * @desc 性能开关
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2023/11/02
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers\Performance;

use App\Components\ApiResponse\StatusCode;
use App\Http\Controllers\Controller;
use App\Http\Validation\Apm\SwitchValidation;
use App\Model\Apm\ApmSwitchConfig;
use App\Service\MonitorConfigChangeService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ApmPerformanceSwitchController extends Controller
{
    /**
     * 每页显示条数
     *
     * @var int
     */
    const PER_PAGE = 10;

    /**
     * 开关列表
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3489
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->developerAppId()
            ->limit()
            ->page()
            ->validate();

        try {
            //获取数据
            $res = ApmSwitchConfig::query()
                ->where('developer_app_id', $params['developer_app_id'])
                ->orderByDesc('id')
                ->paginate($params['limit'] ?? self::PER_PAGE);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS, ['list' => $res->items(), 'total' => $res->total()]);
        } catch (Exception $e) {
            \Log::error('获取开关列表报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 添加开关
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3492
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->developerAppId()->osType()->deviceNum()->startTime()->endTime()->status()->packageName()
            ->enablePackBlur()->validate();

        try {
            //添加开关
            $config = ApmSwitchConfig::query()->create([
                'developer_app_id' => $params['developer_app_id'],
                'os_type' => $params['os_type'],
                'device_num' => $params['device_num'] ?? 0,
                'start_time' => $params['start_time'] ?? '00:00:00',
                'end_time' => $params['end_time'] ?? '23:59:59',
                'status' => $params['status'],
                'package_name' => $params['package_name'] ?? '',
                'enable_pack_blur' => $params['enable_pack_blur'] ?? 0,
            ]);
            //刷新缓存
            ApmSwitchConfig::flushCache($config->developer_app_id);
            // 监控更改
            (new MonitorConfigChangeService(null, "PerfMate添加开关配置"))->monitor($config->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('添加开关接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 编辑开关
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3494
     * @param Request $request
     * @return JsonResponse
     */
    public function edit(Request $request): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->switchId()->osType()->deviceNum()->startTime()->endTime()->status()->packageName()
            ->enablePackBlur()->validate();

        try {
            $config = ApmSwitchConfig::query()->find($params['switch_id']);
            //判断是否存在
            if (empty($config)) {
                return $this->response(StatusCode::C_PARAM_ERROR);
            }
            // 创建监控服务类
            $service = new MonitorConfigChangeService($config->toArray(), "PerfMate修改开关配置");
            //修改开关
            $config->update([
                'os_type' => $params['os_type'],
                'device_num' => $params['device_num'] ?? 0,
                'start_time' => $params['start_time'] ?? '00:00:00',
                'end_time' => $params['end_time'] ?? '23:59:59',
                'status' => $params['status'],
                'package_name' => $params['package_name'] ?? '',
                'enable_pack_blur' => $params['enable_pack_blur'] ?? 0,
            ]);
            //刷新缓存
            ApmSwitchConfig::flushCache($config->developer_app_id);
            // 监控
            $service->monitor($config->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('编辑开关接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 修改状态
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3495
     * @param Request $request
     * @return JsonResponse
     */
    public function status(Request $request): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()
            ->switchId()
            ->status()
            ->validate();

        try {
            $config = ApmSwitchConfig::query()->find($params['switch_id']);
            //判断是否存在
            if (empty($config)) {
                return $this->response(StatusCode::C_PARAM_ERROR);
            }
            // 创建监控服务类
            $service = new MonitorConfigChangeService($config->toArray(), "PerfMate修改开关状态");
            //修改状态
            $config->update(['status' => $params['status']]);
            //刷新缓存
            ApmSwitchConfig::flushCache($config->developer_app_id);
            // 监控
            $service->monitor($config->toArray());
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('修改状态接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }

    /**
     * 删除开关
     *
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=3496
     * @param Request $request
     * @return JsonResponse
     */
    public function del(Request $request): JsonResponse
    {
        //请求参数校验
        $params = SwitchValidation::build()->switchId()->validate();

        try {
            //删除开关配置
            $config = ApmSwitchConfig::query()->where('id', $params['switch_id'])->first();
            //检测状态
            if (empty($config) || $config->status == ApmSwitchConfig::STATUS_ON) {
                return $this->response(StatusCode::C_PARAM_ERROR, [], '请先关闭开关');
            }
            // 创建监控服务类
            $service = new MonitorConfigChangeService($config->toArray(), "PerfMate删除开关配置");
            //删除开关
            $config->delete();
            // 监控
            $service->monitor([]);
            //返回数据
            return $this->response(StatusCode::C_SUCCESS);
        } catch (Exception $e) {
            \Log::error('删除开关接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(StatusCode::C_SYS_EXCAPTION);
        }
    }
}
