<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRemarkToRealMachineReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('tool')->table('real_machine_report', function (Blueprint $table) {
            //
            $table->string('remark', 255)->nullable()->comment('备注');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('tool')->table('real_machine_report', function (Blueprint $table) {
            //
            $table->dropColumn('remark');
        });
    }
}
