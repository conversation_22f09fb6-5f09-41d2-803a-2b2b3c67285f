<?php

namespace App\Service\Apm\Performance;

use App\Model\Apm\StarRocks\MysqlApmReportList;

class ApmScoreBelowEightyReportList extends ApmBase
{
    /**
     * 获取具体设备某一天的数据报告列表
     */
    public function getList(): array
    {
        $selectRaw = <<<EXPRESSION
{$this->mysql_apm_report_list_table}.id AS report_id,
{$this->mysql_apm_report_list_table}.dev_str AS dev_str,
{$this->performance_stat_data_table}.duration AS duration,
{$this->mysql_apm_device_list_table}.dev_brand AS dev_brand,
{$this->mysql_apm_device_list_table}.dev_model AS dev_model,
{$this->mysql_apm_report_list_table}.app_version_name AS game_version_code,
{$this->performance_score_data_table}.all_score AS all_score,
{$this->mysql_apm_report_list_table}.created_at AS created_at,
{$this->mysql_apm_device_list_table}.os_type AS os_type,
{$this->mysql_apm_device_list_table}.device_tier AS device_tier,
{$this->performance_stat_data_table}.sum_fps / {$this->performance_stat_data_table}.num AS avg_fps,
{$this->performance_stat_data_table}.sum_used_memory / {$this->performance_stat_data_table}.num AS avg_used_memory,
{$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time AS stutter
EXPRESSION;


        $this->reportList = MysqlApmReportList::query()
            ->selectRaw($selectRaw)
            ->join($this->mysql_apm_device_list_table, function ($join) {
                $join->on("{$this->mysql_apm_device_list_table}.developer_app_id", '=', "{$this->mysql_apm_report_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_device_list_table}.dev_str", '=', "{$this->mysql_apm_report_list_table}.dev_str");
            })
            ->join($this->performance_stat_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->join($this->performance_score_data_table, "{$this->performance_score_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id", 'left')
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime])
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration())
            ->where("{$this->performance_score_data_table}.all_score", '<', $this->standardScore)
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id'])
            ->when(isset($this->params['os_type']), function ($query) {
                $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) {// 判断是否传入是否模拟器
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) {// 判断是否传入设备挡位
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->orderBy($this->sortField, $this->sortType)
            ->limit(10)
            ->getFromSR();

        //处理数据
        $this->handleListData();

        //返回数据
        return [
            'list' => $this->reportList
        ];
    }
}
