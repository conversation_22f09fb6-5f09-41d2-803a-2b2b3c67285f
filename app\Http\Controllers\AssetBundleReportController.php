<?php

namespace App\Http\Controllers;

use App\Model\Report;
use App\Service\ReportService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AssetBundleReportController extends Controller
{
    private $report;
    private $reportService;

    public function __construct(Report $report, ReportService $reportService)
    {
        $this->report = $report;
        $this->reportService = $reportService;
    }

    /**
     * 检测新增接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2535
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        ini_set('memory_limit', '2048M');
        $input = $request->all();

        $validator = \Validator::make($input, [
            'report' => 'required|file'
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }

        $reportFormat = "json";
        if ($input['report']->getClientOriginalExtension() === $reportFormat) {
            $input['report_url'] = $this->reportService->storeReport($input['report'], $reportFormat);
            try {
                \DB::connection('tool')->beginTransaction();
                // 解析json文件获取平台
                $report = json_decode(file_get_contents(public_path($input['report_url'])), true);
                $input['platform'] = $report['MetaData']['Platform'] ?? '';
                $this->report->store($input, true);
                \DB::connection('tool')->commit();
                return $this->response();
            } catch (Exception $e) {
                \Log::error('检测新增接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                    $e->getLine());
                \DB::connection('tool')->rollBack();
                return $this->response(1005);
            }
        } else {
            return $this->response(1001);
        }

    }

    /**
     * 检测列表接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2533
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'developer_app_id' => 'required|int',
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        try {
            $page = $input['page'] ?? 1;
            $perPage = $input['per_page'] ?? 15;
            $start_created_at = null;
            $end_created_at = null;
            if (isset($input['start_created_at'], $input['end_created_at'])) {
                $start_created_at = $input['start_created_at']." 00:00:00";
                $end_created_at = $input['end_created_at']." 23:59:59";
            }
            [$list, $total] = $this->reportService->getReportList($input['developer_app_id'], $perPage, $page,
                $start_created_at, $end_created_at);
            return $this->response(0, ['list' => $list, 'total' => $total]);
        } catch (Exception $e) {
            \Log::error('AB冗余资源检测列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            return $this->response(1005);
        }

    }

    /**
     * 获取报表地址
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2562
     * @param $id
     * @return JsonResponse
     */
    public function getReport($id): JsonResponse
    {
        try {
            $content = $this->report->findOrFail($id, ['report_url']);
            return $this->response(0, $content);
        } catch (Exception $e) {
            \Log::error('获取AB冗余资源检测报告详情接口报错-reportId:' . $id . ',原因:' . $e->getMessage() . ' in: ' .
                $e->getFile() . ' line: ' . $e->getLine());
            return $this->response(1005);
        }
    }

    /**
     * 报告更新接口
     * @doc http://developer.shiyuegame.com/showdoc/web/#/129?page_id=2574
     * @param Request $request
     * @param $reportId
     * @return JsonResponse
     */
    public function updateReport(Request $request, $reportId): JsonResponse
    {
        $input = $request->all();
        $validator = \Validator::make($input, [
            'remark' => 'present|max:255'
        ]);
        if ($validator->fails()) {
            return $this->response(1000);
        }
        try {
            \DB::connection('tool')->beginTransaction();
            $this->report->where('report_id', $reportId)->update(['remark' => $input['remark']]);
            \DB::connection('tool')->commit();
            return $this->response();
        } catch (Exception $e) {
            \Log::error('保存配置接口报错' . ',原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' .
                $e->getLine());
            \DB::connection('tool')->rollBack();
            return $this->response(1005);
        }
    }
}
